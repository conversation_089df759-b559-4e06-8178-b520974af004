<?php

namespace App\Models\View;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\View\SummaryLog
 *
 * @method static \Illuminate\Database\Eloquent\Builder|SummaryLog newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SummaryLog newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SummaryLog query()
 * @mixin \Eloquent
 */
class SummaryLog extends Model
{
    use HasFactory;

    protected $connection = 'sqlsrv';
    protected $table = 'Summary_All';

    protected $casts = [
        "Total" => 'integer'
    ];
}
