<?php

namespace App\Http\Middleware;

use App\Models\Settings\Nvr;
use App\Traits\AppConfig;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Symfony\Component\HttpFoundation\Response;

class AuthNvrMiddleware
{
    use AppConfig;
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // get detail nvr
        $getNvr = Nvr::select('ip', 'username', 'password', 'name')
            ->where('id', $request->nvr_id)->first();

        $nvrName = str_replace(' ', '_', $getNvr->name);
        $request->attributes->add(['DETAIL_NVR' => [
            'ip'   => $getNvr->ip,
            'name' => $nvrName
        ]]);

        // check login user
        $responseUser = Http::withoutVerifying()
            ->withOptions(['verify' => false])
            ->withHeaders([
                'Content-Type' => 'application/json',
                'Cookie' => 'JSESSIONID=' . $request->session()->get($nvrName),
            ])
            ->get($getNvr->ip . '/users/userid');
        $setReponseUserCode = $responseUser->json();

        // set login
        if ($setReponseUserCode['resultCode'] != 0) {
            $response = Http::withoutVerifying()
                ->withOptions(['verify' => false])
                ->post($getNvr->ip . '/loginInfo/login/v1.0', [
                    'userName' => $getNvr->username,
                    'password' => $getNvr->password,
                ]);
            $setReponseCode = $response->json();

            if ($setReponseCode['resultCode'] == 0) {
                $setCookiesNvr  = $response->cookies()->getCookieByName('JSESSIONID')->getValue();
                $request->session()->put($nvrName, $setCookiesNvr);
                $this->storeConfig('Cookie-' . $nvrName, 'NVR',  $setCookiesNvr);
            }
        } else {
            $request->session()->put($nvrName, $request->session()->get($nvrName));
            $this->storeConfig('Cookie-' . $nvrName, 'NVR',  $request->session()->get($nvrName));
        }

        return $next($request);
    }
}
