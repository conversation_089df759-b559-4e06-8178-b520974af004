<?php

namespace App\Console\Commands;

use App\Models\Settings\Setting;
use Illuminate\Console\Command;

class SettingCommand extends Command
{
    /**
     * Create settings
     *
     * @var string
     */
    protected $signature = 'app:settings {option} {value}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create Settings';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        Setting::setSetting($this->argument('option'), $this->argument('value'));
    }
}
