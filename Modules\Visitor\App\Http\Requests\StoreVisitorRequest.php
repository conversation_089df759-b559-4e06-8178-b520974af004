<?php

namespace Modules\Visitor\App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreVisitorRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'form' => 'required',
            'form.*.persPersonPin'      => 'required',
            'form.*.certType'           => 'required',
            'form.*.certNum'            => 'required',
            'form.*.visEmpName'         => 'required',
            'form.*.visitEmpPhone'      => 'required',
            'form.*.company'            => 'required',
            'form.*.visitReason'        => 'required',
            'form.*.visitorCount'       => 'required',
            'form.*.startTime'          => 'required',
            'form.*.endTime'            => 'required',
            'form.*.cardNo'             => 'required',
            'form.*.accessMapping'      => 'required',
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }
}
