<?php

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Route;
use Vinkla\Hashids\Facades\Hashids;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/', function () {
    return view('welcome');
});

Route::post('/logout', function (Request $request) {
    Auth::logout();
});

Route::get('ascii', function () {
    $inputString = "HRD";

    // Sum the ASCII values of each character in the string
    $numericValue = 0;

    for ($i = 0; $i < strlen($inputString); $i++) {
        $numericValue += ord($inputString[$i]);
    }

    return response()->json([
        'ascii' => $numericValue,
        'hasid' => Hashids::encode($numericValue),
        'string' => $inputString,
        'crc32' => crc32($inputString),
        'hexdec' => hexdec($inputString),
    ]);
});
