<?php

namespace App\Services\Dashboard;

use App\Models\View\ViewVisitor;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class SummaryVisitorService
{
    /**
     * Get visitor report by date range
     *
     * @param string $startDate
     * @param string $endDate
     * @return array
     */
    public function getVisitorReportByDateRange($startDate, $endDate)
    {
        try {
            // Validate date format
            $start = Carbon::createFromFormat('Y-m-d', $startDate);
            $end = Carbon::createFromFormat('Y-m-d', $endDate);

            if ($start->gt($end)) {
                throw new \Exception('Start date cannot be greater than end date');
            }

            $data = ViewVisitor::getVisitorReport($startDate, $endDate);

            Log::info('Visitor report retrieved successfully', [
                'start_date' => $startDate,
                'end_date' => $endDate,
                'total_records' => $data->count()
            ]);

            return [
                'success' => true,
                'data' => $data,
                'total_records' => $data->count(),
                'date_range' => [
                    'start_date' => $startDate,
                    'end_date' => $endDate
                ]
            ];

        } catch (\Exception $e) {
            Log::error('Error retrieving visitor report', [
                'error' => $e->getMessage(),
                'start_date' => $startDate,
                'end_date' => $endDate
            ]);

            return [
                'success' => false,
                'message' => $e->getMessage(),
                'data' => collect([])
            ];
        }
    }

    /**
     * Get current month visitor report
     *
     * @return array
     */
    public function getCurrentMonthReport()
    {
        $startDate = now()->startOfMonth()->format('Y-m-d');
        $endDate = now()->endOfMonth()->format('Y-m-d');

        return $this->getVisitorReportByDateRange($startDate, $endDate);
    }

    /**
     * Get last month visitor report
     *
     * @return array
     */
    public function getLastMonthReport()
    {
        $startDate = now()->subMonth()->startOfMonth()->format('Y-m-d');
        $endDate = now()->subMonth()->endOfMonth()->format('Y-m-d');

        return $this->getVisitorReportByDateRange($startDate, $endDate);
    }

    /**
     * Get visitor report with pagination-like chunking
     *
     * @param string $startDate
     * @param string $endDate
     * @param int $chunkSize
     * @return array
     */
    public function getVisitorReportChunked($startDate, $endDate, $chunkSize = 100)
    {
        try {
            $data = ViewVisitor::getVisitorReport($startDate, $endDate);
            $chunks = $data->chunk($chunkSize);

            return [
                'success' => true,
                'total_records' => $data->count(),
                'total_chunks' => $chunks->count(),
                'chunk_size' => $chunkSize,
                'chunks' => $chunks->toArray()
            ];

        } catch (\Exception $e) {
            Log::error('Error chunking visitor report', [
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => $e->getMessage(),
                'chunks' => []
            ];
        }
    }

    /**
     * Export visitor report data
     *
     * @param string $startDate
     * @param string $endDate
     * @param string $format (json, csv, array)
     * @return mixed
     */
    public function exportVisitorReport($startDate, $endDate, $format = 'json')
    {
        $result = $this->getVisitorReportByDateRange($startDate, $endDate);

        if (!$result['success']) {
            return $result;
        }

        $data = $result['data'];

        switch (strtolower($format)) {
            case 'csv':
                return $this->convertToCsv($data);
            case 'array':
                return $data->toArray();
            case 'json':
            default:
                return $data->toJson();
        }
    }

    /**
     * Convert data to CSV format
     *
     * @param \Illuminate\Support\Collection $data
     * @return string
     */
    private function convertToCsv($data)
    {
        if ($data->isEmpty()) {
            return '';
        }

        $csv = '';
        $headers = array_keys((array) $data->first());
        $csv .= implode(',', $headers) . "\n";

        foreach ($data as $row) {
            $csv .= implode(',', array_values((array) $row)) . "\n";
        }

        return $csv;
    }
}
