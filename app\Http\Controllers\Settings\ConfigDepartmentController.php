<?php

namespace App\Http\Controllers\Settings;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use App\Models\Settings\Department;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Validator;
use App\Services\Settings\DepartmentService;

class ConfigDepartmentController extends Controller
{

    public DepartmentService $service;

    public function __construct(DepartmentService $service)
    {
        $this->service = $service;
        // $this->middleware(['direct_permission:Roles-index'])->only(['index', 'show', 'permissionRole']);
        // $this->middleware(['direct_permission:Roles-store'])->only(['store', 'storePermissionRole']);
        // $this->middleware(['direct_permission:Roles-edits'])->only('update');
        // $this->middleware(['direct_permission:Roles-erase'])->only('destroy');
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            return $this->success('', $this->service->index($request));
        } catch (\Exception $exception) {
            throw new \Exception($exception->getMessage(), 1);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        DB::beginTransaction();
        try {
            
            Department::create($this->service->formData($request, 'store'));
            $this->service->processCreateDept($request);

            DB::commit();

            return $this->success('Data inserted!', [
                'errors' => false,
            ]);
        } catch (\Exception $exception) {
            DB::rollBack();
            throw new \Exception($exception->getMessage(), 1);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show($id): JsonResponse
    {
        $data = Department::where("id", "=", $id)->get();

        return $this->success('Success', [
            'data' => $data
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id): JsonResponse
    {
        DB::beginTransaction();
        try {
            $data = Department::find($id);
            $forms = collect($this->service->formData($request, 'update'));
            //return $this->error('', 422, [$forms]);
            foreach ($forms as $index => $form) {
                $data->$index = $form;
            }
            $data->save();

            // $this->processItemDetails($category, $id);

            DB::commit();

            return $this->success('Data updated!', [
                'errors' => false,
            ]);
        } catch (\Exception $exception) {
            DB::rollBack();

            throw new \Exception($exception->getMessage(), 1);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request, $id): JsonResponse
    {
        try {
            $collect = collect($request->selected);
            $selected = $collect->pluck('id');

            $details = Department::where('id', $id)->first();
            Http::post(config('app.url_zkteco').'/api/department/delete/'.$details->dpt_code.'?access_token='.config('app.token_zkteco'));
            Department::where('id', $id)->delete();

            return $this->success('Row deleted!', [
                'errors' => false,
            ]);
        } catch (\Exception $exception) {
            throw new \Exception($exception->getMessage(), 1);
        }
    }


    public function syncDept(Request $request){
        // set_time_limit(500); 
        $responseCherry = DB::connection('sqlsrvcherry')->select("SELECT 
            'GENERAL' as accType,
            'INTERNAL' as compType,
            right(A.WorkLocation, LEN(A.WorkLocation) - charindex(' ', A.WorkLocation)) as Work,
                A.WorkLocation,
                RIGHT(A.Company, LEN(A.Company) - 3) as Company, 
                A.Department 
            FROM vw_employee_masterdata A
            GROUP BY
                A.WorkLocation,
                A.Company,
                A.Department 
            ORDER BY A.Company, A.Department ASC
            ");

            $responseHris = DB::select("SELECT 
                'INTERNAL' as compType,
                'GENERAL' as accType,
                'MOROWALI' as Work,
                alias as Company
            FROM organizations where alias not in ('IMIP','MSS','BDT','MMM','BDM') AND organization_type_id = '1'
            GROUP by alias
            ORDER BY alias asc
        ");

        // $responseContractor = DB::connection('sqlsrvcontractor')->select("SELECT 
        //     'CONTRACTOR' as compType,
        //     'GENERAL' as accType,
        //     'MOROWALI' as Work,
        //      Company
        // FROM vw_master_company WHERE Status = 1  AND Fullname = 'ANINDYA WIRA KONSULT'
        // ORDER BY Company ASC
        // ");


        foreach($responseCherry as $res){
            $request = array(
                'workLocation'       => $res->Work,
                'company'            => $res->Company,
                'dept'               => $res->Department,
                'accessType'         => $res->accType,
                'compType'           => $res->compType,
                'organization_group' => 1
            );
            
            $this->service->processSyncDept($request);
        }

        foreach($responseHris as $res){
            $request = array(
                'workLocation'       => $res->Work,
                'company'            => $res->Company,
                'dept'               => NULL,
                'accessType'         => $res->accType,
                'compType'           => $res->compType,
                'organization_group' => 2
            );
            
            $this->service->processSyncDept($request);
        }

        // foreach($responseContractor as $res){
        //     $request = array(
        //         'workLocation'       => $res->Work,
        //         'company'            => $res->Company,
        //         'dept'               => NULL,
        //         'accessType'         => $res->accType,
        //         'compType'           => $res->compType,
        //         'organization_group' => 2
        //     );
            
        //     $this->service->processSyncDept($request);
        // }

        return response()->json([
            'status'  => true,
            'message' => 'Sync Success'
        ], 200);
    }
}
