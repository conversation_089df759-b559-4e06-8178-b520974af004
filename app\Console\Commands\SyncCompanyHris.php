<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\Settings\Organization;

class SyncCompanyHris extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-company-hris';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync Company HRIS';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        Log::info('Start Sync Company : '.date('Y-m-d H:i:s'));

        $organizationAliases = Organization::where('organization_type_id', 1)->pluck('alias')->toArray();

        $excludedAliases = array_merge(['IMIP', 'MSS', 'BDT', 'MMM', 'BMS'], $organizationAliases);
        
        $responseHris = DB::connection('mysqlhris')
            ->table('vw_company_active')
            ->selectRaw("
                'INTERNAL' as compType,
                'GENERAL' as accType,
                'MOROWALI' as Work,
                alias as Company,
                'group',
                name as Fullname
            ")
            ->whereNotIn('alias', $excludedAliases)
            ->groupBy('alias')
            ->orderBy('alias', 'ASC')
            ->get();

       
        foreach($responseHris as $res){

            if($res->group == 'TSINGSHAN'){
                $Group = '5';
            }else{
                $Group = '7';
            }
            $request = array(
                'alias'                => $res->Company,
                'name'                 => $res->Fullname,
                'status'               => 1,
                'organization_type_id' => 1,
                'created_by'           => null,
                'organization_group'   => 2,
                'group_id'             => $Group,
                'data_source'          => 'ftp',
            );
            
            Organization::create($request);
        }

        Log::info('Done Sync Company : '.date('Y-m-d H:i:s'));
    }
}
