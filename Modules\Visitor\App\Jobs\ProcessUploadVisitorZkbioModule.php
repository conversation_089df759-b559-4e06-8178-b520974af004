<?php

namespace Modules\Visitor\App\Jobs;

use App\Models\Applications;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use App\Services\ProcessDataVisitorService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class ProcessUploadVisitorZkbioModule implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use Batchable;

    public Collection $row;
    public string $accLevelIds;
    public $batchId;
    public Applications $application;

    /**
     * Create a new job instance.
     */
    public function __construct($row, $batchId, $application)
    {
        $this->row = $row;
        $this->batchId = $batchId;
        $this->application = $application;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {

        try {
            // Proses job
            Log::info('Processing job for batch ID: ' . $this->batchId);
        } catch (\Exception $e) {
            Log::error('Job failed with error: ' . $e->getMessage());
        }
        
        //service process 1
        $rows = $this->row;

        $service = new ProcessDataVisitorService();

        $service->insertDataVisitor($rows, batch_id: $this->batchId, applications: $this->application);
    }
}
