<?php

namespace App\Models\Settings;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Settings\FaceListOrganization
 *
 * @property int $id
 * @property int $face_list_id
 * @property int $organization_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Settings\Organization|null $organization
 * @method static \Illuminate\Database\Eloquent\Builder|FaceListOrganization newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|FaceListOrganization newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|FaceListOrganization query()
 * @method static \Illuminate\Database\Eloquent\Builder|FaceListOrganization whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FaceListOrganization whereFaceListId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FaceListOrganization whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FaceListOrganization whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FaceListOrganization whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class FaceListOrganization extends Model
{
    use HasFactory;
    protected $guarded = [];

    public function organization()
    {
        return $this->belongsTo(Organization::class, 'organization_id', 'id');
    }
}
