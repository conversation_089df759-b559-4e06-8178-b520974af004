<?php

namespace Modules\Visitor\App\Http\Middleware;

use App\Models\Applications;
use App\Traits\ApiResponse;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class VisitorMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next)
    {
        $application                        = Applications::where('token', $request->bearerToken())
            ->where('status', '1')
            ->first();

        if (!$application) {
            return $this->error('Token credentials not match!', [], '422');
        }
        return $next($request);
    }
}
