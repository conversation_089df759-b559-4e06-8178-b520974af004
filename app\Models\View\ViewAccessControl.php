<?php

namespace App\Models\View;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\View\ViewAccessControl
 *
 * @method static \Illuminate\Database\Eloquent\Builder|ViewAccessControl newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ViewAccessControl newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ViewAccessControl query()
 * @mixin \Eloquent
 */
class ViewAccessControl extends Model
{
    use HasFactory;

    protected $connection = 'mysqlhris';
    protected $table = 'vw_access_control';
}
