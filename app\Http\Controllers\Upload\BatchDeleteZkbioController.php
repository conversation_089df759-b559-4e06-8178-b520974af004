<?php
namespace App\Http\Controllers\Upload;

use App\Http\Controllers\Controller;
use App\Jobs\Upload\ProcessDeleteZkbio;
use App\Services\Upload\BatchDeleteZkbioService;
use App\Models\Applications;
use Modules\ExternalApi\app\Services\ProcessDeletePersonService;
use Modules\ExternalApi\app\Services\ProcessMutationPersonService;
use App\Jobs\Service\TransformDataMutasiService;
use Modules\ExternalApi\app\Services\ProcessUpdateExternalApiService;
use App\Jobs\Upload\ProcessMutasiPerson;
use Illuminate\Database\Eloquent\Builder;
use App\Models\Settings\AccessLevel;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;
use App\Imports\FaceRecordImport;
use Illuminate\Database\Eloquent\Collection;
use App\Models\Settings\Organization;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Bus;
use App\Models\Settings\Nvr;
use Throwable;
use ZipArchive;
use Illuminate\Support\Str;
use DB;


class BatchDeleteZkbioController extends Controller
{

    public function index(Request $request)
    {
        $service                            = new BatchDeleteZkbioService();
        try {
            return $this->success('', $service->index($request));
        } catch (\Exception $exception) {
            throw new \Exception($exception->getMessage(), 1);
        }
    }

    public function store(Request $request)
    {
        $files = $request->file("files");
        $collection = Excel::toArray(new FaceRecordImport, $files);
        $actionType = $request->actionType;
        
        $status = $request->status;
        $user_id = auth()->user()->id;
        $zkbioUpload = [];
        $zkbioMutasi = [];
        $huaweiDelete = [];
        $dataRow = 0;
        $application = Applications::where('name', 'Middleware')
                ->where('status', '1')
                ->first();
        
        try{
            foreach ($collection[0] as $index => $row) {
                if ($index != 0) {
                    if($row[5] == '0000-00-00' || $row[5] == '00/00/0000' || $row[5] == '0000/00/00'){
                        $row[5] = '1970-01-01';
                    }
                    /* Emp Data */
                    $org = Organization::where('name', $row[9])->first();
                    if($org != NULL){
                        $org_group = $org->organization_group;
                        $org_type = $org->organization_type;
                        $org_alias = $org->alias;
                    }

                    $statusEmployee = 0;
                    $leaveDate = TRUE;
                    $is_leave = FALSE;
                    if($row[12] == 4){/* Blacklist */
                        $statusEmployee = 2;
                        $is_leave = TRUE;
                        $leaveDate = $row[13];
                    }else if($row[12] == 1){/* Aktif */
                        $statusEmployee = 3;
                    }
                    /* Delete Data */
                    if($statusEmployee != 3){
                        $zkbioUpload[] = [
                            'photoName' => $row[0],
                            'name' => preg_replace('/[^A-Za-z0-9 !@#$%^&*().]/u','', strip_tags($row[1])),
                            'credentialNumber' => $row[2],
                            'strId' => $row[2],
                            'credentialType' => ($row[3] == 'ID Card') ? 0 : 1,
                            'gender' => ($row[4] == 'MALE') ? 'M' : 'F',
                            'bornTime' => date("Y-m-d", strtotime($row[5])),
                            'country' => $row[6],
                            'occupation' => $row[7],
                            'description' => $row[8],
                            'company' => $row[9],
                            'work_location' => $row[10],
                            "work_location" => $row[10],
                            'dept_name' => $row[11],
                            'org_group' => $org_group,
                            'org_type' => $org_type,
                            'org_alias' => $org_alias,
                            "statusEmployee" => $statusEmployee,
                            "leaveDate" => $leaveDate,
                            "is_leave" => $is_leave,
                            "application_id" => $application->id
                        ];

                        $huaweiDelete[] = [
                            "id" => $row[2],
                            "photo" => $row[0],
                            'photoName' => $row[0],
                            'occupation' => $row[7],
                            'strId' => $row[2],
                            "name" => preg_replace('/[^A-Za-z0-9 !@#$%^&*().]/u','', strip_tags($row[1])),
                            "identifyNumber" => $row[2],
                            'credentialNumber' => $row[2],
                            'credentialType' => ($row[3] == 'ID Card') ? 0 : 1,
                            'bornTime' => date("Y-m-d", strtotime($row[5])),
                            'country' => $row[6],
                            "oldCredentialNumber" => (isset($row['oldIdentifyNumber'])) ? $item['oldIdentifyNumber'] : '',
                            "type" => "ID Card",
                            "gender" => ($row[4] == 'MALE') ? 'M' : 'F',
                            "birthDate" => date("Y-m-d", strtotime($row[5])),
                            "nationality" => $row[6],
                            "employeeType" => "Karyawan",
                            'org_group' => $org_group,
                            'org_type' => $org_type,
                            "description" => $row[8],
                            "company" => $row[9],
                            "workLocation" => $row[10],
                            "work_location" => $row[10],
                            "departmentName" => $row[11],
                            'dept_name' => $row[11],
                            "statusEmployee" => $statusEmployee,
                            "leaveDate" => $leaveDate,
                            "is_leave" => $is_leave,
                            'oldCompany' => (isset($row['oldCompany'])) ? $item['oldCompany'] : '',
                            "application_id" => $application->id,
                            "department" => $row[11],
                        ];
                        $dataRow++;
                    }else if($statusEmployee == 3){
                    /* Mutasi Data */
                        $zkbioMutasi[] = [
                            "form" => array(
                                "id" => $row[2],
                                "photo" => $row[0],
                                'photoName' => $row[0],
                                'occupation' => $row[7],
                                'strId' => $row[2],
                                "name" => preg_replace('/[^A-Za-z0-9 !@#$%^&*().]/u','', strip_tags($row[1])),
                                "identifyNumber" => $row[2],
                                'credentialNumber' => strval($row[2]),
                                'credentialType' => ($row[3] == 'ID Card') ? 0 : 1,
                                'bornTime' => date("Y-m-d", strtotime($row[5])),
                                'country' => $row[6],
                                "oldCredentialNumber" => (isset($row['oldIdentifyNumber'])) ? $item['oldIdentifyNumber'] : '',
                                "type" => "ID Card",
                                "gender" => ($row[4] == 'MALE') ? 'M' : 'F',
                                "birthDate" => date("Y-m-d", strtotime($row[5])),
                                "nationality" => $row[6],
                                "employeeType" => "Karyawan",
                                'org_group' => $org_group,
                                'org_type' => $org_type,
                                "description" => $row[8],
                                "company" => $row[9],
                                "workLocation" => $row[10],
                                "departmentName" => $row[11],
                                'dept_name' => $row[11],
                                "statusEmployee" => $statusEmployee,
                                "leaveDate" => $leaveDate,
                                "is_leave" => $is_leave,
                                "oldCompany" => $row[14],
                                "application_id" => $application->id,
                                "department" => $row[11],
                                "access_type" => "GENERAL",
                                "work_location" => $row[10],
                            )
                        ];
                        $dataRow++;
                    }
                }
            }

            if ($dataRow <= 0) {
                return $this->error('data must greater than 0');
            }

            /* Delete Data Zkbio */
            if (count($zkbioUpload) >= 1) {
                $collection                     = collect($zkbioUpload);
                $chunks                         = $collection->chunk(100);
                $batchQueue                     = [];
                $batch                          = Bus::batch([])
                    ->catch(function (Batch $batch, Throwable $e) {
                        Log::info('error upload batch ' . now(), [
                            'error'             => $e->getTraceAsString(),
                        ]);
                    })
                    ->name('Upload Zkbio '. date('Y-m-d H:i:s'))
                    // ->onQueue('ZkbioDeleteApiPrd')
                    ->onQueue('ZkbioDevApiPatar')
                    ->allowFailures()
                    ->dispatch();
                foreach ($chunks as $key => $chunk) {
                    $batch->add(new ProcessDeleteZkbio($chunk, $user_id));
                }
            }
            
            /* Delete Data Huawei */
            if (count($huaweiDelete) >= 1) {
                $serviceHuawei = new ProcessDeletePersonService();
                $serviceHuawei->processQueueHuawei($huaweiDelete, $application);
            }

            /* Mutasi Data */
            if (count($zkbioMutasi) >= 1) {
                $batch   = Bus::batch([])
                ->catch(function (Batch $batch, Throwable $e) {
                    Log::info('error upload batch ' . now(), [
                        'error'             => $e->getTraceAsString(),
                    ]);
                })
                ->name('Edit Zkbio '. date('Y-m-d H:i:s'))
                ->onQueue('ZkbioDevApiPatarMutasi')
                // ->onQueue('ZkbioPrdApiMutasi')
                ->allowFailures()
                ->dispatch();
                $batch->add(new ProcessMutasiPerson(dataMutasi: $zkbioMutasi, application: $application));
                
                return response()->json([
                    'message' => 'Data Persons is currently being processed in the mutasi queue',
                    'status' => 'processing',
                ]);
            }

            return response()->json([
                'status'                    => true,
                'message'                   => 'Success',
            ], 200);
        
        }catch(\Exception $e){
            return $this->success('Upload failed', [
                'batch'                     => $e->getMessage()
            ]);
        }
    }

    public function destroy(Request $request)
    {
        
    }

    
}
