<?php

namespace App\Models\Settings;

use App\Models\Mapping\MappingAccess;
use App\Models\Settings\Organization;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Settings\AccessLevelOrganization;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * App\Models\Settings\AccessLevel
 *
 * @property int $id
 * @property string $access_level_name
 * @property string $access_level_code
 * @property int $status
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string $access_level_id
 * @property-read Organization|null $organization
 * @property-read \Illuminate\Database\Eloquent\Collection<int, MappingAccess> $mappingAccess
 * @property-read int|null $mapping_access_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, AccessLevelOrganization> $organizationAccessLevel
 * @property-read int|null $organization_access_level_count
 * @method static \Illuminate\Database\Eloquent\Builder|AccessLevel newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AccessLevel newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AccessLevel onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|AccessLevel query()
 * @method static \Illuminate\Database\Eloquent\Builder|AccessLevel whereAccessLevelCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccessLevel whereAccessLevelId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccessLevel whereAccessLevelName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccessLevel whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccessLevel whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccessLevel whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccessLevel whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccessLevel whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccessLevel whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccessLevel whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccessLevel withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|AccessLevel withoutTrashed()
 * @property string $department_name
 * @property int $access_type_id
 * @property-read \App\Models\Settings\AccessType|null $accessType
 * @method static \Illuminate\Database\Eloquent\Builder|AccessLevel whereAccessTypeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccessLevel whereDepartmentName($value)
 * @property int|null $access_id
 * @property string|null $access_name
 * @property-read mixed $organization_ids
 * @method static \Illuminate\Database\Eloquent\Builder|AccessLevel whereAccessId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccessLevel whereAccessName($value)
 * @mixin \Eloquent
 */
class AccessLevel extends Model
{
    use HasFactory;
    use SoftDeletes;
    protected $guarded = [];
    public $appends = [
        'organization',
        'organization_ids'
    ];

    public function getOrganizationAttribute()
    {
        return $this->organizationAccessLevel()->get()->pluck('organization.alias')->implode(', ');
    }

    public function getOrganizationIdsAttribute()
    {
        return $this->organizationAccessLevel()->get()->pluck('organization_id')->implode(',');
    }

    public function mappingAccess()
    {
        return $this->hasMany(MappingAccess::class);
    }

    public function organization()
    {
        return $this->belongsTo(Organization::class);
    }

    public function organizationAccessLevel()
    {
        return $this->hasMany(AccessLevelOrganization::class, 'access_level_id', 'id');
    }

    public function accessType()
    {
        return $this->belongsTo(AccessType::class);
    }
}
