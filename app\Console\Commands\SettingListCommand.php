<?php

namespace App\Console\Commands;

use App\Models\Settings\Setting;
use Illuminate\Console\Command;

class SettingListCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:setting-list';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $results = Setting::select('option', 'value')->get();
        $this->info('Settings List:');
        $this->table(['Option', 'Value'], $results->toArray());
    }
}
