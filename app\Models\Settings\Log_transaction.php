<?php

namespace App\Models\Settings;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Settings\Log_transaction
 *
 * @property int $id
 * @property string $credential_number
 * @property string $credential_type
 * @property string $name
 * @property string $company
 * @property string|null $type
 * @property int|null $nvr_id
 * @property int|null $face_list_id
 * @property int $status
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property string|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int|null $application_id
 * @property string|null $access_level_code
 * @property string|null $service_code
 * @property array|null $service_payload
 * @property array|null $service_message
 * @property-read mixed $label
 * @property-read mixed $value
 * @method static \Illuminate\Database\Eloquent\Builder|Log_transaction newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Log_transaction newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Log_transaction query()
 * @method static \Illuminate\Database\Eloquent\Builder|Log_transaction whereAccessLevelCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Log_transaction whereApplicationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Log_transaction whereCompany($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Log_transaction whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Log_transaction whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Log_transaction whereCredentialNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Log_transaction whereCredentialType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Log_transaction whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Log_transaction whereFaceListId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Log_transaction whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Log_transaction whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Log_transaction whereNvrId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Log_transaction whereServiceCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Log_transaction whereServiceMessage($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Log_transaction whereServicePayload($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Log_transaction whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Log_transaction whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Log_transaction whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Log_transaction whereUpdatedBy($value)
 * @property string|null $batch_id
 * @method static \Illuminate\Database\Eloquent\Builder|Log_transaction whereBatchId($value)
 * @property string|null $access_level_name
 * @method static \Illuminate\Database\Eloquent\Builder|Log_transaction whereAccessLevelName($value)
 * @property string|null $log_type
 * @property string|null $method
 * @method static \Illuminate\Database\Eloquent\Builder|Log_transaction whereLogType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Log_transaction whereMethod($value)
 * @mixin \Eloquent
 */
class Log_transaction extends Model
{
    use HasFactory;

    protected $guarded = [];
    protected $table = 'log_transaction';

    public $appends = [
        'label',
        'value'
    ];

    protected $casts = [
        'service_message' => 'array',
        'service_payload' => 'array'
    ];

    public function getLabelAttribute()
    {
        return $this->name;
    }

    public function getValueAttribute()
    {
        return $this->id;
    }
}
