<?php

namespace App\Models\Settings;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\Settings\OrganizationType
 *
 * @property int $id
 * @property string $name
 * @property string|null $description
 * @property int $status
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read mixed $status_name
 * @method static \Illuminate\Database\Eloquent\Builder|OrganizationType newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|OrganizationType newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|OrganizationType onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|OrganizationType query()
 * @method static \Illuminate\Database\Eloquent\Builder|OrganizationType whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrganizationType whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrganizationType whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrganizationType whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrganizationType whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrganizationType whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrganizationType whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrganizationType whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrganizationType whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrganizationType withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|OrganizationType withoutTrashed()
 * @mixin \Eloquent
 */
class OrganizationType extends Model
{
    use HasFactory;
    use SoftDeletes;
    protected $guarded = [];

    protected $appends = ['status_name'];

    public function getStatusNameAttribute()
    {
        return ($this->status == 1) ? 'Active' : 'Not Active';
    }
}
