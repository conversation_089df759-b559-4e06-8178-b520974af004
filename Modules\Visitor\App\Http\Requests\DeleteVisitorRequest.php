<?php

namespace Modules\Visitor\App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class DeleteVisitorRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'form' => 'required',
            'form.*.identifyNumber'     => 'required',
            // 'form.*.persPersonPin'      => 'required',
            'accessType'                => 'required',
            'service'                   => 'required',
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }
}
