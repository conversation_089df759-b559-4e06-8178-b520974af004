<?php

namespace App\Models\View;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\View\DetailWithoutCompany
 *
 * @method static \Illuminate\Database\Eloquent\Builder|DetailWithoutCompany newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DetailWithoutCompany newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DetailWithoutCompany query()
 * @mixin \Eloquent
 */
class ViewMasterContractor extends Model
{
    use HasFactory;

    protected $connection = 'sqlsrvcontractor';
    protected $table = 'vw_contractor_zkbio';
}
