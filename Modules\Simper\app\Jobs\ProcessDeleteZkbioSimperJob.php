<?php

namespace Modules\Simper\app\Jobs;

use App\Services\ProcessDataPersonService;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Modules\Simper\app\Services\ProcessDataSimperService;

class ProcessDeleteZkbioSimperJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use Batchable;

    /**
     * Create a new job instance.
     */
    public function __construct(public array $rows)
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $Services = new ProcessDataPersonService();
        foreach ($this->rows as $key => $item) {
            $Services->processDeletePersonZkbio($item, $this->batch()->id);
        }
    }
}
