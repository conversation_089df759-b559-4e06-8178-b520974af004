APP_NAME="Access Control"
APP_ENV=local
APP_KEY=base64:QqU0pqKuIa0I8GPiycVjs6+FvgTswaWhU+k2aPv84Vs=
APP_DEBUG=true
APP_URL=https://accesscontrollapi.imip.co.id

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=sqlsrv
DB_HOST=**************
DB_PORT=1433
DB_DATABASE=AC_PRD
DB_USERNAME=AC_PRD
DB_PASSWORD=Imip@2023


#DB_CONNECTION_HRIS=mysql
#DB_HOST_HRIS=***************
#DB_PORT_HRIS=3306
#DB_DATABASE_HRIS=db_hris
#DB_USERNAME_HRIS=u_access_control
#DB_PASSWORD_HRIS=Y2zXcuZpqznRveO

DB_CONNECTION_CHERRY=sqlsrv
DB_HOST_CHERRY=***************
DB_PORT_CHERRY=1433
DB_DATABASE_CHERRY=cherry_production
DB_USERNAME_CHERRY=ck_admin
DB_PASSWORD_CHERRY='ck_admin!@#'

DB_CONNECTION_HRIS=mysql
DB_HOST_HRIS=***************
DB_PORT_HRIS=3306
DB_DATABASE_HRIS=db_hris
DB_USERNAME_HRIS=u_access_control
DB_PASSWORD_HRIS=Y2zXcuZpqznRveO

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database
QUEUE_WORKER_TIMEOUT=120
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=***************
MAIL_PORT=587
MAIL_USERNAME="<EMAIL>"
MAIL_PASSWORD="0kE$ds43Xpan!049"
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

FTP_HOST=***************
FTP_USERNAME=u_acc_control
FTP_PASSWORD=@imipappdev

SFTP_HOST_HRD=**********
SFTP_USERNAME_HRD=yoga
SFTP_PASSWORD_HRD=yoga123

SFTP_HOST_ECON=**********
SFTP_USERNAME_ECON=useraccgate
SFTP_PASSWORD_ECON=B@hodop!2025

VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

TELEGRAM_LOGGER_BOT_TOKEN=**********************************************
TELEGRAM_LOGGER_CHAT_ID=-4019363969

# Use a minimal log template
TELEGRAM_LOGGER_TEMPLATE="laravel-telegram-logging::minimal"

# Or use the backward compatible one (default setting used even without inserting this row)
TELEGRAM_LOGGER_TEMPLATE="laravel-telegram-logging::standard"

ZKTECO_URL="http://**************:8098"
ZKTECO_TOKEN="6B0EEB2884FAC222E8AA11D6BCD956C2C87C1A957545A40D881117116A1E6D88"

NGINX_PORT=4004
PRODUCTION="1"
PHP_OPCACHE_ENABLE=1
