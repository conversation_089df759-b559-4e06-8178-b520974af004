<?php

namespace App\Services\Settings;

use App\Models\Settings\FaceList;
use App\Models\Settings\FaceListOrganization;
use App\Models\Settings\Nvr;
use App\Traits\ApiResponse;
use App\Traits\AppConfig;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;

class FaceListService
{
    use ApiResponse;
    use AppConfig;

    /**
     * @param $request
     *
     * @return array
     */
    public function index($request): array
    {
        $options = json_decode($request->options, true);
        $pages = isset($options['page']) ? (int)($options['page'] + 1) : 1;
        $row_data = isset($options['rows']) ? (int)$options['rows'] : 200;
        $sorts = isset($options['sortField']) ? (string)$options['sortField'] : "nvr_name";
        $order = (!empty($options['sortOrder'])) ? 'desc' : 'asc';
        $search = isset($request->search) ? (string)$request->search : "";
        $filters = json_decode(request()->input('filters'), true);

        $offset = ($pages - 1) * $row_data;

        $filter = json_decode($request->get('filters'));

        $result = array();
        
       
        $query = FaceList::when($filter, function ($query, $filter) {
            foreach ($filter as $item) {
                if ($item->id === 'company') {
                    $query->where('company', 'LIKE', '%' . $item->value . '%');
                }
                if ($item->id === 'face_list_name') {
                    $query->where('face_list_name', 'LIKE', '%' . $item->value . '%');
                }
                if ($item->id === 'name') {
                    $query->where('name', 'LIKE', '%' . $item->value . '%');
                }
            }
        })->with(['nvr', 'organizationFaceList.organization']);
        

        $result["total"] = $query->count();

        $result['data'] = $query
            ->orderBy($sorts, $order)
            ->skip($request->get('start'))
            ->take($request->get('size'))
            ->limit(20)
            ->get()
            ->map(function ($item) {
                $item->organization_id = array_map('intval', explode(",", $item->organization_ids));
                return $item;
            });
            ;

        $result['form'] = $this->getForm();

        $result['header'] = $this->frontEndHeader();

        $collect = collect($result);

        return $collect->all();
    }

    /**
     * @return array
     */
    public function getForm(): array
    {
        $form = $this->form('roles');
        $form['id'] = 0;

        return $form;
    }

    /**
     * @param $request
     * @param $type
     *
     * @return array
     */
    public function formData($request, $type): array
    {
        $data = $request->all();

        Arr::forget($data, 'id');
        Arr::forget($data, 'created_at');
        Arr::forget($data, 'updated_at');
        Arr::forget($data, 'organization_id');
        Arr::forget($data, 'ACTIONS');

        if ($type == 'store') {
            $nvr = Nvr::find($data['nvr_id'])->first();
            $data['created_by'] = $request->user()->id;
            $data['face_list_code'] = $this->generateFaceListCode();
            $data['nvr_name'] = $nvr->name;
        }

        foreach ($data as $key => $value) {
            if (strpos($key, '/api/') === 0) {
                unset($data[$key]);
            }
        }

        return $data;
    }

    public function generateFaceListCode()
    {
        $face_list = FaceList::orderBy('id', 'desc')->first();
        if ($face_list) {
            return ++$face_list->face_list_code;
        }
        return 'FL000001';
    }

    public function processItemDetails($organizations, $face_list_id)
    {   
        foreach ($organizations as $value) {
            FaceListOrganization::create([
                'face_list_id' => $face_list_id,
                'organization_id' => $value
            ]);
        }
    }

    public function processItemUpdateDetails($organizations, $face_list_id)
    {   
        
        $detail = FaceListOrganization::where("face_list_id", $face_list_id)
        ->where("organization_id", $organizations)
        ->first();
         
        if (!$detail) {
            FaceListOrganization::create([
                'face_list_id' => $face_list_id,
                'organization_id' => $organizations
            ]);
        }
    }

    public function update($id, $data)
    {
        return DeviceConfig::where('id', $id)->update($data);
    }

    public function getFaceListCode($nvrId, $faceListName)
    {
        $faceLists = $this->getFaceListNvr($nvrId);

        // throw new \Exception(json_encode($faceLists));

        foreach ($faceLists as $faceList) {
            if ($faceList['label'] == $faceListName) {
                return $faceList['value'];
            }
        }
    }

    public function getFaceListNvr($nvrId)
    {
        $nvr = Nvr::where('id', $nvrId)->first();

        $response = Http::withoutVerifying()
            ->withOptions(['verify' => false])
            ->withHeaders([
                'Content-Type' => 'application/json',
                'Cookie' => 'JSESSIONID=' . $this->getConfig('Cookie-' . $nvr->name, 'NVR'),
            ])
            ->get($nvr->ip . '/sdk_service/rest/facerepositories', [
                'no'        => 1,
                'size'      => 32,
                'sort'      => 'desc',
                'ordername' => 'name',
            ]);

        $data     = array();
        $faceList = json_decode($response->body(), true);

        throw new \Exception(json_encode($faceList));
        throw new \Exception(json_encode($nvrId));
        if (isset($faceList['repositories'])) {
            foreach ($faceList['repositories'] as $fl) {
                $data[] = array(
                    'value' => strval($fl['id']),
                    'label' => $fl['name'],
                );
            }
        }
        return $data;
    }

    public function frontEndHeader()
    {
        return [
            [
                "accessorKey" => 'nvr.name',
                "header" => 'NVR Name',
            ],
            [
                "accessorKey" => 'nvr.ip',
                "header" => 'NVR IP',
            ],
            [
                "accessorKey" => 'face_list_name',
                "header" => 'Face List Name',
            ],
            // [
            //     "accessorKey" => 'face_list_code',
            //     "header" => 'Face List Code',
            // ],
            [
                "accessorKey" => 'organization',
                "header" => 'Organization',
            ],
            [
                "accessorKey" => 'status',
                "header" => 'Status',
            ],
        ];
    }
}
