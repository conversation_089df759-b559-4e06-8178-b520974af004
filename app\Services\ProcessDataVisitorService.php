<?php

namespace App\Services;

use Carbon\Carbon;
use App\Models\Applications;
use App\Models\Settings\Nvr;
use App\Helpers\DepartmentCode;
use App\Traits\NvrCookieHelper;
use App\Models\Settings\Persons;
use App\Models\Settings\Setting;
use App\Models\Settings\FaceList;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use App\Models\Settings\AccessLevel;
use Illuminate\Support\Facades\Http;
use App\Models\Settings\Organization;
use Intervention\Image\Facades\Image;
use Illuminate\Support\Facades\Storage;
use App\Models\Settings\Log_transaction;
use App\Services\Upload\BatchUploadService;

class ProcessDataVisitorService
{
    use NvrCookieHelper;

    /**
     * @param int $nvr_id
     * @param string $company
     * @return mixed
     */
    /* Zkbio Services */
    protected function createLogAccessLevel(array $employee, $upload, $params, bool $submitted = true, string $message = '', $batch_id, $code = ''): void
    {
        if (!$submitted) {
            $code                           = '';
        } else {
            $uploadCollection = $upload->collect();
            $code = $uploadCollection->has('code') ? $uploadCollection['code'] : (
                $uploadCollection->has('ret') ? $uploadCollection['ret'] : -1
            );
            $message = $uploadCollection->has('message') ? $uploadCollection['message'] : 'No message provided';
        }

        Log_transaction::create([
            'credential_number' => $employee['certNum'],
            'credential_type'   => 1,
            'name'              => $employee['visEmpName'],
            'company'           => $employee['company'],
            'type'              => 'ZKBIO',
            'nvr_id'            => null,
            'face_list_id'      => null,
            'access_level_code' => (isset($params['visLevels'])) ? $params['visLevels'] : '-',
            'status'            => 1,
            'application_id'    => $employee['application_id'],
            'service_code'      => $code,
            'service_message'   => $message,
            'service_payload'   => "",
            'batch_id'          => $batch_id,
            'access_level_name' =>(isset($params['access_level_name'])) ? $params['access_level_name'] : '-' ,
            'log_type'          => 'VISITOR',
        ]);

    }

    public function getDepartmentCode(array $row, string $accessType = 'GENERAL'): array
    {
        $org = Organization::where('name', $row['company'])->first();
        $org_group = $org->organization_group;
        $org_type = $org->organization_type;

        $data_emp = array(
            'workLocation' => 'MOROWALI',
            'company' => $org->alias,
            'dept' => $row['dept_name'],
            'organization_group' => $org_group,
            'compType' => $org_type,
            'accessType' => $accessType,
        );

        $departmentCode = new DepartmentCode();
        $deptCode = $departmentCode->deptCodeVehicle($data_emp, 3);
        return [
            'deptCode' => $deptCode['deptCode'],
            'deptName' => $deptCode['deptName'],
        ];
    }

    public function insertDataVisitor(Collection $rows, string $batch_id, Applications $applications): void
    {
        $start                          = Carbon::now()->format('Y-m-d H:i:s');

        Log::info('start upload Visitor ZKBIO, time: ' . $start);

        
        $data_level = [];
        foreach ($rows as $row) {
     
            $access = AccessLevel::where('access_level_id', $row['accessMapping'])->first();

            if (isset($row['photoName'])) {
                $base64 =  $row['photoName'];
            }else{
                $base64 = '';
            }
            
            $accessLevelCode = $access->access_level_code;
            $accessLevelName = $access->access_level_name;

            $accessToken                = Setting::getSetting('AccessTokenZkbio');
            $pid                        = $row['persPersonPin'];
            $cardNumber                 = $row['certNum'];
            $certType                   = 1;
            $company                    = $row['company'];
            $visitEmpPhone              = $row['visitEmpPhone'];
            $visitReason                = $row['visitReason'];
            $visitorCount               = intval($row['visitorCount']);
            $cardNo                     = $row['cardNo'];
            $acc_level_name             = $accessLevelName;
            $acc_level_code             = $accessLevelCode;
            $startTime                  = $row['startTime'];
            $endTime                    = $row['endTime'];
            $data_name                  = $row['visEmpName'];
            $departmentName             = $row['departmentName'];

            $code_depart                = '';
            $name_depart                = $row['departmentName'];

            $this->processUploadVisitor($accessToken, $pid, $certType, $cardNumber, $company, $visitEmpPhone, $name_depart, $code_depart,  $data_name, $cardNo, $startTime, $endTime, $visitReason, $visitorCount, $row, $batch_id,$acc_level_name,$acc_level_code,$base64);
        }


        Log::info('Upload Visitor ZKBIO Done ! Time : ' . Carbon::now()->diffForHumans($start));
    }


    protected function processUploadVisitor(string $accessToken, string $pid, int $certType, string $cardNumber, string $company, string $visitEmpPhone, string $name_depart, string  $code_depart, string $data_name, $cardNo, string $startTime, string $endTime, string $visitReason, int $visitorCount, array $row, string $batch_id, string $acc_level_name, string $acc_level_code, string $base64): void
    {
        $params                         = [
            'persPersonPin'     => $pid,
            'certType'          => $certType,
            'certNum'           => $cardNumber,
            'visEmpName'        => $data_name,
            'visitEmpPhone'     => $visitEmpPhone,
            'company'           => $company,
            'visitReason'       => $visitReason,
            'visitorCount'      => $visitorCount,
            'startTime'         => $startTime,
            'endTime'           => $endTime,
            'cardNo'            => $cardNo,
            'departmentName'    => $name_depart,
            'visLevels'         => $acc_level_code,
            'access_level_name' => $acc_level_name,
            'facePhoto'         => $base64,
        ];
        

        $UrlVisitorAdd = Setting::getSetting('UrlVisitorAdd');
        $upload = Http::withoutVerifying()
            ->withOptions(['verify' => false])
            ->withHeaders([
                'Content-Type' => 'application/json',
            ])
            ->post($UrlVisitorAdd . '?access_token=' . $accessToken, $params);

        Log::info('Result from upload access level zkbio', [
            'json' => $upload->json(),
            'url' => $UrlVisitorAdd
        ]);
        $this->createLogAccessLevel(employee: $row, upload: $upload, params: $params, batch_id: $batch_id, submitted: true, message: '', code : '');
   }

    public function deleteDataVisitor(Collection $rows, string $batch_id, Applications $applications): void
    {
        $start                          = Carbon::now()->format('Y-m-d H:i:s');

        Log::info('start delete Visitor ZKBIO, time: ' . $start);

        
        $data_level = [];
        foreach ($rows as $row) {
            Log::info('request', [
                'Params row visitor' => $row
            ]);
            $accessToken                = Setting::getSetting('AccessTokenZkbio');
            $pid                        = $row['persPersonPin'];
            $cardNumber                 = $row['certNum'];
            $certType                   = $row['certType'];
            
            /* Emp Data */
            $code_depart                = 'VHC001';
            $name_depart                = 'VEHICLE';
            Log::info('Department Name ' . $name_depart . ' (' . $code_depart . ') to ZKBIO Visitor');

            $this->processDeleteVisitor($accessToken, $certType, $cardNumber, $pid, $row, $batch_id);
        }


        Log::info('Upload Visitor ZKBIO Done ! Time : ' . Carbon::now()->diffForHumans($start));
    }

    protected function processDeleteVisitor(string $accessToken,string $certType, string $cardNumber, $pid, array $row, string $batch_id): void
    {
        $params = [
            'certType'                  => $certType,
            'certNum'                   => $cardNumber,
        ];
        
        $urlDelete                      = Setting::getSetting('UrlVisitorExit');
        $upload                         = Http::withoutVerifying()
            ->withOptions(['verify' => false])
            ->withHeaders([
                'Content-Type' => 'application/json',
            ])
            ->post($urlDelete . '?certType='.$certType.'&certNum='.$cardNumber.'&access_token=' . $accessToken, $params);

        Log::info('Result from delete visitor on Zkbio', [
            'json' => $upload->json(),
            'url' => $urlDelete,
            'log' => $params
        ]);
        $this->createLogAccessLevel(employee: $row, upload: $upload, params: $params, batch_id: $batch_id, submitted: true, message: '', code : '');
    }

    /* Huawei Services */
    public function uploadFaceRecord($rows, int $nvr_id, $batch_id): void
    {
        $service                        = new BatchUploadService();

        $start                          = Carbon::now()->format('Y-m-d H:i:s');


        foreach ($rows as $employee) {
            $company                    = $employee['company'];
            $statusEmployee             = $employee['statusEmployee'];
            $fullName                   = preg_replace('/[^A-Za-z0-9\-\s]/', '', $employee['name']); 

            if (isset($employee['photoName'])) {
                $base64 =  preg_replace('#^data:image/\w+;base64,#i', '', $employee['photoName']);
                $photoName = $employee['photoName'];
                $faceList   = $this->getFaceList($nvr_id, $company, $statusEmployee);

                Log::info('start data to NVR ' , [
                    'faceList' => $faceList
                ]);

                if ($faceList) {
                    foreach ($faceList as $index => $item) {
                        $index          = 1;
                        $nvr            = Nvr::find($item->nvr_id);
                        $service->validateNvrCookie($nvr->id);
                        $nvrName        = str_replace(' ', '_', $nvr->name);
                        $cookieNvr      = $this->getConfigNvr($nvrName, 'cookie ' . $nvrName);
                        $this->processUploadFaceRecord($index, $fullName, $employee, $base64, $cookieNvr, $nvr, $nvr_id, $batch_id, $photoName, $item->face_list_id);
                        Log::info('Cookie ' . $nvrName . ' = ' . $cookieNvr);
                    }
                } else {
                    $this->createLogFaceRecord(employee: $employee, item: [], upload: [], params: [], batch_id: $batch_id, submitted: false, message: 'FaceList Not Found!', code : 'FL404');
                  
                }
            } else {
                $this->createLogFaceRecord(employee: $employee, item: [], upload: [], params: [], batch_id: $batch_id, submitted: false, message: 'Photo not found!',code : 'PH404');
                Log::info('Photo not found!');
            }
        }

        Log::info('Upload Done ! Time : ' . Carbon::now()->diffForHumans($start), [
            'batch id' => $batch_id
        ]);
    }

    protected function processUploadFaceRecord(int $index, string $fullName, array $employee, string $base64, string $cookieNvr, Nvr $nvr, $item, $batch_id,$photoName, int $faceList): void
    {
        $params                         = [
            'peopleList'                => [
                'index'                 => $index,
                'name'                  => preg_replace('/-+/', '', $fullName),
                'credentialNumber'      => $employee['credentialNumber'],
                'credentialType'        => $employee['credentialType'],
                'gender'                => $employee['gender'],
                'bornTime'              => $employee['bornTime'],
                'country'               => $employee['country'],
                'occupation'            => $employee['occupation'],
                'description'           => $employee['description'],
                'strId'                 => $employee['strId'],
                'pictures'              => [$base64]
            ]
        ];

        $paramSearch                    = [
            'credential_number'         => $employee['credentialNumber']
        ];

        $paramPerson = [
            'credential_type'           => $employee['credentialType'],
            'name'                      => preg_replace('/-+/', '', $fullName),
            'company'                   => $employee['company'],
            'department'                => $employee['description'],
            'photo_name'                => NULL,
            'status'                    => 1,
        ];
        Log::info('Starting Upload To NVR List ');
        $upload                         = Http::withoutVerifying()
            ->withOptions(['verify' => false])
            ->withHeaders([
                'Accept'                => 'application/json',
                'Cookie'                => 'JSESSIONID=' . $cookieNvr,
            ])
            ->post($nvr->ip . '/sdk_service/rest/facerepositories/' . $faceList . '/peoples', $params);
        
            Log::info('Result from POST: ' . $nvr->ip, [
                'json' => $upload->json(),
            ]);

            Log::info('NVR IP ' . $nvr->ip);

            $code                       = $upload->collect()['resultCode'];
            $message                    = $upload->collect()['resultMsg'];
 
            Persons::updateOrCreate($paramSearch, $paramPerson);

            Log_transaction::create([
                'credential_number'     => $employee['credentialNumber'],
                'credential_type'       => $employee['credentialType'],
                'name'                  => $employee['name'],
                'company'               => $employee['company'],
                'type'                  => 'HUAWEI', 
                'nvr_id'                => $nvr->id,
                'face_list_id'          => $faceList,
                'access_level_code'     => null,
                'status'                => 3,
                'application_id'        => $employee['application_id'],
                'service_code'          => $code,
                'service_message'       => $message,
                'service_payload'       => '',
                'batch_id'              => $batch_id,
                'access_level_name'     => null,
                'log_type'          => 'VISITOR',
            ]);

        Log::info('Result from upload face record to NVR: ' . $nvr->name, [
            'json' => $upload->json(),
        ]);
    }

    protected function createLogFaceRecord(array $employee, $item, $upload, $params, bool $submitted = true, string $message = '',$code, $batch_id ): void
    {
        if (!$submitted) {
            $code                       = $code;
            $params                     = '';
        } else {
            $code                       = $upload->collect()['resultCode'];
            $message                    = $upload->collect()['resultMsg'];
            $params                     = json_encode(collect($params['peopleList'])->except(['pictures']));
        }

        $fullName                       = preg_replace('/[^A-Za-z0-9\-\s]/', '', $employee['name']);
        $photoName                      = $employee['photoName'];
        $paramSearch                    = [
            'credential_number'         => $employee['credentialNumber']
        ];

        $paramPerson = [
            'credential_type'           => $employee['credentialType'],
            'name'                      => preg_replace('/-+/', '', $fullName),
            'company'                   => $employee['company'],
            'department'                => $employee['department'],
            'photo_name'                => NULL,
            'status'                    => 1,
        ];

        Persons::updateOrCreate($paramSearch, $paramPerson);

        $log                            =  Log_transaction::create([
            'credential_number' => $employee['credentialNumber'],
            'credential_type'   => $employee['credentialType'],
            'name'              => $employee['name'],
            'company'           => $employee['company'],
            'type'              => 'HUAWEI',
            'nvr_id'            => '',
            'face_list_id'      => '',
            'access_level_code' => null,
            'status'            => 3,
            'application_id'    => $employee['application_id'],
            'service_code'      => $code,
            'service_message'   => $message,
            'service_payload'   => '',
            'batch_id'          => $batch_id,
            'access_level_name' => null,
            'log_type'          => 'VISITOR',
        ]);

        Log::info('Create Log HUAWEI for user ' . $employee['credentialNumber'] . ' with ID ' . $log->id);
    }


    public function deleteHuaweiVisitor(Collection $rows, $nvr_id, string $batch_id, Applications $applications): void
    {
        $start                          = Carbon::now()->format('Y-m-d H:i:s');
        $service                        = new BatchUploadService();
        Log::info('start delete Visitor HUAWEI, time: ' . $start);
        foreach ($rows as $employee) {
            $company                    = $employee['company'];
            $statusEmployee             = $employee['statusEmployee'];
            $fullName                   = preg_replace('/[^A-Za-z0-9\-\s]/', '', $employee['name']); 

            $faceLists   = $this->getFaceList($nvr_id, $company, $statusEmployee);

            Log::info('start delete data from NVR ' , [
                'faceList' => $faceLists
            ]);

            if ($faceLists) {
                foreach ($faceLists as $key => $item) {
                    $paramsPeople = [
                        'groupids' => $item->face_list_id,
                        'credentialNumber' => $employee['credentialNumber'],
                        'page' => [
                            "no" => "1",
                            "size" => "1",
                            "sort" => "asc",
                            "orderName" => "name"
                        ]
                    ];

                    $nvr            = Nvr::find($item->nvr_id);
                    Log::info('Now nvr in facelist is : ' , [
                        'faceList_nvr' => $item->id
                    ]);

                    $nvr            = Nvr::find($item->nvr_id);
                    $service->validateNvrCookie($nvr->id);
                    $nvrName        = str_replace(' ', '_', $nvr->name);
                    $cookieNvr      = $this->getConfigNvr($nvrName, 'cookie ' . $nvrName);

                    $detailPeople = Http::withoutVerifying()
                        ->withOptions(['verify' => false])
                        ->withHeaders([
                            'Accept' => 'application/json',
                            'Cookie' => 'JSESSIONID=' . $cookieNvr,
                        ])
                        ->post($nvr->ip . '/sdk_service/rest/facerepositories/peoples', $paramsPeople);

                    $data = json_decode($detailPeople, true);
                   

                    if ($data['total'] != 0) {
                        $people = $data['peopleList'];
                        $collection = collect($people);
                        $paramsId = json_encode($collection->pluck('peopleId'));
                        $peopleIds = json_decode($paramsId, true)[0];

                        $url = $nvr->ip . '/sdk_service/rest/facerepositories/' . $item->face_list_id . '/peoples?ids=' . $peopleIds . '&credentialnumbers=' . $employee['credentialNumber'];

                        $response = Http::withoutVerifying()
                            ->withOptions(['verify' => false])
                            ->withHeaders([
                                'Accept' => 'application/json',
                                'Cookie' => 'JSESSIONID=' . $cookieNvr,
                            ])->delete($url);
                     
                          
    
                        if(isset($response->collect()['resultCode'])){
                            $code = $response->collect()['resultCode'];
                            $message = $response->collect()['resultMsg'];
                        }else{
                          
                            $responseData = simplexml_load_string($response->body()); 
                            $responseArray = json_decode(json_encode($responseData), true); 

                            if (isset($responseArray['result']['code'])) {
                                $code = $responseArray['result']['code'];
                                $message = $responseArray['result']['errmsg'];
                            } else {
                                $code = $responseArray['code'] ?? null;
                                $message = $responseArray['errmsg'] ?? 'Unknown error message';
                            }
                        }

                        $paramSearch = [
                            'credential_number' => $employee['credentialNumber']
                        ];

                        $paramPerson = [
                            'name'              => preg_replace('/-+/', '', $employee['name']),
                            'company'           => $employee['company'],
                            'department'        => $employee['department'],
                            'status'            => $employee['statusEmployee'],
                        ];
                
                        Persons::updateOrCreate($paramSearch, $paramPerson);

                        Log_transaction::create([
                            'credential_number' => $employee['credentialNumber'],
                            'credential_type'   => $employee['credentialType'],
                            'name'              => $employee['name'],
                            'company'           => $company,
                            'type'              => 'HUAWEI', // HUAWEI & ZKBIO
                            'nvr_id'            => $nvr->id,
                            'face_list_id'      => $item->face_list_id,
                            'access_level_code' => null,
                            'status'            => $employee['statusEmployee'],
                            'application_id'    => $employee['application_id'],
                            'service_code'      => $code,
                            'service_message'   => $message,
                            'service_payload'   => '',
                            'batch_id'          => $batch_id,
                            'access_level_name' => null,
                            'log_type'          => 'VISITOR',
                            'method'            => 'DELETE'
                        ]);
                    
                    }
                    }
                } else {
                
                $paramSearch = [
                    'credential_number' => $employee['credentialNumber']
                ];

                $paramPerson = [
                    'name'              => preg_replace('/-+/', '', $employee['name']),
                    'company'           => $employee['company'],
                    'department'        => $employee['department'],
                    'photo'             => $employee['photo'],
                    'status'            => $employee['statusEmployee'],
                ];
        
                Persons::updateOrCreate($paramSearch, $paramPerson);

                Log_transaction::create([
                    'credential_number' => $credential_number,
                    'credential_type'   => $employee['credentialType'],
                    'name'              => $employee['name'],
                    'company'           => $company,
                    'type'              => 'HUAWEI', // HUAWEI & ZKBIO
                    'nvr_id'            => $nvr->id,
                    'face_list_id'      => 0,
                    'access_level_code' => null,
                    'status'            => $employee['statusEmployee'],
                    'application_id'    => $employee['application_id'],
                    'service_code'      => 'FL404',
                    'service_message'   => 'Mapping Face List Not Found',
                    'service_payload'   => null,
                    'batch_id'          => $batch_id,
                    'access_level_name' => null,
                    'log_type'          => 'VISITOR',
                ]);
            }
        }

        Log::info('Upload Done ! Time : ' . Carbon::now()->diffForHumans($start), [
            'batch id' => $batch_id
        ]);


        Log::info('Upload Visitor HUAWEI Done ! Time : ' . Carbon::now()->diffForHumans($start));
    }

    protected function processDeleteHuaweiVisitor(int $index, string $fullName, array $employee, string $cookieNvr, Nvr $nvr, $batch_id, int $faceList): void
    {
        $params                         = [
            'peopleList'                => [
                'index'                 => $index,
                'name'                  => preg_replace('/-+/', '', $fullName),
                'credentialNumber'      => $employee['credentialNumber'],
                'credentialType'        => $employee['credentialType'],
                'gender'                => $employee['gender'],
                'bornTime'              => $employee['bornTime'],
                'country'               => $employee['country'],
                'occupation'            => $employee['occupation'],
                'description'           => $employee['description'],
                'strId'                 => $employee['strId'],
            ]
        ];

        $paramSearch                    = [
            'credential_number'         => $employee['credentialNumber']
        ];

        $paramPerson = [
            'credential_type'           => $employee['credentialType'],
            'name'                      => preg_replace('/-+/', '', $fullName),
            'company'                   => $employee['company'],
            'department'                => $employee['description'],
            'photo_name'                => NULL,
            'status'                    => 0,
        ];
        Log::info('Starting Delete From NVR List ');
        $delete                         = Http::withoutVerifying()
            ->withOptions(['verify' => false])
            ->withHeaders([
                'Accept'                => 'application/json',
                'Cookie'                => 'JSESSIONID=' . $cookieNvr,
            ])
            ->post($nvr->ip . '/sdk_service/rest/facerepositories/' . $faceList . '/peoples?ids='.$employee['identifyNumber'].'&credentialnumbers='.$employee['credentialNumber'].'');
        
            Log::info('Result from DELETE: ' . $nvr->ip, [
                'json' => $delete->json(),
            ]);

            Log::info('NVR IP ' . $nvr->ip);

            $code                       = $delete->collect()['resultCode'];
            $message                    = $delete->collect()['resultMsg'];
 
            Persons::updateOrCreate($paramSearch, $paramPerson);

            Log_transaction::create([
                'credential_number'     => $employee['credentialNumber'],
                'credential_type'       => $employee['credentialType'],
                'name'                  => $employee['name'],
                'company'               => $employee['company'],
                'type'                  => 'HUAWEI', 
                'nvr_id'                => $nvr->id,
                'face_list_id'          => $faceList,
                'access_level_code'     => null,
                'status'                => 3,
                'application_id'        => $employee['application_id'],
                'service_code'          => $code,
                'service_message'       => $message,
                'service_payload'       => '',
                'batch_id'              => $batch_id,
                'access_level_name'     => null
            ]);

        Log::info('Result of deleting face record from NVR: ' . $nvr->name, [
            'json' => $upload->json(),
        ]);
    }

    public function getFaceList(int $nvr_id, string $company, int $status = 3): mixed
    {
        return FaceList::where('nvr_id', $nvr_id)
            ->whereHas('organizationFaceList', function ($query) use ($company) {
                $query->whereHas('organization', function ($qr) use ($company) {
                    return $qr->where('alias', 'LIKE', '%EXTERNAL%');
                });
            })
            ->where('face_list_type', $status)
            ->get();
    }

    

}
