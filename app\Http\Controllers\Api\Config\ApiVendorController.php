<?php

namespace App\Http\Controllers\Api\Config;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;

class ApiVendorController extends Controller
{
    public function index(Request $request)
    {
        $tags = [];
        $filter = json_decode($request->get('filters'));
        $data_apivendor = ApiVendor::where(function ($q) use ($filter) {
            if ($filter != '') {
                foreach ($filter as $tags) {
                    if ($tags != null) {
                        if ($tags->id == 'status') {
                            $val = ($tags->value == 'Active') ? 1 : 0;
                        } else {
                            $val = $tags->value;
                        }
                        $q->where($tags->id, 'LIKE', '%' . $val . '%');
                    }
                }
            }
        })
            ->skip($request->get('start'))
            ->take($request->get('size'))
            ->orderBy('id', 'asc')
            ->get();
        $dataAll = ApiVendor::orderBy('id', 'desc')->get();
        $select = [];

        foreach ($dataAll as $role) {
            $select[] = [
                'value' => $role->id,
                'label' => $role->type,
            ];
        }

        try {
            return response()->json([
                'status' => true,
                'message' => 'Success',
                'data' => $data_apivendor,
                'select' => $select,
                'total' => $dataAll->count(),
                'response' => $request->all(),
            ], 200);
        } catch (\Throwable $th) {
            return response()->json([
                'status' => false,
                'message' => $th->getMessage(),
                'data' => []
            ], 500);
        }
    }
}
