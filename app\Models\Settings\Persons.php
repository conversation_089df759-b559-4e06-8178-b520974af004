<?php

namespace App\Models\Settings;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Settings\Persons
 *
 * @property int $id
 * @property string $credential_number
 * @property string $credential_type
 * @property string $name
 * @property string $company
 * @property string|null $type
 * @property int|null $nvr_id
 * @property int|null $face_list_id
 * @property int $status
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property string|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int|null $application_id
 * @property string|null $access_level_code
 * @property string|null $service_code
 * @property string|null $service_payload
 * @property string|null $service_message
 * @property string|null $batch_id
 * @method static \Illuminate\Database\Eloquent\Builder|Persons newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Persons newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Persons query()
 * @method static \Illuminate\Database\Eloquent\Builder|Persons whereAccessLevelCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Persons whereApplicationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Persons whereBatchId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Persons whereCompany($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Persons whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Persons whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Persons whereCredentialNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Persons whereCredentialType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Persons whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Persons whereFaceListId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Persons whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Persons whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Persons whereNvrId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Persons whereServiceCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Persons whereServiceMessage($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Persons whereServicePayload($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Persons whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Persons whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Persons whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Persons whereUpdatedBy($value)
 * @property string|null $access_level_name
 * @method static \Illuminate\Database\Eloquent\Builder|Persons whereAccessLevelName($value)
 * @property string $department
 * @property string|null $nvr_name
 * @property string|null $nvr_code
 * @property string|null $face_list_code
 * @property string|null $photo_name
 * @method static \Illuminate\Database\Eloquent\Builder|Persons whereDepartment($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Persons whereFaceListCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Persons whereNvrCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Persons whereNvrName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Persons wherePhotoName($value)
 * @mixin \Eloquent
 */
class Persons extends Model
{
    use HasFactory;
    protected $guarded = ['id'];
    protected $table = 'persons';
}
