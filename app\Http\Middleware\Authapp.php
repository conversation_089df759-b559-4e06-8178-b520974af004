<?php

namespace App\Http\Middleware;

use App\Models\Appauthentication;
use Closure;
use Illuminate\Http\Request;
// use Illuminate\Support\Facades\Http;
// use Symfony\Component\HttpFoundation\Response;

class Authapp
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure $next
     */
    public function handle(Request $request, Closure $next)
    {
        $bearer                                 = $request->header('Authorization');
        $is_fav                                 = false;
        if (isset($bearer)) {
            $fav                                = Appauthentication::where('token', $request->bearerToken())->where('status', 1)->first();
            if ($fav) {
                $is_fav                         = true;
            }
        }

        if ($is_fav == true) {
            return $next($request);
        } else {
            return response()->json([
                'status'                        => false,
                'message'                       => 'Unauthorized ' . $request->bearerToken(),
            ], 200);
        }
    }
}
