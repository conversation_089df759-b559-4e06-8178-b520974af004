<?php

namespace App\Http\Controllers\Settings;

use App\Http\Controllers\Controller;
use App\Models\DeviceConfig;
use App\Models\DeviceConfigDetail;
use App\Services\Settings\DeviceConfigService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class DeviceConfigController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $service = new DeviceConfigService();
        try {
            return $this->success('', $service->index($request));
        } catch (\Exception $exception) {
            throw new \Exception($exception->getMessage(), 1);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $service = new DeviceConfigService();
        DB::beginTransaction();
        try {
            $header = DeviceConfig::create([
                'device_name' => $request->device_name,
                'serial_number' => $request->serial_number,
                'device_id' => $request->device_id,
                'area_name' => $request->area_name,
                'device_location' => $request->device_location,
                'device_type' => $request->device_type,
                'latitude' => $request->latitude,
                'longitude' => $request->longitude,
                'created_by' => $request->user()->id
            ]);
            foreach ($request->line_items as $key => $value) {
                $service->processItemDetails($value, $header->id);
            }

            DB::commit();

            return $this->success('Data inserted!', [
                'errors' => false,
            ]);
        } catch (\Exception $exception) {
            DB::rollBack();
            throw new \Exception($exception->getMessage(), 1);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Request $request, string $id)
    {
        $service = new DeviceConfigService();
        $data = DeviceConfig::where('id', $request->id)
            ->with(['lineItems'])
            ->first();

        return $this->success('', [
            'data' => ($data) ? $data->lineItems : $service->defaultDetail(),
            // 'default_item' => $service->defaultDetail(),
            'data2' => $data,
            'columns' => $service->columns(),
            'colHeaders' => $service->colHeaders(),
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $service = new DeviceConfigService();
        DB::beginTransaction();
        try {
            $header = DeviceConfig::where('id', $id)
                ->update([
                    'device_name' => $request->device_name,
                    'serial_number' => $request->serial_number,
                    'device_id' => (string) $request->device_id,
                    'area_name' => $request->area_name,
                    'device_location' => $request->device_location,
                    'device_type' => $request->device_type,
                    'latitude' => (string) $request->latitude,
                    'longitude' => (string) $request->longitude,
                ]);

            DeviceConfigDetail::where('device_config_id', $id)->delete();
            $header = DeviceConfig::where('id', $id)->first();
            foreach ($request->line_items as $key => $value) {
                $service->processItemDetails($value, $header->id);
            }

            DB::commit();

            return $this->success('Data updated!', [
                'errors' => false,
            ]);
        } catch (\Exception $exception) {
            DB::rollBack();
            throw new \Exception($exception->getMessage(), 1);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $data = DeviceConfig::where('id', $id)
                ->delete();

            return $this->success('Data deleted!', [
                'errors' => false,
            ]);
        } catch (\Exception $exception) {
            throw new \Exception($exception->getMessage(), 1);
        }
    }
}
