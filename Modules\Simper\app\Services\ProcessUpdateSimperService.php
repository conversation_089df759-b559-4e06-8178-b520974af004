<?php

namespace Modules\Simper\app\Services;
use App\Models\Applications;
use App\Models\Settings\AccessLevel;
use App\Models\Settings\Log_transaction;
use App\Models\Settings\Setting;
use Carbon\Carbon;
use Illuminate\Bus\Batch;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Modules\Simper\app\Jobs\ProcessEditSimperZkbioJob;
use Throwable;

class ProcessUpdateSimperService
{
      /**
     * @param $data
     * @param Applications $application
     * @return array
     * @throws Throwable
     */
    public function processQueueZkbio($data, Applications $application): array
    {

        $collection = collect($data);
        $chunks = $collection->chunk(20);
        $batchQueue = [];
        $batch = Bus::batch([])
            ->catch(function (Batch $batch, Throwable $e) use ($application) {
                Log::info('error upload batch ' . now(), [
                    'error' => $e->getTraceAsString(),
                ]);
                // sleep(5);
                $logTransaction = Log_transaction::where('batch_id', $batch->id)
                    ->select(
                        "credential_number",
                        "name",
                        "company",
                        "nvr_id",
                        "face_list_id",
                        "service_code",
                        "service_message",
                        "batch_id"
                    )
                    ->where("service_code", "<>", "0")
                    ->where('application_id', $application->id)
                    ->where('type', "ZKBIO")
                    ->get();

                $params = [
                    'message' => $e->getMessage(),
                    'batch_id' => $batch->id,
                    'status' => 'error',
                    'name' => $batch->name,
                    'data' => $logTransaction,
                    // "application" => $application->id,
                ];

                Http::post($application->link_url, $params);

                activity()
                    ->performedOn($application)
                    ->withProperties($params)
                    ->log('Error process face record to application ' . $application->name);
            })
            ->then(function (Batch $batch) use ($application) {
                Log::info('Batch ID ZKBIO ' . $batch->id);
                $logTransaction = Log_transaction::where('batch_id', $batch->id)
                    ->select(
                        "credential_number",
                        "name",
                        "company",
                        "nvr_id",
                        "face_list_id",
                        "service_code",
                        "service_message",
                        "batch_id"
                    )
                    ->where('application_id', $application->id)
                    ->where('type', "ZKBIO")
                    ->get();
                $params = [
                    'message' => 'success',
                    'batch_id' => $batch->id,
                    'status' => 'completed',
                    'name' => $batch->name,
                    "data" => $logTransaction,
                    // "application" => $application->id,
                ];
                Http::post($application->link_url, $params);

                activity()
                    ->performedOn($application)
                    ->withProperties($params)
                    ->log('Success process face record to application ' . $application->name);
            })
            ->name('Upload Zkbio Vehicle' . date('Y-m-d H:i:s'))
            ->onQueue('ZkbioSimperDev')
            // ->onQueue('Zkbio')
            ->allowFailures()
            ->dispatch();

        foreach ($chunks as $key => $chunk) {
            $batch->add(new ProcessEditSimperZkbioJob(row: $chunk, batchId: $batch->id, application: $application));
        }

        return [
            [
                'label' => 'ZKBIO',
                'batch_id' => $batch->id
            ]
        ];
    }

    public function updateDataVehicle(Collection $rows, string $batch_id, Applications $applications): void
    {
        $start = Carbon::now()->format('Y-m-d H:i:s');

        Log::info('start upload Vehicle ZKBIO, time: ' . $start);

        $data_level = [];
        foreach ($rows as $row) {

            $access = AccessLevel::where('access_level_id', $row['accessMapping'])->first();
            $accessLevelCode = $access->access_level_code;
            $accessLevelName = $access->access_level_name;

            $accessToken    = Setting::getSetting('AccessTokenZkbio');
            $pid            = $row['credentialNumber'];
            $cardNumber     = $row['cardNumber'];
            $data_name      = $row['name'];
            $data_last_name = '';
            $acc_level_name = $accessLevelName;
            $acc_level_code = $accessLevelCode;
            $accStartTime   = $row['accStartTime'];
            $accEndTime     = $row['accEndTime'];

            /* Emp Data */
            // $deptCode = $this->getDepartmentCode((array) $row, $row['access_type']);
            $code_depart = 'VHC001';
            $name_depart = 'VEHICLE';

            Log::info('Department Name ' . $name_depart . ' (' . $code_depart . ') to ZKBIO ');

            $this->processUploadVehicle($accessToken,$pid, $cardNumber, $code_depart, $name_depart, $data_name, $data_last_name, $accStartTime, $accEndTime, $row, $batch_id,$acc_level_name,$acc_level_code);
        }

        Log::info('Upload Vehicle ZKBIO Done ! Time : ' . Carbon::now()->diffForHumans($start));
    }
}
