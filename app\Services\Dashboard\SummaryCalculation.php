<?php

namespace App\Services\Dashboard;

use App\Models\Settings\FaceList;
use App\Models\Settings\Nvr;
use App\Models\Settings\Setting;
use App\Models\View\SummaryLog;
use App\Models\View\SummaryPerCompany;
use App\Services\Upload\BatchUploadService;
use App\Traits\NvrCookieHelper;
use Illuminate\Support\Facades\Http;

class SummaryCalculation
{
    use NvrCookieHelper;

    public function getTotalPersonNvr()
    {
        $service = new BatchUploadService();
        $dataNvr = Nvr::all();
        $dataTrustList = [];
        $dataBlockList = [];
        foreach ($dataNvr as $nvr) {
            $service->validateNvrCookie($nvr->id);
            $nvrName = str_replace(' ', '_', $nvr->name);
            $cookieNvr = $this->getConfigNvr($nvrName, 'cookie ' . $nvrName);
            $dataTrustList[] = [
                "DeviceName" => $nvr->name,
                "Total" => intval($this->calculateHuawei($cookieNvr, $nvr, [3])),
            ];
            $dataBlockList[] = [
                "DeviceName" => $nvr->name,
                "Total" => intval($this->calculateHuawei($cookieNvr, $nvr, [2])),
            ];
        }

        $collection1 = collect($dataTrustList);
        $collection2 = collect($dataBlockList);

        $data = [
            [
                'name' => 'Trust list',
                'data' => $collection1->pluck('Total')->toArray()
            ],
            [
                'name' => 'Block List',
                'data' => $collection2->pluck('Total')->toArray()
            ]
        ];

        return $data;
    }

    public function calculateTotalPersonHuawei(): array
    {
        $service = new BatchUploadService();
        $dataNvr = Nvr::all();
        $data = [];
        foreach ($dataNvr as $nvr) {
            $service->validateNvrCookie($nvr->id);
            $nvrName = str_replace(' ', '_', $nvr->name);
            $cookieNvr = $this->getConfigNvr($nvrName, 'cookie ' . $nvrName);
            $data[] = [
                "DeviceName" => $nvr->name,
                "Total" => $this->calculateHuawei($cookieNvr, $nvr, [3]),
            ];
        }

        $data[] = [
            "DeviceName" => "Zkbio",
            "Total" => intval(Setting::getSetting(option: 'TotalPersonZkbio')),
        ];

        $collection1 = collect($data);
        $collection2 = collect($this->calculateLogTransaction());

        // Extract totals
        $data = [
            [
                'name' => 'Device',
                'data' => $collection1->pluck('Total')->toArray()
            ],
            [
                'name' => 'Log Transaction',
                'data' => $collection2->pluck('Total')->toArray()
            ]
        ];
        return $data;
    }

    public function calculateHuawei($cookieNvr, $nvr, $faceListType = [3, 2]): int
    {
        $faceList = FaceList::where('nvr_id', $nvr->id)
            ->whereIn("face_list_type", $faceListType)
            ->select('face_list_id')
            ->get()
            ->pluck('face_list_id')
            ->implode(",");

        $total = Http::withoutVerifying()
            ->withOptions(['verify' => false])
            ->timeout(300)
            ->withHeaders([
                'Accept' => 'application/json',
                'Cookie' => 'JSESSIONID=' . $cookieNvr,
            ])
            ->post($nvr->ip . '/sdk_service/rest/facerepositories/peoples', [
                "groupids" => $faceList,
                "page" => [
                    "no" => "1",
                    "size" => "1",
                    "sort" => "asc",
                    "orderName" => "name"
                ]
            ]);

        return $total->collect()["number"];
    }

    public function calculateLogTransaction()
    {
        return SummaryLog::orderBy("DeviceName")->get()->toArray();
    }

    public function getPersonTotalByCompany(): array
    {
        $list = ["Zkbio", "NVR1", "NVR2", "NVR3", "NVR4"];
        $data = [];
        foreach ($list as $key => $value) {
            $data[] = [
                "name" => $value,
                "data" => $this->getTotalByCompany($value)
            ];
        }
        return [
            "series" => $data,
            "categories" => $this->getTotalByCompany("alias")
        ];
    }

    public function getTotalByCompany($column): array
    {
        return SummaryPerCompany::orderBy("alias")->select($column)->whereNotNull('alias')->get()->pluck($column)->toArray();
    }
}
