<?php

namespace App\Models\Settings;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Settings\AccessLevelOrganization
 *
 * @property int $id
 * @property int $access_level_id
 * @property int $organization_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Settings\Organization|null $organization
 * @method static \Illuminate\Database\Eloquent\Builder|AccessLevelOrganization newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AccessLevelOrganization newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AccessLevelOrganization query()
 * @method static \Illuminate\Database\Eloquent\Builder|AccessLevelOrganization whereAccessLevelId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccessLevelOrganization whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccessLevelOrganization whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccessLevelOrganization whereOrganizationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccessLevelOrganization whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class AccessLevelOrganization extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function organization()
    {
        return $this->belongsTo(Organization::class, 'organization_id', 'id');
    }
}
