<?php

namespace App\Http\Controllers\Upload;

use App\Http\Controllers\Controller;
// use App\Imports\FaceRecordImport;
// use App\Jobs\Upload\ProcessUploadFaceRecord;
use App\Models\Mapping\HrisImage;
// use App\Services\Upload\UploadFaceRecordService;
use Illuminate\Bus\Batch;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;
// use Maatwebsite\Excel\Facades\Excel;
use Throwable;

class HrisFaceRecordController extends Controller{
    public function __construct()
    {
    }

    public function index(Request $request)
    {
        $response = DB::connection('mysql')->select("SELECT * FROM vw_access_control");
        echo "<pre>";
        print_r($response);
        echo "</pre>";
        // $data = array();
        // foreach($response as $res){
        //     $data[] = array(
        //         'value' => $res->photo_name,
        //         'label' => $res->name,
        //     );
        // }
        // try{
        //     return response()->json([
        //         'status'   => true,
        //         'message'  => 'Success',
        //         'data'     => $data,
        //     ], 200);
        // } catch (\Throwable $th) {
        //     return response()->json([
        //         'status'  => false,
        //         'message' => $th->getMessage(),
        //         'data'    => []
        //     ], 500);
        // }
    }

    public function store(Request $request){
        
    }
}