<?php

namespace App\Http\Controllers\Api\Config;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Role;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class UserController extends Controller
{
    public function index(Request $request){
        $tags = [];
        $filter = json_decode($request->get('filters'));
        $dataUser = User::where(function ($q) use ($filter){    
            if($filter != ''){
                foreach ($filter as $tags) {
                    if($tags != null ){   
                        $q->where($tags->id,'LIKE','%'.$tags->value.'%');
                    }
                }
            }
        })
        ->skip($request->get('start'))
        ->take($request->get('size'))
        ->orderBy('id','desc')
        ->get();

        $dataAll = User::orderBy('id','desc')->get();
      
        try{
            return response()->json([
                'status'   => true,
                'message'  => 'Success',
                'data'     => $dataUser,
                'total'    => $dataAll->count(),
                'response' => $request->all(),
            ], 200);
        } catch (\Throwable $th) {
            return response()->json([
                'status'  => false,
                'message' => $th->getMessage(),
                'data'    => []
            ], 500);
        }
    }

    public function store(Request $request){
        try {
            //Validated
            $validateUser = Validator::make(
                $request->all(),
                [
                    'name'     => 'required',
                    'email'    => 'required|email|unique:users,email',
                    'password' => 'required',
                    'username' => 'required|unique:users,username',
                ]
            );

            if ($validateUser->fails()) {
                return response()->json([
                    'status'  => false,
                    'message' => 'validation error',
                    'errors'  => $validateUser->errors()
                ], 422);
            }

            //get role dan konek role to user
            $roles = Role::where('id', '=', $request->role_id)->first();

            $user = User::create([
                'name'         => $request->name,
                'email'        => $request->email,
                'username'     => $request->username,
                'role_id'      => $request->role_id,
                'password'     => Hash::make($request->password),
                'status'       => 1,
                'token_cherry' => ''
            ]);

            $user->assignRole($roles->name);
            $this->setPermission($request, $user);

            return response()->json([
                'status'  => true,
                'message' => 'User Created Success'
            ], 200);
        } catch (\Throwable $th) {
            return response()->json([
                'status'  => false,
                'message' => $th->getMessage()
            ], 401);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\User  $user
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $user = User::find($id);
        $rules = [
            'username'   => 'required|string',
            'name'       => 'required|string|max:255',
            'email'      => 'required|string',
            'perusahaan' => 'string',
            'divisi'     => 'string',
            'role_id'    => 'numeric',
        ];
       
        if($request->username != $request->username_old){
            $rules['username'] = 'required|unique:users';
        }

        if($request->email != $request->email_old){
            $rules['email'] = 'required|unique:users';
        }

        $validatedData = $request->validate($rules);
        DB::beginTransaction();

        DB::table('model_has_roles')
        ->where('model_id', '=', $user->id)
        ->where('model_type', '=', 'App\Models\User')
        ->delete();

        //update data user
        $validatedData['status'] = $request->status;
        $data = User::where('id', $user->id)
        ->update($validatedData);

        //get role dan konek role to user
        $roles = Role::where('id', '=', $request->role_id)->first();
        $user->assignRole($roles->name);

        $this->setPermission($request, $user);

        if($data){
            DB::commit();
            return response()->json([
                'success' => true,
                'message' => $request->input('username').' update successfully !',
            ], 200);
        }else{
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => "Failed to update !"
            ], 401);
        }
    }

    function setPermission($request, $user){
        // delete permission
        $role_permission_del = DB::table('role_has_permissions')
        ->select('role_id', 'permission_id','name')
        ->leftJoin('permissions', 'permissions.id', '=', 'role_has_permissions.permission_id')
        ->where('role_id', $user->role_id)->get();

        foreach($role_permission_del as $rm){
            $user->revokePermissionTo($rm->name);
        }

        // create permission
        $role_permission_ins = DB::table('role_has_permissions')
        ->select('role_id', 'permission_id','name')
        ->leftJoin('permissions', 'permissions.id', '=', 'role_has_permissions.permission_id')
        ->where('role_id', $request->role_id)->get();

        foreach($role_permission_ins as $rm){
            $user->givePermissionTo($rm->name);
        }
    }


     /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($user, Request $request)
    {
        $data = User::find($user);
        $data->delete();
        return response()->json([
            'success' => true,
            'message' => $request->input('name').' successfully deleted !',
        ], 200);
    }
    
}
