<?php

namespace Modules\ExternalApi\app\Jobs;

use App\Models\Settings\Nvr;
use Illuminate\Bus\Queueable;
use Illuminate\Bus\Batchable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use App\Services\ProcessDataPersonService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class ProcessEditPersonModule implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use Batchable;

    public Collection $row;
    public int $nvrId;
    public $batchId;


    /**
     * Create a new job instance.
     */
    public function __construct($row, $nvrId, $batchId)
    {
        $this->row = $row;
        $this->nvrId = $nvrId;
        $this->batchId = $batchId;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $rows = $this->row;

        $service = new ProcessDataPersonService();
        $service->editFaceRecord($rows, $this->nvrId, $this->batchId);
    }
}
