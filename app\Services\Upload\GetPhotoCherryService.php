<?php

namespace App\Services\Upload;

use App\Models\User;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class GetPhotoCherryService
{

    public function getUserPhoto($nik)
    {
        $nik = (string) $nik;
        $responseCherry = DB::connection('sqlsrvcherry')->table('vw_employee_masterdata')
        ->select(DB::raw("Photo"))
        ->where('Nik', $nik)
        ->first();

        if (!$responseCherry || !$responseCherry->Photo) {
            return null;
        }
        $imageBinaryData            = $responseCherry->Photo;
        $base64Image                = base64_encode($imageBinaryData);
        return preg_replace('#^data:image/\w+;base64,#i', '', $base64Image);
    }

}