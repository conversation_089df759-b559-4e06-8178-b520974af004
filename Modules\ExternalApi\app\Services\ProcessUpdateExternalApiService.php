<?php
namespace Modules\ExternalApi\app\Services;

use Throwable;
use Illuminate\Bus\Batch;
use App\Models\Applications;
use App\Models\Settings\Nvr;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use App\Models\Settings\Log_transaction;
use App\Services\ProcessDataPersonService;
use Illuminate\Database\Eloquent\Collection;
use App\Jobs\Upload\ProcessUploadFaceRecordUpdate;
use Modules\ExternalApi\app\Jobs\ProcessEditZkbioModule;
use Modules\ExternalApi\app\Jobs\ProcessUploadZkbioModule;
use Modules\ExternalApi\app\Jobs\ProcessEditPersonModule;
use Modules\ExternalApi\app\Jobs\ProcessDeletePersonModule;
use Modules\ExternalApi\app\Jobs\ProcessUploadPersonModule;

class ProcessUpdateExternalApiService
{
    /**
     * @param $data
     * @param Applications $application
     * @return string
     * @throws Throwable
     */
    public function processQueueZkbio($data, Applications $application): array
    {
        $service = new ProcessDataPersonService();

        $collection = collect($data);
        $chunks = $collection->chunk(20);
        $batchQueue = [];
        $batch = Bus::batch([])
            ->catch(function (Batch $batch, Throwable $e) use ($application) {
                Log::info('error upload batch ' . now(), [
                    'error' => $e->getTraceAsString(),
                ]);

                $params = [
                    'message' => $e->getMessage(),
                    'batch_id' => $batch->id,
                    'status' => 'error'
                ];

                Http::post($application->link_url, $params);

                activity()
                    ->performedOn($application)
                    ->withProperties($params)
                    ->log('Error process face record to application ' . $application->name);
            })
            ->finally(function (Batch $batch) use ($application) {
                $logTransaction = Log_transaction::whereBatchId($batch->id)
                    ->select(
                        "credential_number",
                        "name",
                        "company",
                        "nvr_id",
                        "face_list_id",
                        "service_code",
                        "service_message",
                        "batch_id"
                    )
                    ->where("service_code", "<>", "0")
                    ->whereApplicationId($application->id)
                    ->whereType("ZKBIO")
                    ->get();
                $params = [
                    'message' => 'success',
                    'batch_id' => $batch->id,
                    'status' => 'completed',
                    'name' => $batch->name,
                    "data" => $logTransaction
                ];
                Http::post($application->link_url, $params);

                activity()
                    ->performedOn($application)
                    ->withProperties($params)
                    ->log('Success process face record to application ' . $application->name);
            })
            ->name('Edit Zkbio ' . date('Y-m-d H:i:s'))
            ->onQueue('ZkbioDevApiPatar')
            // ->onQueue('ZkbioUploadApiPrd')
            ->allowFailures()
            ->dispatch();

        foreach ($chunks as $key => $chunk) {
            $batch->add(new ProcessEditZkbioModule(row: $chunk, batchId: $batch->id, application: $application));
        }

        return [
            [
                'label' => 'ZKBIO',
                'batch_id' => $batch->id
            ]
        ];
    }

    /**
     * @param $data
     * @param Collection $nvr
     * @param Applications $application
     * @return array
     * @throws Throwable
     */
    public function processQueueHuawei($data, Applications $application)
    {
        $nvr = Nvr::all();
        $batchId = [];
        foreach ($nvr as $value) {
            $nvrName = str_replace(' ', '', $value->name);
            $collection = collect($data);
            $chunks = $collection->chunk(100);

            $batch = Bus::batch([])
                ->catch(function (Batch $batch, Throwable $e) use ($application, $nvrName) {
                    Log::info('Error mutation to huawei ' . $nvrName . ' time: ' . now(), [
                        'error' => $e->getTraceAsString(),
                    ]);

                    $logTransaction = Log_transaction::where('batch_id', $batch->id)
                        ->select(
                            "credential_number",
                            "name",
                            "company",
                            "access_level_code",
                            "access_level_name",
                            "service_code",
                            "service_message",
                            "batch_id"
                        )
                        ->where("service_code", "<>", "0")
                        ->where('application_id', $application->id)
                        ->where('type', "HUAWEI")
                        ->get();

                    $params = [
                        'message' => $e->getMessage(),
                        'batch_id' => $batch->id,
                        'status' => 'error',
                        'name' => $batch->name,
                        'data' => $logTransaction,
                        // "application" => $application->id,
                    ];
                    Http::post($application->link_url, $params);

                    activity()
                        ->performedOn($application)
                        ->withProperties($params)
                        ->log('Error delete person to application ' . $application->name);
                })
                ->finally(function (Batch $batch) use ($application) {
                    $logTransaction = Log_transaction::whereBatchId($batch->id)
                        ->select(
                            "credential_number",
                            "name",
                            "company",
                            "access_level_code",
                            "access_level_name",
                            "service_code",
                            "service_message",
                            "batch_id"
                        )
                        // ->where("service_code", "<>", "0")
                        ->whereApplicationId($application->id)
                        ->whereIn("type", ["HUAWEI", "ZKBIO"])
                        ->get();

                    $params = [
                        'message' => 'success',
                        'batch_id' => $batch->id,
                        'status' => 'completed',
                        "data" => $logTransaction
                    ];
                    Http::post($application->link_url, $params);

                    activity()
                        ->performedOn($application)
                        ->withProperties($params)
                        ->log('Success mutation person to application ' . $application->name);
                })
                ->name('Update Face Record ' . $nvrName . date('Y-m-d H:i:s'))
                // ->onQueue(queue: $nvrName . 'API-MUTATION')
                ->onQueue(queue: $nvrName.'API-VISITORPATAR')
                ->allowFailures()
                ->dispatch();

            foreach ($chunks as $key => $chunk) {
                $batch->add([new ProcessDeletePersonModule($chunk, $value)]);
                $batch->add([new ProcessUploadPersonModule($chunk, $value->id, $batch->id)]);
            }

            $batchId[] = [
                'label' => $nvrName,
                'batch_id' => $batch->id
            ];
        }

        return $batchId;
    }
}
