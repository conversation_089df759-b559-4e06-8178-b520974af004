<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
// use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;


/**
 * App\Models\Applications
 *
 * @property int $id
 * @property string $name
 * @property string $token
 * @property string $link_url
 * @property int $status
 * @property string|null $created_at
 * @property string|null $updated_at
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection<int, \Illuminate\Notifications\DatabaseNotification> $notifications
 * @property-read int|null $notifications_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Laravel\Sanctum\PersonalAccessToken> $tokens
 * @property-read int|null $tokens_count
 * @method static \Illuminate\Database\Eloquent\Builder|Applications newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Applications newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Applications query()
 * @method static \Illuminate\Database\Eloquent\Builder|Applications whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Applications whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Applications whereLinkUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Applications whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Applications whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Applications whereToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Applications whereUpdatedAt($value)
 * @property string|null $index
 * @property string|null $post
 * @property string|null $delete
 * @property string|null $put
 * @method static \Illuminate\Database\Eloquent\Builder|Applications whereDelete($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Applications whereIndex($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Applications wherePost($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Applications wherePut($value)
 * @mixin \Eloquent
 */
class Applications extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    public $timestamps                          = false;
    protected $table                            = 'applications';
    protected $primaryKey                       = 'id';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'token',
        'link_url',
        'post',
        'delete',
        'put',
        'index'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        // 'password',
        // 'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        // 'email_verified_at' => 'datetime'
    ];
    protected $guarded = [];
}
