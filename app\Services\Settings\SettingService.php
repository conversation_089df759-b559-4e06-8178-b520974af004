<?php

namespace App\Services\Settings;
use App\Models\Settings\Setting;
use App\Traits\ApiResponse;


class SettingService
{
    use ApiResponse;

    /**
     * @param $request
     *
     * @return array
     */
    public function index($request): array
    {
        $options = json_decode($request->options, true);
        $pages = isset($options['page']) ? (int)($options['page'] + 1) : 1;
        $row_data = isset($options['rows']) ? (int)$options['rows'] : 20;
        $sorts = isset($options['sortField']) ? (string)$options['sortField'] : "option";
        $order = (!empty($options['sortOrder'])) ? 'desc' : 'asc';
        $search = isset($request->search) ? (string)$request->search : "";
        $filters = $options['filters'] ?? null;

        $offset = ($pages - 1) * $row_data;

        $result = array();
        $query = Setting::when($filters, function ($query, $filters) {
            if (isset($filters['value']['value'])) {
                $query->where('value', 'LIKE', '%' . $filters['value']['value'] . '%');
            }
        });

        $result["total"] = $query->count();

        $result['data'] = $query
            ->orderBy($sorts, $order)
            ->get();

        $result['form'] = $this->getForm();
        $roles = Setting::select('id', 'option', 'value')->get();
        $selectRoles = [];

        foreach ($roles as $role) {
            $selectRoles[] = [
                'value' => $role->id,
                'label' => $role->name.' - '.$role->alias,
            ];
        }
        $result['select'] = $selectRoles;
        $result['default'] = [
            [
                'id' => null,
                'option' => null,
                'value' => null,
            ]
        ];
        $result['colHeaders'] = ['ID', 'Option', 'Value'];
        $result['columns'] = $this->frontendColumns();

        $collect = collect($result);

        return $collect->all();
    }

    public function frontendColumns()
    {
        return [
            [
                'data' => 'id',
                'width' => 90,
                'wordWrap' => false
            ],
            [
                'data' => 'option',
                'width' => 150,
                'wordWrap' => false
            ],
            [
                'data' => 'value',
                'width' => 300,
                'wordWrap' => false
            ],
        ];
    }

    /**
     * @return array
     */
    public function getForm(): array
    {
        $form = $this->form('roles');
        $form['id'] = 0;

        return $form;
    }

    public function create($data)
    {
        return Setting::create($data);
    }

    public function update($id, $data)
    {
        return Setting::where('id', $id)->update($data);
    }

    /**
     * @param $request
     * @param $type
     *
     * @return array
     */
    public function formData($value): array
    {
        $data = [
            'option' => $value['option'],
            'value' => $value['value'],
            // 'created_by' => auth()->user()->id,
        ];
        return $data;
    }
}
