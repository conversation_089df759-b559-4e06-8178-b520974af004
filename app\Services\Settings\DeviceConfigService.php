<?php

namespace App\Services\Settings;

use App\Models\DeviceConfig;
use App\Models\DeviceConfigDetail;
use App\Traits\ApiResponse;
use App\Traits\AppConfig;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;

class DeviceConfigService
{
    use ApiResponse;
    use AppConfig;

    /**
     * @param $request
     *
     * @return array
     */
    public function index($request): array
    {
        $options = json_decode($request->options, true);
        $pages = isset($options['page']) ? (int) ($options['page'] + 1) : 1;
        $row_data = isset($options['rows']) ? (int) $options['rows'] : 200;
        $sorts = isset($options['sortField']) ? (string) $options['sortField'] : "id";
        $order = (!empty($options['sortOrder'])) ? 'desc' : 'asc';
        $search = isset($request->search) ? (string) $request->search : "";
        $filters = $options['filters'] ?? null;

        $offset = ($pages - 1) * $row_data;

        $result = array();
        $query = DeviceConfig::when($filters, function ($query, $filters) {
            if (isset($filters['device_name']['value'])) {
                $query->where('device_name', 'LIKE', '%' . $filters['device_name']['value'] . '%');
            }
            if (isset($filters['serial_number']['value'])) {
                $query->where('serial_number', 'LIKE', '%' . $filters['serial_number']['value'] . '%');
            }
            if (isset($filters['area_name']['value'])) {
                $query->where('area_name', 'LIKE', '%' . $filters['area_name']['value'] . '%');
            }
        });

        $result["total"] = $query->count();

        $result['data'] = $query
            ->orderBy($sorts, $order)
            ->offset($offset)
            ->get();

        $result['form'] = $this->getForm();

        $result['header'] = $this->frontEndHeader();
        $result['defaultDetail'] = $this->defaultDetail();

        $collect = collect($result);

        return $collect->all();
    }

    /**
     * @return array
     */
    public function getForm(): array
    {
        $form = $this->form('roles');
        $form['id'] = 0;

        return $form;
    }

    /**
     * @param $request
     * @param $type
     *
     * @return array
     */
    public function formData($request, $type): array
    {
        $data = $request->all();

        Arr::forget($data, 'id');
        Arr::forget($data, 'created_at');
        Arr::forget($data, 'updated_at');
        Arr::forget($data, 'organization_id');
        Arr::forget($data, 'ACTIONS');

        if ($type == 'store') {
            $data['created_by'] = $request->user()->id;
        }

        foreach ($data as $key => $value) {
            if (strpos($key, '/api/') === 0) {
                unset($data[$key]);
            }
        }

        return $data;
    }

    public function processItemDetails($deviceLine, $device_config_id)
    {
        $data = [
            'device_config_id' => $device_config_id,
            'header' => (!empty($deviceLine['header'])) ? $deviceLine['header'] : "",
            'sub_menu' => (!empty($deviceLine['sub_menu'])) ? $deviceLine['sub_menu'] : "",
            'field' => (!empty($deviceLine['field'])) ? $deviceLine['field'] : "",
            'value' => (!empty($deviceLine['value'])) ? $deviceLine['value'] : "",
            'notes' => (!empty($deviceLine['notes'])) ? $deviceLine['notes'] : "",
        ];

        $id = (array_key_exists('id', $deviceLine) ? $deviceLine['id'] : null);
        $detail = DeviceConfigDetail::where("id", $id)->first();
        if ($detail) {
            $detail->update($data);
        } else {
            DeviceConfigDetail::create($data);
        }
    }

    public function update($id, $data)
    {
        return DeviceConfig::where('id', $id)->update($data);
    }

    public function frontEndHeader()
    {
        return [
            [
                "accessorKey" => 'device_id',
                "header" => 'Device ID',
            ],
            [
                "accessorKey" => 'device_name',
                "header" => 'Device Name',
            ],
            [
                "accessorKey" => 'serial_number',
                "header" => 'Serial Number',
            ],
            [
                "accessorKey" => 'area_name',
                "header" => 'Area Name',
            ],
            [
                "accessorKey" => 'device_location',
                "header" => 'Location',
            ],
            [
                "accessorKey" => 'device_type_name',
                "header" => 'Device Type',
            ],
        ];
    }

    public function colHeaders()
    {
        return [
            'ID',
            "Header",
            "Sub Menu",
            "Field",
            "Value",
            "Notes"
        ];
    }
    public function columns()
    {
        return [
            [
                // 0
                "width" => 80,
                "wordWrap" => false,
                "data" => 'id',
            ],
            [
                // 1
                "width" => 40,
                "wordWrap" => false,
                "data" => 'header',
            ],
            [
                // 1
                "width" => 60,
                "wordWrap" => false,
                "data" => 'sub_menu',
            ],
            [
                // 1
                "width" => 80,
                "wordWrap" => false,
                "data" => 'field',
            ],
            [
                // 1
                "width" => 60,
                "wordWrap" => false,
                "data" => 'value',
            ],
            [
                // 1
                "width" => 100,
                "wordWrap" => false,
                "data" => 'notes',
            ],
        ];
    }

    public function defaultDetail()
    {
        return [
            [
                "header" => "System",
                "sub_menu" => 'Date Time',
                "field" => "",
                "value" => "",
                "notes" => "",
            ],
            [
                "header" => "System",
                "sub_menu" => "access logs setting",
                "field" => 'Camera mode',
                "value" => "",
                "notes" => "",
            ],
            [
                "header" => "System",
                "sub_menu" => "access logs setting",
                "field" => 'Display User Photo',
                "value" => "",
                "notes" => "",
            ],
            [
                "header" => "System",
                "sub_menu" => "access logs setting",
                "field" => 'Access log alert',
                "value" => "",
                "notes" => "",
            ],
            [
                "header" => "System",
                "sub_menu" => "access logs setting",
                "field" => 'Periodic Del of Access Logs',
                "value" => "",
                "notes" => "",
            ],
            [
                "header" => "System",
                "sub_menu" => "access logs setting",
                "field" => 'Authentication Timeout(s)',
                "value" => "",
                "notes" => "",
            ],
            [
                "header" => "System",
                "sub_menu" => "access logs setting",
                "field" => 'Face comparison Interval(s)',
                "value" => "",
                "notes" => "",
            ],
            [
                "header" => "System",
                "sub_menu" => "face",
                "field" => '1:N Threshold Value',
                "value" => "",
                "notes" => "",
            ],
            [
                "header" => "System",
                "sub_menu" => "face",
                "field" => '1:N Natch Threshold for Masked',
                "value" => "",
                "notes" => "",
            ],
            [
                "header" => "System",
                "sub_menu" => "face",
                "field" => '1:1 Threshold Value',
                "value" => "",
                "notes" => "",
            ],
            [
                "header" => "System",
                "sub_menu" => "detection management",
                "field" => 'detection management',
                "value" => "",
                "notes" => "",
            ],
            [
                "header" => "COMM.",
                "sub_menu" => "Ethernet",
                "field" => 'IP Address',
                "value" => "",
                "notes" => "",
            ],
            [
                "header" => "COMM.",
                "sub_menu" => "Ethernet",
                "field" => 'Subnet Mask',
                "value" => "",
                "notes" => "",
            ],
            [
                "header" => "COMM.",
                "sub_menu" => "Ethernet",
                "field" => 'Gateway',
                "value" => "",
                "notes" => "",
            ],
            [
                "header" => "COMM.",
                "sub_menu" => "Ethernet",
                "field" => 'DNS',
                "value" => "",
                "notes" => "",
            ],
            [
                "header" => "COMM.",
                "sub_menu" => "Ethernet",
                "field" => 'TCP COMM.Port',
                "value" => "",
                "notes" => "",
            ],
            [
                "header" => "COMM.",
                "sub_menu" => "Ethernet",
                "field" => 'DHCP',
                "value" => "",
                "notes" => "",
            ],
            [
                "header" => "COMM.",
                "sub_menu" => "Ethernet",
                "field" => 'Display in status bar',
                "value" => "",
                "notes" => "",
            ],
            [
                "header" => "COMM.",
                "sub_menu" => "Cloud Server Setting",
                "field" => 'Server Mode',
                "value" => "",
                "notes" => "",
            ],
            [
                "header" => "COMM.",
                "sub_menu" => "Cloud Server Setting",
                "field" => 'Enable Domain Name',
                "value" => "",
                "notes" => "",
            ],
            [
                "header" => "COMM.",
                "sub_menu" => "Cloud Server Setting",
                "field" => 'Server Address',
                "value" => "",
                "notes" => "",
            ],
            [
                "header" => "COMM.",
                "sub_menu" => "Cloud Server Setting",
                "field" => 'Enable Proxy Server',
                "value" => "",
                "notes" => "",
            ],
            [
                "header" => "COMM.",
                "sub_menu" => "Cloud Server Setting",
                "field" => 'HTTPS',
                "value" => "",
                "notes" => "",
            ],
        ];
    }
}
