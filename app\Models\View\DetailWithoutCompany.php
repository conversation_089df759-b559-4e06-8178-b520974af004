<?php

namespace App\Models\View;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\View\DetailWithoutCompany
 *
 * @method static \Illuminate\Database\Eloquent\Builder|DetailWithoutCompany newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DetailWithoutCompany newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DetailWithoutCompany query()
 * @mixin \Eloquent
 */
class DetailWithoutCompany extends Model
{
    use HasFactory;

    protected $connection = 'sqlsrv';
    protected $table = 'DetailWithoutCompany';
}
