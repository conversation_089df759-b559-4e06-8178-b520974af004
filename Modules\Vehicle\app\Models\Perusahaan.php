<?php

namespace Modules\Vehicle\app\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Modules\Vehicle\Database\factories\PerusahaanFactory;

class Perusahaan extends Model
{
    use HasFactory;

    protected $connection = 'sqlsrvsimper';
    protected $table = 'opt_perusahaan';
    protected $primaryKey = 'id_perusahaan'; 
    
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [];
    
    protected static function newFactory(): PerusahaanFactory
    {
        //return PerusahaanFactory::new();
    }
}
