<?php

namespace App\Models;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;

/**
 * App\Models\Transactions
 *
 * @property int $id
 * @property int $mapping_id
 * @property string $type
 * @property int $credential
 * @property int $applications_id
 * @property int $status
 * @property string $nik
 * @property string|null $created_at
 * @property string|null $updated_at
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection<int, \Illuminate\Notifications\DatabaseNotification> $notifications
 * @property-read int|null $notifications_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Laravel\Sanctum\PersonalAccessToken> $tokens
 * @property-read int|null $tokens_count
 * @method static \Illuminate\Database\Eloquent\Builder|Transactions newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Transactions newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Transactions query()
 * @method static \Illuminate\Database\Eloquent\Builder|Transactions whereApplicationsId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Transactions whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Transactions whereCredential($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Transactions whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Transactions whereMappingId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Transactions whereNik($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Transactions whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Transactions whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Transactions whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class Transactions extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    public $timestamps                          = false;
    protected $table                            = 'transactions';
    protected $primaryKey                       = 'id';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'dept_name',
        'app_name',
        'mapping_id',
        'type',
        'credential',
        'applications_id',
        'created_at',
        'status',
        'nik',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
    ];
    protected $guarded = [];
}
