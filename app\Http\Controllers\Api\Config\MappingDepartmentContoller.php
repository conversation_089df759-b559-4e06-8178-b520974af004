<?php

namespace App\Http\Controllers\Api\Config;

use App\Http\Controllers\Controller;
use App\Models\Settings\MappingDepartment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class MappingDepartmentContoller extends Controller
{
    public function index(Request $request){
        $tags    = [];
        $filter  = json_decode($request->get('filters'));
        $sorting = json_decode($request->get('sorting'));

        $dataMappingDept = MappingDepartment::select('m_mapping_dept.name',
            'm_mapping_dept.id as id',
            'department',
            'department_code',
            'face_list_name',
            'face_list_id',
            'acc_level',
            'nvr_id',
            'acc_level_code',
            'm_nvr.status as status_map',
            'm_mapping_dept.status as status',
            'm_nvr.name as nvr_name',
            'description',
            'flag'
        )
        ->where(function ($q) use ($filter){
            if($filter != ''){
                foreach ($filter as $tags) {
                    if($tags != null ){
                        if($tags->id == 'name'){
                            $q->where('m_mapping_dept.name','LIKE','%'.$tags->value.'%');
                        }
                        elseif($tags->id == 'nvr_name'){
                            $q->where('m_nvr.name','LIKE','%'.$tags->value.'%');
                        }
                        else{
                            if($tags->id == 'status'){
                                $val = ($tags->value == 'Active') ? 1 : 0;
                                $q->where('m_mapping_dept.status','LIKE','%'.$val.'%');
                            }else{
                                $val =  $tags->value;
                                $q->where($tags->id,'LIKE','%'.$val.'%');
                            }

                        }
                    }
                }
            }
        })
        ->leftJoin('m_nvr', 'm_nvr.id', 'm_mapping_dept.nvr_id')
        ->skip($request->get('start'))
        ->take($request->get('size'));
        if($sorting != ''){
            foreach ($sorting as $tags) {
                if($tags != null ){
                    $sort    = ($sorting[0]->desc) ? 'desc' : 'asc';
                    $dataMappingDept->orderBy($sorting[0]->id,$sort);
                }else{
                    $dataMappingDept->orderBy('m_mapping_dept.id','desc');
                }
            }
        }else{
            $dataMappingDept->orderBy('m_mapping_dept.id','desc');
        }

        $dataArray = array();
        foreach($dataMappingDept->get() as $res){
            $dataArray[] = array(
                'id'              => $res->id,
                'name'            => $res->name,
                'department'      => $res->department,
                'department_code' => explode(",",$res->department_code),
                'face_list_name'  => $res->face_list_name,
                'face_list_id'    => explode(",",$res->face_list_id),
                'nvr_id'          => $res->nvr_id,
                'acc_level'       => explode(",",$res->acc_level),
                'acc_level_code'  => explode(",",$res->acc_level_code),
                'status'          => $res->status,
                'nvr_name'        => $res->nvr_name,
                'description'     => $res->description,
                'flag'            => $res->flag
            );
        }

        $dataAll = MappingDepartment::orderBy('id','desc')->get();

        try{
            return response()->json([
                'status'   => true,
                'message'  => 'Success',
                'data'     => $dataArray,
                'total'    => $dataAll->count(),
                'response' =>  $request->all(),
            ], 200);
        } catch (\Throwable $th) {
            return response()->json([
                'status'  => false,
                'message' => $th->getMessage(),
                'data'    => []
            ], 500);
        }
    }

    public function store(Request $request){
        try {
            $validateUser = Validator::make(
                $request->all(),
                [
                    'name'            => 'required|unique:m_mapping_dept,name',
                    'department_code' => 'required',
                    'face_list_id'    => 'required',
                    'nvr_id'          => 'required',
                    'acc_level'       => 'required',
                    'flag'            => 'required',
                ]
            );

            if ($validateUser->fails()) {
                return response()->json([
                    'status'  => false,
                    'message' => 'validation error',
                    'errors'  => $validateUser->errors()
                ], 422);
            }

            $DepartmentName = $request->department;
            $deptNameList = array();
            foreach($DepartmentName as $dc){
                $departCheck = MappingDepartment::where('m_mapping_dept.department_code','LIKE','%'.$dc.'%')
                ->where('flag', 'GENERAL')
                ->where('status', 1)
                ->first();

                $deptNameList[] = $dc;

            }

            if($deptNameList AND $request->flag  == "GENERAL"){
                return response()->json([
                    'status'  => false,
                    'message' => 'Department '.implode(',',$deptNameList).' Registered!'
                ], 401);
            }

            $user = MappingDepartment::create([
                'name'            => $request->name,
                'department'      => implode(',', $request->department),
                'department_code' => implode(',', $request->department_code),
                'face_list_name'  => implode(',', $request->face_list_name),
                'face_list_id'    => implode(',', $request->face_list_id),
                'acc_level'       => implode(',', $request->acc_level),
                'acc_level_code'  => implode(',', $request->acc_level_code),
                'nvr_id'          => $request->nvr_id,
                'description'     => $request->description,
                'flag'            => $request->flag,
                'status'          => 1,
            ]);

            return response()->json([
                'status'  => true,
                'message' => 'Mapping Department Created Success'
            ], 200);
        } catch (\Throwable $th) {
            return response()->json([
                'status'  => false,
                'message' => $th->getMessage()
            ], 401);
        }
    }

     /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\MappingDept  $mappingDept
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $rules = [
            'name'            => 'required',
            'department_code' => 'required',
            'face_list_id'    => 'required',
            'nvr_id'          => 'required',
            'acc_level'       => 'required',
            'flag'            => 'required',
        ];

        if($request->name != $request->name_old){
            $rules['name'] = 'required|unique:m_mapping_dept,name';
        }

        $validatedData = $request->validate($rules);
        DB::beginTransaction();

        //update data nvr
        $data = MappingDepartment::where('id', $id)
        ->update(
            [
                'name'            => $request->name,
                'department'      => (is_array($request->department)) ? implode(',', $request->department) : $request->department,
                'department_code' => (is_array($request->department_code)) ? implode(',', $request->department_code) : $request->department_code,
                'face_list_name'  => (is_array($request->face_list_name)) ? implode(',', $request->face_list_name) : $request->face_list_name,
                'face_list_id'    => (is_array($request->face_list_id)) ? implode(',', $request->face_list_id) : $request->face_list_id,
                'acc_level'       => (is_array($request->acc_level)) ? implode(',', $request->acc_level) : $request->acc_level,
                'acc_level_code'  => (is_array($request->acc_level_code)) ? implode(',', $request->acc_level_code) : $request->acc_level_code,
                'nvr_id'          => $request->nvr_id,
                'description'     => $request->description,
                'flag'            => $request->flag,
                'status'          => $request->status,
            ]
        );

        if($data){
            DB::commit();
            return response()->json([
                'success' => true,
                'message' => $request->input('name').' update successfully !',
            ], 200);
        }else{
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => "Failed to update !"
            ], 401);
        }
    }

     /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($mappingDept, Request $request)
    {
        $data = MappingDepartment::find($mappingDept);
        $data->delete();
        return response()->json([
            'success' => true,
            'message' => $request->input('name').' successfully deleted !',
        ], 200);
    }
}
