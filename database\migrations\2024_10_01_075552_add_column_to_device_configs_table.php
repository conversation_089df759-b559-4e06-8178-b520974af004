<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('device_configs', function (Blueprint $table) {
            // Step 1: Add the column as nullable
            $table->string("device_id")->nullable();
        });

        // Step 2: Populate the 'device_id' column with unique incremental values
        DB::statement('
            WITH CTE AS (
                SELECT id, ROW_NUMBER() OVER (ORDER BY id) AS row_num
                FROM device_configs
            )
            UPDATE device_configs
            SET device_id = CTE.row_num
            FROM CTE
            WHERE device_configs.id = CTE.id
        ');

        // Step 3: Modify the column to be non-nullable and unique
        Schema::table('device_configs', function (Blueprint $table) {
            $table->string("device_id")->nullable(false)->unique()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('device_configs', function (Blueprint $table) {
            //
        });
    }
};
