<?php

namespace App\Services\Dashboard;

use Illuminate\Support\Facades\DB;

class SummaryEmployeesService
{

    /* Non-IMIP GROUP */
    public function getSummaryMasterHris()
    {
        $data = DB::connection('mysqlhris')
            ->select("
            select
                REPLACE(departementName, 'PT ', '') AS Alias,
                company AS Company,
                count(idNumber) as Total_Mst_Employees
            from vw_access_control A
            where isActive COLLATE utf8mb4_general_ci='YES'
            group by company, departementName, company
            order by departementName

        ");
        $result = [];
        foreach ($data as $value) {
            if (!str($value->Alias)->contains(["BDM", "BDT", "IMIP", "MSS"])) {
                $result[] = $value;
            }
        }

        return $result;
    }

    /* IMIP GROUP */
    public function getSummaryMasterCherry()
    {
        return DB::connection('sqlsrvcherry')
            ->select("
                SELECT
                    CASE 
                        WHEN A.Company LIKE 'PT %' THEN REPLACE(A.Company, 'PT ', '')
                        ELSE A.Company
                    END AS Alias,
                    CASE 
                        WHEN A.Company = 'PT IMIP' THEN 'PT. Indonesia Morowali Industrial Park'
                        WHEN A.Company = 'PT BDM' THEN 'PT. Bintang Delapan Mineral'
                        WHEN A.Company = 'PT BDT' THEN 'PT. Bintang Delapan Terminal'
                    END AS Company,
                    COUNT(*) as Total_Mst_Employees
                from vw_employee_masterdata A
                where IsActive='True'
                    and A.Company in ('PT BDM', 'PT BDT','PT IMIP')
                    and A.WorkLocation like '%MOROWALI%'
                group by A.Company
                order by A.Company
            ");
    }

    public function getSummaryZkbioTenant()
    {
        return DB::connection('sqlsrv')
            ->select("
                SELECT
                    *
                from vw_tenant_zkbio A
                order by A.alias
            ");
    }
    
    public function getSummaryZkbioImip()
    {
        return DB::connection('sqlsrv')
            ->select("
                SELECT
                    *
                from vw_imip_zkbio A
                order by A.alias
            ");
    }
}