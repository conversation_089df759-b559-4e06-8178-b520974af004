<?php

namespace App\Jobs\Service;

use Carbon\Carbon;
use Illuminate\Bus\Batch;
use App\Models\Applications;
use App\Models\Transactions;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use App\Models\Settings\MappingDepartment;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Contracts\Queue\ShouldBeUnique;

class PersonJob implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    public $data;
    private $response;
    /**
     * Create a new job instance.
     */
    public function __construct($data)
    {
        //
        $this->data = $data;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $checking         = Http::get(config('app.url_zkteco').'/api/person/get/'.$this->data['Id'].'?access_token='.config('app.token_zkteco'));
        $jsonDataChecking = $checking->json();

        if(isset($jsonDataChecking['data']) AND $this->data['Method'] == "POST"){
            $this->batch()->cancel();
        }else{
         
            if($this->data['Action'] == 'Disabled'){
                $response = Http::post(config('app.url_zkteco').'/api/person/add?access_token='.config('app.token_zkteco'), [
                    'pin'          => $this->data['Id'],
                    'isDisabled'   => true,
                    'accStartTime' => date('Y-m-d H:i:s'),
                    'accEndTime'   => date('Y-m-d H:i:s')
                ]);
            }
            elseif($this->data['Action'] == 'Active'){
                $response = Http::post(config('app.url_zkteco').'/api/person/add?access_token='.config('app.token_zkteco'), [
                    'pin'          => $this->data['Id'],
                    'deptCode'     => $this->data['DepartmentCode'],
                    'isDisabled'   => false,
                    'accStartTime' => null,
                    'accEndTime'   => null,
                    'accLevelIds'  => $this->data['DataMapping']['acc_level_code'],
                ]);
            }
            elseif($this->data['Action'] == 'Delete'){
                $response = Http::post(config('app.url_zkteco').'/api/person/delete/'.$this->data['Id'].'?access_token='.config('app.token_zkteco'));
            }
            else{
                $response = Http::post(config('app.url_zkteco').'/api/person/add?access_token='.config('app.token_zkteco'), [
                    'pin'         => $this->data['Id'],
                    'deptCode'    => $this->data['DepartmentCode'],
                    'name'        => $this->data['Name'],
                    'lastName'    => '',
                    'gender'      => '',
                    'cardNo'      => '',
                    'personPhoto' => $this->data['Images'],
                    'accLevelIds' => $this->data['DataMapping']['acc_level_code'],
                ]);
            }
        }
    }

    // public function getResponse()
    // {
    //     return $this->response;
    // }
}
