<?php

namespace Modules\Visitor\App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class VisitorRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'form.*.persPersonPin'      => 'required|string',
            'form.*.certType'           => 'required|string',
            'form.*.certNum'            => 'required|string',
            'form.*.visEmpName'         => 'required|string',
            'form.*.visitEmpPhone'      => 'required|string',
            'form.*.company'            => 'required|string',
            'form.*.visitReason'        => 'required|string',
            'form.*.visitorCount'       => 'required|string',
            'form.*.startTime'          => 'required|date_format:Y-m-d',
            'form.*.endTime'            => 'required|date_format:Y-m-d',
            'facePhoto'                 => 'required|string',
            'service'                   => 'required|string|in:ALL,Huawei,Zkbio',
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    protected function validateContentType()
    {
        $contentType                    = $this->header('Content-Type');

        if ($contentType !== 'application/json') {
            $this->validator->errors()->add('content_type', 'The content type must be application/json.');
        }
    }
}
