<?php

namespace App\Models\Settings;

use App\Models\Mapping\MappingAccess;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\Settings\FaceList
 *
 * @property int $id
 * @property string $nvr_name
 * @property string|null $nvr_ip
 * @property string $face_list_name
 * @property string $face_list_code
 * @property int $status
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int $nvr_id
 * @property int $face_list_id
 * @property-read mixed $organization
 * @property-read \Illuminate\Database\Eloquent\Collection<int, MappingAccess> $mappingAccess
 * @property-read int|null $mapping_access_count
 * @property-read \App\Models\Settings\Nvr|null $nvr
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Settings\FaceListOrganization> $organizationFaceList
 * @property-read int|null $organization_face_list_count
 * @method static \Illuminate\Database\Eloquent\Builder|FaceList newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|FaceList newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|FaceList onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|FaceList query()
 * @method static \Illuminate\Database\Eloquent\Builder|FaceList whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FaceList whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FaceList whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FaceList whereFaceListCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FaceList whereFaceListId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FaceList whereFaceListName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FaceList whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FaceList whereNvrId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FaceList whereNvrIp($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FaceList whereNvrName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FaceList whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FaceList whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FaceList whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FaceList withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|FaceList withoutTrashed()
 * @property int|null $face_list_type
 * @property-read mixed $organization_ids
 * @method static \Illuminate\Database\Eloquent\Builder|FaceList whereFaceListType($value)
 * @mixin \Eloquent
 */
class FaceList extends Model
{
    use HasFactory;
    use SoftDeletes;
    protected $guarded = [];
    public $appends = [
        'organization',
        'organization_ids'
    ];

    public function getOrganizationAttribute()
    {
        return $this->organizationFaceList()->get()->pluck('organization.alias')->implode(',');
    }

    public function getOrganizationIdsAttribute()
    {
        return $this->organizationFaceList()->get()->pluck('organization_id')->implode(',');
    }

    public function nvr()
    {
        return $this->belongsTo(Nvr::class, 'nvr_id', 'id');
    }

    public function organizationFaceList()
    {
        return $this->hasMany(FaceListOrganization::class, 'face_list_id', 'id');
    }

    public function mappingAccess()
    {
        return $this->hasMany(MappingAccess::class);
    }
}
