<?php

namespace App\Imports;

use App\Models\Mapping\FaceImage;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;

class FaceRecordImport implements ToCollection
{
    /**
     * @param Collection $collection
     */
    public function collection(Collection $collection)
    {
        $data = [];
        foreach ($collection as $row) {
            $faceImage = FaceImage::where('file_name', 'LIKE', '%' . $row[1] . '%')->first();
            $data[] = [
                'name' => $row[2],
                'credentialNumber' => $row[3],
                'credentialType' => $row[4],
                'gender' => $row[5],
                'bornTime' => $row[6],
                'country' => $row[7],
                'occupation' => $row[8],
                'description' => $row[9],
                'strId' => $row[3],
                'fileId' => $faceImage->file_id,
            ];

            // $batchQueue[] = new ProcessUploadFaceRecord($data, $faceImage);
        }
        return $data;
        // $batch = Bus::batch($batchQueue)
        //     ->catch(function (Batch $batch, Throwable $e) {
        //         Log::info('error upload batch ' . now(), [
        //             'error' => $e->getTraceAsString(),
        //         ]);
        //     })
        //     ->name('Upload Face Record ' . date('Y-m-d H:i:s'))
        //     ->onQueue('Medium')
        //     ->allowFailures()
        //     ->dispatch();
    }
}
