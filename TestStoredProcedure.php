<?php

// Test file untuk men<PERSON>ji stored procedure baru
// Jalankan dengan: php artisan tinker
// Kemudian copy paste kode ini

use App\Models\View\ViewVisitor;
use Illuminate\Support\Facades\Log;

// Test stored procedure baru
function testNewStoredProcedures()
{
    $startDate = '2025-06-01';
    $endDate = '2025-06-30';
    
    echo "=== Testing New Stored Procedures ===\n";
    echo "Date Range: {$startDate} to {$endDate}\n\n";
    
    try {
        // Test 1: Original stored procedure
        echo "1. Testing Original sp_ReportVisitorPivot:\n";
        $originalData = ViewVisitor::getVisitorReport($startDate, $endDate);
        echo "Records found: " . $originalData->count() . "\n";
        if ($originalData->count() > 0) {
            echo "First record: " . json_encode($originalData->first()) . "\n";
            echo "Columns: " . implode(', ', array_keys((array)$originalData->first())) . "\n";
        }
        echo "\n";
        
        // Test 2: New pivot stored procedure
        echo "2. Testing New sp_ReportVisitorPivotNew:\n";
        $newPivotData = ViewVisitor::getVisitorReportNew($startDate, $endDate);
        echo "Records found: " . $newPivotData->count() . "\n";
        if ($newPivotData->count() > 0) {
            echo "First record: " . json_encode($newPivotData->first()) . "\n";
            echo "Columns: " . implode(', ', array_keys((array)$newPivotData->first())) . "\n";
        }
        echo "\n";
        
        // Test 3: Simple stored procedure
        echo "3. Testing Simple sp_ReportVisitorSimple:\n";
        $simpleData = ViewVisitor::getVisitorReportSimple($startDate, $endDate);
        echo "Records found: " . $simpleData->count() . "\n";
        if ($simpleData->count() > 0) {
            echo "First record: " . json_encode($simpleData->first()) . "\n";
            echo "Sample records:\n";
            foreach ($simpleData->take(5) as $index => $record) {
                echo "  Record " . ($index + 1) . ": Company={$record->company}, Date={$record->created_at}, Total={$record->Total}\n";
            }
        }
        echo "\n";
        
        // Test 4: With summary stored procedure
        echo "4. Testing With Summary sp_ReportVisitorWithSummary:\n";
        $summaryData = ViewVisitor::getVisitorReportWithSummary($startDate, $endDate);
        echo "Records found: " . $summaryData->count() . "\n";
        if ($summaryData->count() > 0) {
            echo "Sample records by type:\n";
            $groupedData = $summaryData->groupBy('row_type');
            foreach ($groupedData as $type => $records) {
                echo "  {$type}: " . $records->count() . " records\n";
                if ($records->count() > 0) {
                    echo "    Sample: " . json_encode($records->first()) . "\n";
                }
            }
        }
        echo "\n";
        
    } catch (Exception $e) {
        echo "Error: " . $e->getMessage() . "\n";
        Log::error('Stored procedure test error', [
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
    }
}

// Jalankan test
testNewStoredProcedures();

// Contoh query manual untuk debugging
function testManualQuery()
{
    echo "=== Testing Manual Query ===\n";
    
    try {
        $results = DB::connection('sqlsrv')->select("
            SELECT  
                A.company,
                CAST(A.created_at AS DATE) AS created_at,
                COUNT(*) AS Total
            FROM log_transaction A
            WHERE A.application_id = 24
            AND A.service_code = '0'
            AND A.status = 3
            AND A.type = 'Zkbio'
            AND CAST(A.created_at AS DATE) BETWEEN '2025-06-01' AND '2025-06-30'
            GROUP BY A.company, CAST(A.created_at AS DATE)
            ORDER BY A.company, CAST(A.created_at AS DATE)
        ");
        
        echo "Manual query results: " . count($results) . " records\n";
        if (count($results) > 0) {
            echo "First 3 records:\n";
            foreach (array_slice($results, 0, 3) as $index => $record) {
                echo "  " . ($index + 1) . ". Company: {$record->company}, Date: {$record->created_at}, Total: {$record->Total}\n";
            }
        }
        
    } catch (Exception $e) {
        echo "Manual query error: " . $e->getMessage() . "\n";
    }
}

// Uncomment untuk test manual query
// testManualQuery();
