<?php

namespace App\Http\Controllers\Settings;

use App\Http\Controllers\Controller;
use App\Models\Settings\FaceList;
use App\Models\Settings\FaceListOrganization;
use App\Services\Settings\FaceListService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ConfigFaceListController extends Controller
{
    public FaceListService $service;

    public function __construct(FaceListService $service)
    {
        $this->service = $service;
        // $this->middleware(['direct_permission:Roles-index'])->only(['index', 'show', 'permissionRole']);
        // $this->middleware(['direct_permission:Roles-store'])->only(['store', 'storePermissionRole']);
        // $this->middleware(['direct_permission:Roles-edits'])->only('update');
        // $this->middleware(['direct_permission:Roles-erase'])->only('destroy');
    }

    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws \Exception
     */
    public function index(Request $request): JsonResponse
    {
        try {
            return $this->success('', $this->service->index($request));
        } catch (\Exception $exception) {
            throw new \Exception($exception->getMessage(), 1);
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return JsonResponse
     * @throws \Exception
     */
    public function store(Request $request): JsonResponse
    {
        DB::beginTransaction();
        try {
            $item = FaceList::create($this->service->formData($request, 'store'));

            $this->service->processItemDetails($request->organization_id, $item->id);
            // $this->processItemDetails($category, $item['id']);

            DB::commit();

            return $this->success('Data inserted!', [
                'errors' => false,
            ]);
        } catch (\Exception $exception) {
            DB::rollBack();
            throw new \Exception($exception->getMessage(), 1);
        }
    }


    /**
     * Display the specified resource.
     *
     * @param int $id
     *
     * @return JsonResponse
     */
    public function show($id): JsonResponse
    {
        $data = FaceList::where("id", "=", $id)->get();

        return $this->success('Success', [
            'data' => $data
        ]);
    }


    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     *
     * @return JsonResponse
     * @throws \Exception
     */
    public function update(Request $request, $id): JsonResponse
    {
        DB::beginTransaction();
        try {
            $header = FaceList::where('id', $request->id)
            ->update([
                'face_list_code' => $request->face_list_code,
                'face_list_id'   => $request->face_list_id,
                'face_list_name' => $request->face_list_name,
                'face_list_type' => $request->face_list_type,
                // 'nvr_name'       => $request->nvr['nvr_name'],
            ]);

            $header = FaceList::where('id', $request->id)->first();

            foreach ($request->organization_id as $key => $value) {
                $this->service->processItemUpdateDetails($value, $request->id);
            }

            DB::commit();

            return $this->success('Data updated!', [
                'errors' => false,
            ]);
        } catch (\Exception $exception) {
            DB::rollBack();

            throw new \Exception($exception->getMessage(), 1);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param Request $request
     * @param int $id
     *
     * @return JsonResponse
     * @throws \Exception
     */
    public function destroy(Request $request, $id): JsonResponse
    {
        DB::beginTransaction();
        try {
            $collect = collect($request->selected);
            $selected = $collect->pluck('id');
            FaceListOrganization::where("face_list_id", $id)->delete();
            $details = FaceList::where('id', $id)->forceDelete();

            DB::commit();
            return $this->success('Row deleted!', [
                'errors' => false,
            ]);
        } catch (\Exception $exception) {
            DB::rollBack();
            throw new \Exception($exception->getMessage(), 1);
        }
    }
}
