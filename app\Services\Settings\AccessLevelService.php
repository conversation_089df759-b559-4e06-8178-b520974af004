<?php

namespace App\Services\Settings;

use App\Traits\ApiResponse;
use Illuminate\Support\Arr;
use App\Models\Settings\FaceList;
use App\Models\Settings\AccessLevel;
use App\Models\Settings\AccessLevelOrganization;

class AccessLevelService
{
    use ApiResponse;

    /**
     * @param $request
     *
     * @return array
     */
    public function index($request): array
    {
        $options = json_decode($request->options, true);
        $pages = isset($options['page']) ? (int)($options['page'] + 1) : 1;
        $row_data = isset($options['rows']) ? (int)$options['rows'] : 20;
        $sorts = isset($options['sortField']) ? (string)$options['sortField'] : "access_level_name";
        $order = (!empty($options['sortOrder'])) ? 'desc' : 'asc';
        $search = isset($request->search) ? (string)$request->search : "";
        $filters = $options['filters'] ?? null;
        $filter = json_decode($request->get('filters'));


        $offset = ($pages - 1) * $row_data;

        $result = array();
        $query = AccessLevel::where(function ($q) use ($filter) {    
            if ($filter != '') {
                foreach ($filter as $tags) {
                    if ($tags != null) {   
                        $q->where( $tags->id, 'LIKE', '%' . $tags->value . '%');
                    }
                }
            }
        });

        $result["total"] = $query->count();

        $result['data'] = $query
            ->orderBy($sorts, $order)
            ->skip($request->get('start'))
            ->take($request->get('size'))
            ->limit(20)
            ->get()
            ->map(function ($item) {
                $item->access_level_code = explode(",", $item->access_level_code);
                return $item;
            })
            ->map(function ($item) {
                $item->organization_id = array_map('intval', explode(",", $item->organization_ids));
                return $item;
            })
            ->map(function ($item) {
                $item->department_names = explode(",", $item->department_name);
                return $item;
            });

        $result['form'] = $this->getForm();
        $roles = AccessLevel::select('id', 'access_level_name')->get();
        $selectRoles = [];

        foreach ($roles as $role) {
            $selectRoles[] = [
                'value' => $role->id,
                'label' => $role->name,
            ];
        }
        $result['select'] = $selectRoles;
        $result['header'] = $this->frontEndHeader();

        $collect = collect($result);

        return $collect->all();
    }

    /**
     * @return array
     */
    public function getForm(): array
    {
        $form = $this->form('roles');
        $form['id'] = 0;

        return $form;
    }

    /**
     * @param $request
     * @param $type
     *
     * @return array
     */
    public function formData($request, $type): array
    {
        $data = $request->all();

        Arr::forget($data, 'id');
        Arr::forget($data, 'created_at');
        Arr::forget($data, 'updated_at');
        Arr::forget($data, 'organization_id');
        Arr::forget($data, 'ACTIONS');

        if ($type == 'store') {
           
            $data['created_by']        = $request->user()->id;
            $data['access_level_id']   = $this->generateAccessLevelCode();
            $data['access_level_name'] = is_array($request->access_level_name) ? implode(',', $request->access_level_name) : $request->access_level_name;
            $data['access_level_code'] = implode(',', $request->access_level_code);
            $data['department_name'] = is_array($request->department_names) ? implode(',', $request->department_names) : $request->department_names;
        }

        if ($type == 'update') {
           
            $data['updated_by']        = $request->user()->id;
            $data['access_level_name'] = is_array($request->access_level_name) ? implode(',', $request->access_level_name) : $request->access_level_name;
            $data['access_level_code'] = implode(',', $request->access_level_code);
            $data['department_name']   = implode(',', $request->department_names);
        }

        Arr::forget($data, 'department_names');
        Arr::forget($data, 'organization');
        Arr::forget($data, 'organization_ids');

        foreach ($data as $key => $value) {
            if (strpos($key, '/api/') === 0) {
                unset($data[$key]);
            }
        }

        return $data;
    }

    public function generateAccessLevelCode()
    {
        $acces_level = AccessLevel::orderBy('id', 'desc')->first();
        if ($acces_level) {
            return ++$acces_level->access_level_id;
        }
        return 'AC000001';
    }


    public function processItemDetails($organizations, $access_level_id)
    {
        foreach ($organizations as $value) {
            AccessLevelOrganization::create([
                'access_level_id' => $access_level_id,
                'organization_id' => $value
            ]);
        }
    }
    

    public function processItemUpdateDetails($organizations, $access_level_id )
    {   
        $detail = AccessLevelOrganization::where("access_level_id", $access_level_id)
        ->where("organization_id", $organizations)
        ->first();
         
        if (!$detail) {
            AccessLevelOrganization::create([
                'access_level_id' => $access_level_id,
                'organization_id' => $organizations
            ]);
        }
    }

    public function frontEndHeader()
    {
        return [
            [
                "accessorKey" => 'access_name',
                "header" => 'Access Name',
            ],
            [
                "accessorKey" => 'access_level_name',
                "header" => 'Access Level Name',
            ],
            [
                "accessorKey" => 'access_level_id',
                "header" => 'Access Level ID',
            ],
            [
                "accessorKey" => 'organization',
                "header" => 'Organization',
            ],
            [
                "accessorKey" => 'status',
                "header" => 'Status',
            ],
        ];
    }
}
