<?php

namespace App\Traits;

use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

trait ApiResponse
{
    /**
     * Return a success JSON response.
     *
     * @param  array|string  $data
     * @param  string|null  $message
     * @param  int  $code
     * @return JsonResponse
     */
    protected function success(string $message = null, $data, int $code = 200): JsonResponse
    {
        $collection = collect([
            'status' => 'Success',
            'message' => $message,
            'locale' => session('locale'),
        ]);

        $merge = $collection->merge($data);

        return response()->json($merge->all(), $code);
    }

    /**
     * Return an error JSON response.
     *
     * @param  string|null  $message
     * @param  int  $code
     * @param array|string|null $data
     *
     * @return JsonResponse
     */
    protected function error(string $message = null, array|string $data = null, int $code = 422): JsonResponse
    {
        return response()->json([
            'status' => 'Error',
            'message' => $message,
            'data' => $data,
            'locale' => session('locale'),
        ], $code);
    }

    /**
     * @param $table
     * @return array
     */
    protected function form($table): array
    {
        $forms = DB::getSchemaBuilder()->getColumnListing($table);
        $arr_form = [];
        foreach ($forms as $form) {
            if (!Str::contains($form, ['created_at', 'updated_at'])) {
                $arr_form[$form] = null;
            }
        }

        return $arr_form;
    }

    /**
     * @param $table
     * @param $column
     * @return array
     */
    public function getEnumValues($table, $column): array
    {
        $type = DB::select(DB::raw("SHOW COLUMNS FROM $table WHERE Field = '{$column}'"))[0]->Type;
        preg_match('/^enum\((.*)\)$/', $type, $matches);
        $items = [];
        foreach (explode(',', $matches[1]) as $value) {
            $items[] = trim($value, "'");
        }

        return $items;
    }
}
