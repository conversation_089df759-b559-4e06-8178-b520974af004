<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
// use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;


/**
 * App\Models\Configapi
 *
 * @property int $id
 * @property string $api_type
 * @property string $api_url
 * @property string $api_key
 * @property int $api_status
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property string|null $created_at
 * @property string|null $updated_at
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection<int, \Illuminate\Notifications\DatabaseNotification> $notifications
 * @property-read int|null $notifications_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Laravel\Sanctum\PersonalAccessToken> $tokens
 * @property-read int|null $tokens_count
 * @method static \Illuminate\Database\Eloquent\Builder|Configapi newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Configapi newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Configapi query()
 * @method static \Illuminate\Database\Eloquent\Builder|Configapi whereApiKey($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Configapi whereApiStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Configapi whereApiType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Configapi whereApiUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Configapi whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Configapi whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Configapi whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Configapi whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Configapi whereUpdatedBy($value)
 * @mixin \Eloquent
 */
class Configapi extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    public $timestamps                          = false;
    protected $table                            = 'configapi';
    protected $primaryKey                       = 'id';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'token',
        'link_url',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        // 'password',
        // 'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
    ];
    protected $guarded = [];
}
