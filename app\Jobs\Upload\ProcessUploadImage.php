<?php

namespace App\Jobs\Upload;

use App\Jobs\RemoveAttachment;
use App\Models\Mapping\FaceImage;
use App\Models\Settings\Nvr;
use App\Services\Upload\BatchUploadService;
use App\Traits\NvrCookieHelper;
use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Request;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessUploadImage implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use Batchable;
    use NvrCookieHelper;

    public $file;
    public $file_name;
    public $nvr_id;
    public $user_id;
    /**
     * Create a new job instance.
     */
    public function __construct($file_name, $nvr_id, $user_id)
    {
        $this->file_name = $file_name;
        $this->nvr_id = $nvr_id;
        $this->user_id = $user_id;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $service = new BatchUploadService();
        $service->validateNvrCookie($this->nvr_id);
        $nvr = Nvr::find($this->nvr_id);
        $nvrName = str_replace(' ', '_', $nvr->name);

        // $file = Storage::get($this->file_name);
        $cookieNvr = $this->getConfigNvr($nvrName, 'cookie ' . $nvrName);


        $client = new Client([
            'verify' => false
        ]);
        $headers = [
            'Cookie' => 'JSESSIONID=' . $cookieNvr
        ];

        Log::info('Upload images to nvr ' . $nvr->ip, [
            'image' => count($this->file_name)
        ]);
        $request = new Request('POST', $nvr->ip . '/sdk_service/rest/image-library/upload-files', $headers);
        $res = $client->sendAsync($request, $this->file_name)->wait();
        $data = json_decode($res->getBody(), true);

        if ($data) {
            if (array_key_exists('file-list', $data)) {
                Log::info('Success upload images to nvr ' . $nvr->ip, [
                    'data' => count($data['file-list'])
                ]);
                foreach ($data['file-list'] as $key => $value) {
                    FaceImage::updateOrCreate([
                        'file_name' => $value['fileName'],
                        'nvr_id' => $this->nvr_id,
                    ], [
                        'file_id' => $value['fileId'],
                        'created_by' => $this->user_id
                    ]);
                    RemoveAttachment::dispatch(storage_path($nvrName . '/' . $value['fileName']))->delay(30);
                }
            } else {
                Log::info('failed get file list', [
                    'data' => $data
                ]);
            }
        } else {
            Log::info('failed upload files', [
                $data
            ]);
        }
    }
}
