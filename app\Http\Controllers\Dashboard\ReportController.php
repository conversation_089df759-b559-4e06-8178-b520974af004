<?php

namespace App\Http\Controllers\Dashboard;

use App\Exports\DashboardReportExport;
use App\Http\Controllers\Controller;
use App\Services\Dashboard\SummaryEmployeeService;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

class ReportController extends Controller
{
    public function store(Request $request)
    {
        $service = new SummaryEmployeeService();
        $cherry = collect($service->getSummaryCherry());
        $hris = collect($service->getSummaryHris());
        $zkbio = collect($service->getSummaryZkbio());

        $query = collect([])
            ->concat($zkbio->map(function ($item) use ($cherry, $hris) {
                // Find matching company in cherry and hris
                $cherryMatch = $cherry->firstWhere('company', $item->company);
                $hrisMatch = $hris->firstWhere('company', $item->company);

                // Calculate Selisih based on the condition
                $selisih = null;
                if ($cherryMatch) {
                    $selisih = $cherryMatch->total - $item->total;
                } elseif ($hrisMatch) {
                    $selisih = $hrisMatch->total - $item->total;
                }

                return [
                    'CompanyZkbio' => $item->company,
                    'TotalZkbio' => $item->total,
                    'CompanyHris' => $hrisMatch ? $hrisMatch->company : null,
                    'TotalHris' => $hrisMatch ? $hrisMatch->total : null,
                    'CompanyCherry' => $cherryMatch ? $cherryMatch->company : null,
                    'TotalCherry' => $cherryMatch ? $cherryMatch->total : null,
                    'Difference' => $selisih,
                    'Notes' => ''
                ];
            }));
        return Excel::download(new DashboardReportExport($query), "acc report.xlsx");
    }
}
