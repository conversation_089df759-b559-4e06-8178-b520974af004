<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('device_configs', function (Blueprint $table) {
            $table->string('device_location')->nullable();
            $table->unsignedSmallInteger('device_type')->nullable(); // 1: Person, 2: Vehicle
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('device_configs', function (Blueprint $table) {
            //
        });
    }
};
