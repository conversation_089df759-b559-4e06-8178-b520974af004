<?php

namespace Modules\ExternalApi\app\Jobs;

use App\Models\Settings\Nvr;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use App\Models\Settings\Persons;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Illuminate\Queue\SerializesModels;
use App\Models\Settings\Log_transaction;
use Illuminate\Queue\InteractsWithQueue;
use App\Services\ProcessDataPersonService;
use App\Services\Upload\BatchUploadService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class ProcessDeletePersonModule implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use Batchable;

    public $chunks;
    public $batchId;
    public Nvr $nvr;

    /**
     * Create a new job instance.
     */
    public function __construct($chunks, Nvr $nvr)
    {
        $this->chunks = $chunks;
        $this->nvr = $nvr;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $dataPersonService = new ProcessDataPersonService();
        $batchUploadService = new BatchUploadService();
        foreach ($this->chunks as $chunk) {

            $credential_number = ($chunk['oldCredentialNumber'] != '') ? $chunk['oldCredentialNumber'] : $chunk['credentialNumber'];
            $company           = ($chunk['oldCompany'] != '') ? $chunk['oldCompany'] : $chunk['company'];
            $faceLists         = $dataPersonService->getFaceList($this->nvr->id, $company);

            
            if ($faceLists) {
                foreach ($faceLists as $key => $item) {
                    $paramsPeople = [
                        'groupids' => $item->face_list_id,
                        'credentialNumber' => $credential_number,
                        'page' => [
                            "no" => "1",
                            "size" => "1",
                            "sort" => "asc",
                            "orderName" => "name"
                        ]
                    ];

                    $batchUploadService->validateNvrCookie($this->nvr->id);
                    $nvrName = str_replace(' ', '_', $this->nvr->name);
                    $cookieNvr = $batchUploadService->getConfigNvr($nvrName, 'cookie ' . $nvrName);

                    $detailPeople = Http::withoutVerifying()
                        ->withOptions(['verify' => false])
                        ->withHeaders([
                            'Accept' => 'application/json',
                            'Cookie' => 'JSESSIONID=' . $cookieNvr,
                        ])
                        ->post($this->nvr->ip . '/sdk_service/rest/facerepositories/peoples', $paramsPeople);

                    $data = json_decode($detailPeople, true);

                    if ($data['total'] != 0) {
                        $people = $data['peopleList'];
                        $collection = collect($people);
                        $paramsId = json_encode($collection->pluck('peopleId'));
                        $peopleIds = json_decode($paramsId, true)[0];

                        $url = $this->nvr->ip . '/sdk_service/rest/facerepositories/' . $item->face_list_id . '/peoples?ids=' . $peopleIds . '&credentialnumbers=' . $credential_number;

                        $response = Http::withoutVerifying()
                            ->withOptions(['verify' => false])
                            ->withHeaders([
                                'Accept' => 'application/json',
                                'Cookie' => 'JSESSIONID=' . $cookieNvr,
                            ])->delete($url);

                        Log::info('Data response of delete NVR', [
                            'data response' => $response
                        ]);

                        
                        if(isset($response->collect()['resultCode'])){
                            $code = $response->collect()['resultCode'];
                            $message = $response->collect()['resultMsg'];
                        }else{
                            $responseData = simplexml_load_string($response->body()); 
                            $responseArray = json_decode(json_encode($responseData), true); 

                            if (isset($responseArray['result']['code'])) {
                                $code = $responseArray['result']['code'];
                                $message = $responseArray['result']['errmsg'];
                            } else {
                                $code = $responseArray['code'] ?? null;
                                $message = $responseArray['errmsg'] ?? 'Unknown error message';
                            }

                        }

                        $paramSearch = [
                            'credential_number' => $chunk['credentialNumber']
                        ];

                        $paramPerson = [
                            'name'              => preg_replace('/-+/', '', $chunk['name']),
                            'company'           => $chunk['company'],
                            'department'        => $chunk['department'],
                            'photo'             => $chunk['photoName'],
                            'status'            => $chunk['statusEmployee'],
                        ];

                
                        Persons::updateOrCreate($paramSearch, $paramPerson);
                       
                        $log_transaction = Log_transaction::create([
                            'credential_number' => $credential_number,
                            'credential_type'   => $chunk['credentialType'],
                            'name'              => $chunk['name'],
                            'company'           => $company,
                            'type'              => 'HUAWEI', // HUAWEI & ZKBIO
                            'nvr_id'            => $this->nvr->id,
                            'face_list_id'      => $item->face_list_id,
                            'access_level_code' => null,
                            'status'            => $chunk['statusEmployee'],
                            'application_id'    => $chunk['application_id'],
                            'service_code'      => $code,
                            'service_message'   => $message,
                            'service_payload'   => '',
                            'batch_id'          => $this->batch()->id,
                            'access_level_name' => null,
                            'log_type'          => 'PERSON',
                            'method'            => 'DELETE'
                        ]);
                    }else{
                        Log_transaction::create([
                            'credential_number' => $credential_number,
                            'credential_type'   => $chunk['credentialType'],
                            'name'              => $chunk['name'],
                            'company'           => $company,
                            'type'              => 'HUAWEI', // HUAWEI & ZKBIO
                            'nvr_id'            => $this->nvr->id,
                            'face_list_id'      => 0,
                            'access_level_code' => null,
                            'status'            => $chunk['statusEmployee'],
                            'application_id'    => $chunk['application_id'],
                            'service_code'      => 'EM404',
                            'service_message'   => 'Employee Not Found',
                            'service_payload'   => null,
                            'batch_id'          => $this->batch()->id,
                            'access_level_name' => null,
                            'log_type'          => 'PERSON',
                            'method'            => 'DELETE',
                        ]);
                    }
                }
            } else {

                $paramSearch = [
                    'credential_number' => $chunk['credentialNumber']
                ];

                $paramPerson = [
                    'name'              => preg_replace('/-+/', '', $chunk['name']),
                    'company'           => $chunk['company'],
                    'department'        => $chunk['department'],
                    'photo'             => $chunk['photo'],
                    'status'            => $chunk['statusEmployee'],
                ];

                Persons::updateOrCreate($paramSearch, $paramPerson);

                Log_transaction::create([
                    'credential_number' => $credential_number,
                    'credential_type'   => $chunk['credentialType'],
                    'name'              => $chunk['name'],
                    'company'           => $company,
                    'type'              => 'HUAWEI', // HUAWEI & ZKBIO
                    'nvr_id'            => $this->nvr->id,
                    'face_list_id'      => 0,
                    'access_level_code' => null,
                    'status'            => $chunk['statusEmployee'],
                    'application_id'    => $chunk['application_id'],
                    'service_code'      => 'FL404',
                    'service_message'   => 'Mapping Face List Not Found',
                    'service_payload'   => null,
                    'batch_id'          => $this->batch()->id,
                    'access_level_name' => null,
                    'log_type'          => 'PERSON',
                    'method' => 'DELETE',
                ]);
            }
        }
    }
}
