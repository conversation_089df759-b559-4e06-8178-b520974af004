<?php

namespace Modules\Vehicle\app\Services;

use App\Models\Applications;
use App\Models\Settings\AccessLevel;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;

class TransformDataApiService
{
    /**
     * @param string $service
     * @param array $form
     * @param string $accessType
     * @return array
     */
    public function transform(string $service, array $form, $application, $accessType): array
    {
        if ($service == 'Huawei') {
            return $this->transformHuawei($form, $application);
        } elseif ($service == 'Zkbio') {
            return $this->transformZkbio($form, $application, $accessType);
        } elseif ($service == 'ZkbioDelete') {
            return $this->transformZkbioDelete($form, $application, $accessType);
        }else {
            return [];
        }
    }

    /**
     * @param array $form
     * @param Applications $application
     * @return array
     */
    protected function transformHuawei(array $form, Applications $application): array
    {
        $data = [];
        foreach ($form as $item) {
            $data[] = [
                'photoName'        => $item['photo'],
                'name'             => preg_replace('/[^A-Za-z0-9 !@#$%^&*().\-]/u', '', strip_tags($item['name'])),
                'credentialNumber' => $item['identifyNumber'],
                'strId'            => $item['identifyNumber'],
                'credentialType'   => ($item['type'] == 'ID Card') ? 0 : 1,
                'gender'           => (Str::contains($item['gender'], ['MALE', 'Male'])) ? 0 : 1,
                'bornTime'         => date("Y-m-d", strtotime($item['birthDate'])),
                'country'          => $item['nationality'],
                'occupation'       => $item['employeeType'],
                'description'      => $item['description'],
                'company'          => $item['company'],
                'base64'           => $item['base64'],
                'application_id'   => $application->id,
                'statusEmployee'   => (array_key_exists('statusEmployee', $item)) ? $item['statusEmployee'] : '3',

            ];
        }

        return $data;
    }

    /**
     * @param array $form
     * @param Applications $application
     * @param  string $accessType
     * @return array
     */
    protected function transformZkbio(array $form, Applications $application, $accessType): array
    {
        $data = [];
        foreach ($form as $item) {

            $accessLevel = $this->getAccessLevel($item['company'], $item['departmentName'], $accessType, $item['identifyNumber']);
            if(isset($accessLevel)){
              $access_type = $accessLevel['access_type'];
            }else{
               $access_type = '';
            }
           
            $data[]      = [
                'name'             => preg_replace('/[^A-Za-z0-9 !@#$%^&*().\-]/u', '', strip_tags($item['name'])),
                'credentialNumber' => $item['identifyNumber'],
                'cardNumber'       => $item['cardNumber'],
                'company'          => $item['company'],
                'dept_name'        => $item['departmentName'],
                'accStartTime'     => $item['accStartTime'],
                'accEndTime'       => $item['accEndTime'],
                'application_id'   => $application->id,
                'accessMapping'    => $item['accessMapping'],
                'access_type'      => $access_type,
                'statusVehicle'    => $item['statusVehicle'],
                'base64'           => $item['base64'],
                'leave_date'       => array_key_exists('leaveDate', $item) ? $item['leaveDate'] : null,
                'statusEmployee'   => (array_key_exists('statusEmployee', $item)) ? $item['statusEmployee'] : '3',
            ];
        }

        return $data;
    }

    /**
     * @param array $form
     * @param Applications $application
     * @param  string $accessType
     * @return array
     */
    protected function transformZkbioDelete(array $form, Applications $application, $accessType): array
    {
        $data = [];
        foreach ($form as $item) {
            $data[]      = [
                'name'             => preg_replace('/[^A-Za-z0-9 !@#$%^&*().\-]/u', '', strip_tags($item['name'])),
                'credentialNumber' => $item['identifyNumber'],
                'company'          => $item['company'],
                'isDisabled'       => $item['isDisabled'],
                'accStartTime'     => $item['accStartTime'],
                'accEndTime'       => $item['accEndTime'],
                'application_id'    => $application->id
            ];
        }

        return $data;
    }

    public function getAccessLevel($organizationName, $departmentName, $accessType, $identifyNumber)
    {
        $accessLevels = AccessLevel::whereHas('organizationAccessLevel.organization', function (Builder $query) use ($organizationName) {
            $query->where('name', 'LIKE', '%' . $organizationName . '%');
        })
            ->whereHas('accessType', function (Builder $query) use ($accessType) {
                $query->where('name', 'LIKE', '%' . $accessType . '%');
            })
            ->with(['accessType'])
            ->get();

        foreach ($accessLevels as $key => $accessLevel) {
            if ($accessLevel->department_name == 'ALL') {
                if ((int) $identifyNumber % 2 == 0 && Str::contains($accessLevel->access_level_name, 'GENAP')) {
                    return [
                        "access_level_code" => $accessLevel->access_level_code,
                        "access_level_name" => $accessLevel->access_level_name,
                        "access_type" => $accessLevel->accessType->name,
                    ];
                } else {
                    return [
                        "access_level_code" => $accessLevel->access_level_code,
                        "access_level_name" => $accessLevel->access_level_name,
                        "access_type" => $accessLevel->accessType->name,
                    ];
                }
            } else {
                $dept = explode(',', $accessLevel->department_name);
                if (Str::contains($departmentName, $dept) && (int) $identifyNumber % 2 == 0 && Str::contains($accessLevel->access_level_name, 'GENAP')) {
                    return [
                        "access_level_code" => $accessLevel->access_level_code,
                        "access_level_name" => $accessLevel->access_level_name,
                        "access_type" => $accessLevel->accessType->name,
                    ];
                } else {
                    return [
                        "access_level_code" => $accessLevel->access_level_code,
                        "access_level_name" => $accessLevel->access_level_name,
                        "access_type" => $accessLevel->accessType->name,
                    ];
                }
            }

        }

    }
}
