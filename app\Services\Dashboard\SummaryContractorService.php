<?php

namespace App\Services\Dashboard;

use Illuminate\Support\Facades\DB;

class SummaryContractorService
{
    public function getSummaryMaster()
    {
        return DB::connection('sqlsrvcontractor')
            ->select("
                SELECT
                    *
                from vw_summary_contractor A
                order by A.alias
            ");
    }

    public function getSummaryZkbio()
    {
        return DB::connection('sqlsrv')
            ->select("
                SELECT
                    *
                from vw_contractor_zkbio A
                order by A.alias
            ");
    }
}