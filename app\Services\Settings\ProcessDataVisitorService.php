<?php

namespace App\Services\Settings;

use App\Helpers\DepartmentCode;
use App\Models\Applications;
use App\Models\Settings\FaceList;
use App\Models\Settings\Log_transaction;
use App\Models\Settings\Nvr;
use App\Models\Settings\Organization;
use App\Models\Settings\Setting;
use App\Services\Upload\BatchUploadService;
use App\Traits\NvrCookieHelper;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\Facades\Image;

class ProcessDataVisitorService
{
    use NvrCookieHelper;

    /**
     * @param int $nvr_id
     * @param string $company
     * @return mixed
     */
    protected function processUploadVisitor(string $accessToken, array $row, string $upload, string $params): void
    {
        $params = [
            'persPersonPin' => '88102635',
            'certType' => 1,
            'certNum' => '88102232',
            'visitEmpName' => 'Testing Data Lagi',
            'visitEmpPhone' => '082187424269',
            'company' => 'IMIP',
            'visitReason' => 'visit',
            'visitorCount' => '1',
            'startTime' => '2024-05-11',
            'endTime' => '2024-05-11',
            'cardNo' => '666666',
            'visLevels' => '4028d8877f6247e3017f90af24304e0c'
        ];

        // $urlAddvisitor                      = Setting::getSetting('UrlAddVisitorZkbio');
        $accessToken = '6B0EEB2884FAC222E8AA11D6BCD956C2C87C1A957545A40D881117116A1E6D88';
        $urlAddvisitor = 'http://***************:8098/api/visRegistration/add';
        $upload = Http::withoutVerifying()
            ->withOptions(['verify' => false])
            ->withHeaders([
                'Content-Type' => 'application/json',
            ])
            ->post($urlAddvisitor . '?&access_token=' . $accessToken, $params);

        // Log::info('Result from upload access level zkbio', [
        //     'json'                          => $upload->json(),
        //     'url'                           => $urlAddvisitor
        // ]);


        // $this->createLogAccessLevel(employee: $row, new_data_level: $new_data_level, upload: $upload, params: $params, submitted: true, message: '', batch_id: $batch_id);
    }

    protected function createLogAccessLevel(array $employee, $new_data_level, $upload, $params, bool $submitted = true, string $message = '', $batch_id): void
    {
        if (!$submitted) {
            $code = '';
        } else {
            $code = ($upload->collect()->has('code')) ? $upload->collect()['code'] : (
                ($upload->collect()->has('ret')) ? $upload->collect()['ret'] : -1
            );
            $message = $upload->collect()['message'];
        }
        Log_transaction::create([
            'credential_number' => $employee['pid'],
            'credential_type' => $employee['credentialType'],
            'name' => $employee['data_name'],
            'company' => $employee['company'],
            'type' => 'ZKBIO', // HUAWEI & ZKBIO
            'nvr_id' => null,
            'face_list_id' => null,
            'access_level_code' => $new_data_level,
            'status' => 1,
            'application_id' => $employee['application_id'],
            'service_code' => $code,
            'service_message' => $message,
            'service_payload' => json_encode(collect($params)),
            'batch_id' => $batch_id,
            'access_level_name' => $employee['access_level_name']
        ]);
    }
}
