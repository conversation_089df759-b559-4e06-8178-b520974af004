<?php

namespace App\Http\Controllers\Settings;

use App\Http\Controllers\Controller;
use App\Http\Requests\Settings\OrganizationTypeStoreRequest;
use App\Models\Settings\OrganizationType;
use App\Services\Settings\OrganizationTypeService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class OrganizationTypeController extends Controller
{
    public OrganizationTypeService $service;

    public function __construct(OrganizationTypeService $service)
    {
        $this->service = $service;
        // $this->middleware(['direct_permission:Roles-index'])->only(['index', 'show', 'permissionRole']);
        // $this->middleware(['direct_permission:Roles-store'])->only(['store', 'storePermissionRole']);
        // $this->middleware(['direct_permission:Roles-edits'])->only('update');
        // $this->middleware(['direct_permission:Roles-erase'])->only('destroy');
    }

    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws \Exception
     */
    public function index(Request $request): JsonResponse
    {
        try {
            return $this->success('', $this->service->index($request));
        } catch (\Exception $exception) {
            throw new \Exception($exception->getMessage(), 1);
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return JsonResponse
     * @throws \Exception
     */
    public function store(OrganizationTypeStoreRequest $request): JsonResponse
    {
        DB::beginTransaction();
        try {
            $item = OrganizationType::create($this->service->formData($request, 'store'));

            // $this->processItemDetails($category, $item['id']);

            DB::commit();

            return $this->success('Data inserted!', [
                'errors' => false,
            ]);
        } catch (\Exception $exception) {
            DB::rollBack();
            throw new \Exception($exception->getMessage(), 1);
        }
    }


    /**
     * Display the specified resource.
     *
     * @param int $id
     *
     * @return JsonResponse
     */
    public function show($id): JsonResponse
    {
        $data = OrganizationType::where("id", "=", $id)->get();

        return $this->success('Success', [
            'data' => $data
        ]);
    }


    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     *
     * @return JsonResponse
     * @throws \Exception
     */
    public function update(Request $request, $id): JsonResponse
    {
        DB::beginTransaction();
        try {
            $data = OrganizationType::find($id);
            $forms = collect($this->service->formData($request, 'update'));
            //return $this->error('', 422, [$forms]);
            foreach ($forms as $index => $form) {
                $data->$index = $form;
            }
            $data->save();

            // $this->processItemDetails($category, $id);

            DB::commit();

            return $this->success('Data updated!', [
                'errors' => false,
            ]);
        } catch (\Exception $exception) {
            DB::rollBack();

            throw new \Exception($exception->getMessage(), 1);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param Request $request
     * @param int $id
     *
     * @return JsonResponse
     * @throws \Exception
     */
    public function destroy(Request $request, $id): JsonResponse
    {
        try {
            $collect = collect($request->selected);
            $selected = $collect->pluck('id');

            $details = OrganizationType::where('id', $id)->delete();

            return $this->success('Row deleted!', [
                'errors' => false,
            ]);
        } catch (\Exception $exception) {
            throw new \Exception($exception->getMessage(), 1);
        }
    }
}
