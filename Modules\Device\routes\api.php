<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Modules\Device\App\Http\Controllers\DeviceController;

/*
    |--------------------------------------------------------------------------
    | API Routes
    |--------------------------------------------------------------------------
    |
    | Here is where you can register API routes for your application. These
    | routes are loaded by the RouteServiceProvider within a group which
    | is assigned the "api" middleware group. Enjoy building your API!
    |
*/

Route::group(['middleware' => ['external.app'], 'prefix' => 'v1'], function () {
    Route::get('device', [DeviceController::class, 'show']); 
    Route::get('device/getAllPos', [DeviceController::class, 'getAllPos']);
    Route::get('device/getAllArea', [DeviceController::class, 'getAllArea']);
    Route::get('device/getDeviceType', [DeviceController::class, 'getDeviceType']);
    Route::post('device/openGate', [DeviceController::class, 'openGate']);
    Route::post('device/closeGate', [DeviceController::class, 'closeGate']);
});