<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('device_config_details', function (Blueprint $table) {
            $table->ulid('id')->primary();
            $table->ulid('device_config_id');
            $table->string('header');
            $table->string('sub_menu');
            $table->string('field');
            $table->string('value');
            $table->string('notes');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('device_config_details');
    }
};
