<?php

namespace Tests\Unit\Http\Controllers\Dashboard;

use Tests\TestCase;
use App\Models\SummaryLog;
use App\Http\Controllers\Dashboard\SummaryController;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;

class SummaryControllerTest extends TestCase
{
    use RefreshDatabase;

    private SummaryController $controller;

    protected function setUp(): void
    {
        parent::setUp();
        $this->controller = new SummaryController();
    }

    public function test_index_returns_all_summary_logs()
    {
        // Arrange
        SummaryLog::factory()->count(3)->create();
        $request = new Request();

        // Act
        $response = $this->controller->index($request);

        // Assert
        $this->assertEquals(200, $response->status());
        $this->assertCount(3, $response->getData()->data);
    }

    public function test_index_returns_empty_array_when_no_logs_exist()
    {
        // Arrange
        $request = new Request();

        // Act
        $response = $this->controller->index($request);

        // Assert
        $this->assertEquals(200, $response->status());
        $this->assertCount(0, $response->getData()->data);
    }

    public function test_index_response_has_correct_json_structure()
    {
        // Arrange
        SummaryLog::factory()->create();
        $request = new Request();

        // Act
        $response = $this->controller->index($request);

        // Assert
        $this->assertEquals(200, $response->status());
        $responseData = $response->getData();
        $this->assertObjectHasAttribute('data', $responseData);
        $this->assertIsArray($responseData->data);
    }
}
