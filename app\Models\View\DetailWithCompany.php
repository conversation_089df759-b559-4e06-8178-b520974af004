<?php

namespace App\Models\View;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\View\DetailWithCompany
 *
 * @method static \Illuminate\Database\Eloquent\Builder|DetailWithCompany newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DetailWithCompany newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DetailWithCompany query()
 * @mixin \Eloquent
 */
class DetailWithCompany extends Model
{
    use HasFactory;

    protected $connection = 'sqlsrv';
    protected $table = 'DetailWithCompany';
}
