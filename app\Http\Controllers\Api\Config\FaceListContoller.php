<?php

namespace App\Http\Controllers\Api\Config;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class FaceListContoller extends Controller
{
    public function index(Request $request)
    {
        $attribute = $request->attributes->get('DETAIL_NVR');

        $response = Http::withoutVerifying()
            ->withOptions(['verify' => false])
            ->withHeaders([
                'Content-Type' => 'application/json',
                'Cookie' => 'JSESSIONID=' . $request->session()->get($attribute['name']),
            ])
            ->get($attribute['ip'] . '/sdk_service/rest/facerepositories', [
                'no'        => 1,
                'size'      => 32,
                'sort'      => 'desc',
                'ordername' => 'name',
            ]);

        $data     = array();
        $faceList = json_decode($response->body(), true);

        if (isset($faceList['repositories'])) {
            foreach ($faceList['repositories'] as $fl) {
                $data[] = array(
                    'value' => strval($fl['id']),
                    'label' => $fl['name'],
                    'type' => $fl['type'],
                );
            }
            return response()->json([
                'status'     => true,
                'message'    => 'Success',
                'data'       => $data
            ], 200);
        } else {
            return response()->json([
                'status'     => false,
                'message'    => 'NVR Code failed.',
                'data'       => $faceList
            ], 422);
        }
    }
}
