<?php

namespace App\Traits;

use App\Models\TempConfig;

trait NvrCookieHelper
{
    public function storeConfigNvr($nvrName, $setCookiesNvr)
    {
        TempConfig::updateOrCreate([
            'config' => $nvrName,
            'type' => 'cookie ' . $nvrName
        ], [
            'value' => $setCookiesNvr,
        ]);
    }

    public function getConfigNvr($config, $type)
    {
        $config = TempConfig::where('config', $config)
            ->where('type', $type)
            ->first();

        if ($config) {
            return $config->value;
        }
        return null;
    }
}
