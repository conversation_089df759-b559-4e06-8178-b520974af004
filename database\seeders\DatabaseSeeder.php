<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use App\Models\User;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // \App\Models\User::factory(10)->create();

        User::create([
            'name'         => '<PERSON><PERSON>o <PERSON> I <PERSON>tang',
            'username'     => '88102332',
            'email'        => '<EMAIL>',
            'company'      => 'PT IMIP',
            'token_cherry' => 'xxx',
            'department'   => 'IT - APPLICATION DEVELOPMENT',
            'password'     => bcrypt('12345678'),
        ]);

        User::create([
            'name'         => 'Tisna',
            'username'     => '88101989',
            'email'        => '<EMAIL>',
            'company'      => 'PT IMIP',
            'token_cherry' => 'xxx',
            'department'   => 'IT - SAP',
            'password'     => bcrypt('12345678'),
        ]);

        User::create([
            'name'         => 'Patar',
            'username'     => '88102624',
            'email'        => '<EMAIL>',
            'company'      => 'PT IMIP',
            'token_cherry' => 'xxx',
            'department'   => 'IT - APPLICATION DEVELOPMENT',
            'password'     => bcrypt('12345678'),
        ]);
    }
}
