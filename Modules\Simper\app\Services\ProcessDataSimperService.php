<?php

namespace Modules\Simper\app\Services;

use App\Models\Applications;
use App\Models\Settings\AccessLevel;
use App\Models\Settings\Log_transaction;
use App\Models\Settings\Setting;
use App\Services\ProcessDataPersonService;
use App\Services\ProcessDataVehicleService;
use Bus;
use Carbon\Carbon;
use Illuminate\Bus\Batch;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Modules\Simper\app\Jobs\ProcessDeleteZkbioSimperJob;
use Modules\Simper\app\Jobs\ProcessUploadSimperZkbioJob;
use Modules\Vehicle\app\Services\ProcessDataVehicleApiService;
use Throwable;

class ProcessDataSimperService
{
    /**
     * @param $data
     * @param Applications $application
     * @return array
     * @throws Throwable
     */
    public function processQueueZkbio($data, Applications $application, $status): array
    {

        $collection = collect($data);
        $chunks = $collection->chunk(20);
        $batchQueue = [];
        $batch = Bus::batch([])
            ->catch(function (Batch $batch, Throwable $e) use ($application) {
                Log::info('error upload batch simper ' . now(), [
                    'error' => $e->getTraceAsString(),
                ]);
                // sleep(5);
                $logTransaction = Log_transaction::where('batch_id', $batch->id)
                    ->select(
                        "credential_number",
                        "name",
                        "company",
                        "nvr_id",
                        "face_list_id",
                        "service_code",
                        "service_message",
                        "batch_id"
                    )
                    ->where("service_code", "<>", "0")
                    ->where('application_id', $application->id)
                    ->where('type', "ZKBIO")
                    ->get();

                $params = [
                    'message' => $e->getMessage(),
                    'batch_id' => $batch->id,
                    'status' => 'error',
                    'name' => $batch->name,
                    'data' => $logTransaction,
                    // "application" => $application->id,
                ];

                Http::post($application->link_url, $params);

                activity()
                    ->performedOn($application)
                    ->withProperties($params)
                    ->log('Error process face record to application ' . $application->name);
            })
            ->then(function (Batch $batch) use ($application) {
                Log::info('Batch ID ZKBIO Simper ' . $batch->id);
                $logTransaction = Log_transaction::where('batch_id', $batch->id)
                    ->select(
                        "credential_number",
                        "name",
                        "company",
                        "nvr_id",
                        "face_list_id",
                        "service_code",
                        "service_message",
                        "batch_id"
                    )
                    ->where('application_id', $application->id)
                    ->where('type', "ZKBIO")
                    ->get();
                $params = [
                    'message' => 'success',
                    'batch_id' => $batch->id,
                    'status' => 'completed',
                    'name' => $batch->name,
                    "data" => $logTransaction,
                    // "application" => $application->id,
                ];
                Http::post($application->link_url, $params);

                activity()
                    ->performedOn($application)
                    ->withProperties($params)
                    ->log('Success process face record to application ' . $application->name);
            })
            ->name('Upload Zkbio Vehicle' . date('Y-m-d H:i:s'))
            ->onQueue('ZkbioSimperDev')
            // ->onQueue('Zkbio')
            ->allowFailures()
            ->dispatch();

        foreach ($chunks as $key => $chunk) {
            $batch->add(new ProcessUploadSimperZkbioJob($chunk, $batch->id, $application, $status));
        }

        return [
            [
                'label' => 'ZKBIO',
                'batch_id' => $batch->id
            ]
        ];
    }

    /**
     * @param Collection $rows
     * @param string $batch_id
     * @param Applications $applications
     * @param string $status
     * @return void
     */
    public function uploadAccessLevelSimper(Collection $rows, string $batch_id, Applications $applications, string $status): void
    {
        $start = Carbon::now()->format('Y-m-d H:i:s');

        $service = new ProcessDataPersonService();
        $serviceVehicle = new ProcessDataVehicleApiService();

        Log::info('start upload person zkbio, time: ' . $start);
        $data_level = [];
        foreach ($rows as $row) {
            Log::info('Upload person simper ' . $row['strId'] . ' dengan jenis kelamin ' . $row['gender'] . ' to zkbio ');

            $pid = $row['strId'];
            $accessToken = Setting::getSetting('AccessTokenZkbio');

            /* Cek if Exist */
            $setResponseUserCode = $service->getPerson($pid, $accessToken)->json();

            if (isset($setResponseUserCode['data']['accLevelIds'])) {
                $data_level = $setResponseUserCode['data']['accLevelIds'];
                $new_data_level = $data_level . ',' . $row['access_level_code'];
            } else {
                $new_data_level = $row['access_level_code'];
            }

            /* Data Name */
            if (isset($setResponseUserCode['data']['name'])) {
                $data_name = $setResponseUserCode['data']['name'];
                $data_last_name = $setResponseUserCode['data']['lastName'];
            } else {
                $data_name = $row['name'];
                $data_last_name = '';
            }

            if ($status == 'simper' && $row['statusEmployee'] == '0') {
                $accessLevelSimper = Setting::getSetting('AccessLevelSimper');
                $data_level_array = explode(',', $new_data_level);
                $data_level_array = array_filter($data_level_array, function ($value) use ($accessLevelSimper) {
                    return trim($value) !== $accessLevelSimper;
                });
                $new_data_level = implode(',', $data_level_array);

                $new_data_level = ($new_data_level == '') ? "NULL" : $new_data_level;
            }

            /* Get Photo */
            if (isset($row['base64'])) {
                $base64 = $row['base64'];
            } else {
                $photo = $service->getPhoto($row);
                $base64 = $photo;
            }

            $photoName = $row['photoName'];


            /* Emp Data */
            $deptCode = $service->getDepartmentCode((array) $row, $row['access_type']);
            

            if ($deptCode) {
                $code_depart = $deptCode['deptCode'];
                $name_depart = $deptCode['deptName'];
            }else{
                $code_depart = "SPR001";
                $name_depart = "SIMPER";
            }

            $personPhoto = $base64;

            Log::info('Department Name ' . $name_depart . ' (' . $code_depart . ') to zkbio ', [
                'cardNumber' => $row["cardNumber"],
                'accStartTime' => $row["accStartTime"],
                'accEndTime' => $row["accEndTime"],
                'code' => $new_data_level,
                'name' => $data_last_name,
                "status" => $status
            ]);

            $extraParams = [
                'cardNumber' => $row["cardNumber"],
                'accStartTime' => $row["accStartTime"],
                'accEndTime' => $row["accEndTime"],
            ];

            $service->processUploadAccessLevel(
                $accessToken,
                $pid,
                $code_depart,
                $name_depart,
                $data_name,
                $data_last_name,
                $row,
                $new_data_level,
                $batch_id,
                $personPhoto,
                $photoName,
                $status,
                $extraParams
            );

            if ($status == 'store') {
                $service->updatePersonnel($accessToken, $pid, $personPhoto, $data_name);
            }
        }

        Log::info('Upload person zkbio Done ! Time : ' . Carbon::now()->diffForHumans($start));
    }

    public function processQueueDeleteZkbio($data, Applications $application)
    {
        $collection = collect($data);
        $chunks = $collection->chunk(100)->toArray();
        $batch = Bus::batch([])
            ->catch(function (Batch $batch, Throwable $e) use ($application) {
                $logTransaction = Log_transaction::where('batch_id', $batch->id)
                    ->select(
                        "credential_number",
                        "name",
                        "company",
                        "access_level_code",
                        "access_level_name",
                        "service_code",
                        "service_message",
                        "batch_id"
                    )
                    ->where("service_code", "<>", "0")
                    ->where('application_id', $application->id)
                    ->where('type', "HUAWEI")
                    ->get();

                $params = [
                    'message' => $e->getMessage(),
                    'batch_id' => $batch->id,
                    'status' => 'error',
                    'name' => $batch->name,
                    'data' => $logTransaction,
                    // "application" => $application->id,
                ];
                Http::post($application->link_url, $params);

                activity()
                    ->performedOn($application)
                    ->withProperties($params)
                    ->log('Error delete person to application ' . $application->name);
            })
            ->finally(function (Batch $batch) use ($application) {
                $logTransaction = Log_transaction::whereBatchId($batch->id)
                    ->select(
                        "credential_number",
                        "name",
                        "company",
                        "access_level_code",
                        "access_level_name",
                        "service_code",
                        "service_message",
                        "batch_id"
                    )
                    // ->where("service_code", "<>", "0")
                    ->whereApplicationId($application->id)
                    ->whereIn("type", ["HUAWEI", "ZKBIO"])
                    ->get();

                $params = [
                    'message' => 'success',
                    'batch_id' => $batch->id,
                    'status' => 'completed',
                    "data" => $logTransaction
                ];
                Http::post($application->link_url, $params);

                activity()
                    ->performedOn($application)
                    ->withProperties($params)
                    ->log('Success delete person to application ' . $application->name);
            })
            ->name('Delete Person Zkbio ' . date('Y-m-d H:i:s'))
            ->onQueue('ZkbioSimperDevDelete')
            ->allowFailures()
            ->dispatch();

        foreach ($chunks as $key => $chunk) {
            // $batchQueue[] = new ProcessUploadFaceRecordUpdate($chunk, $nvrList);
            $batch->add([new ProcessDeleteZkbioSimperJob($chunk)]);
            // $batch->add(new ProcessUploadPersonModule($chunk, $item->id, $batch->id));
        }
        return [
            [
                'label' => 'ZKBIO',
                'batch_id' => $batch->id
            ]
        ];
    }
}
