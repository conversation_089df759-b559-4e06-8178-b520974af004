<?php

namespace Modules\Simper\app\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class DeleteSimperRequest extends FormRequest
{/**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'form.*.identifyNumber' => 'required|string',
            'form.*.statusEmployee' => 'required|string|in:0',
            'accessType' => 'required|string|in:SIMPER',
            'service' => 'required|string|in:ALL,Zkbio',
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function wantsJson()
    {
        return true;
    }

    protected function prepareForValidation()
    {
        // Validate that the request content type is application/json
        $this->validateContentType();
    }

    protected function validateContentType()
    {
        $contentType = $this->header('Content-Type');

        if ($contentType !== 'application/json') {
            $this->validator->errors()->add('content_type', 'The content type must be application/json.');
        }
    }
}
