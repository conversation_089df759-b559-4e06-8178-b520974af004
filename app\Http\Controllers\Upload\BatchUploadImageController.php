<?php

namespace App\Http\Controllers\Upload;

use App\Http\Controllers\Controller;
use App\Jobs\RemoveAttachment;
use App\Jobs\Upload\ProcessUploadImage;
use App\Models\Settings\Nvr;
use App\Services\Upload\BatchUploadService;
use GuzzleHttp\Psr7\Utils;
use Illuminate\Bus\Batch;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Throwable;
use ZipArchive;

class BatchUploadImageController extends Controller
{
    public function __construct()
    {
        // $this->middleware('nvr.api.auth')->only('store');
    }

    public function index(Request $request)
    {
        $service = new BatchUploadService();
        try {
            return $this->success('', $service->index($request));
        } catch (\Exception $exception) {
            throw new \Exception($exception->getMessage(), 1);
        }
    }

    public function store(Request $request)
    {
        $files = $request->file("zip_file");

        $nvr_id = $request->nvr_id;
        $user_id = auth()->user()->id;
        $file_names = [];


        $files = $request->file('images');

        $nvrLists = explode(',', $nvr_id);
        foreach ($nvrLists as $nvrList) {
            foreach ($files as $value) {
                $nvr = Nvr::find($nvrList);
                $nvrName = str_replace(' ', '_', $nvr->name);
                $dir_path = $nvrName;
                $zip = new ZipArchive();
                $file = file_get_contents($value->getRealPath());
                Storage::put($dir_path . '.zip', $file);
                $zipFile = $zip->open(Storage::path($dir_path . '.zip'));
                if ($zipFile === TRUE) {
                    $zip->extractTo(Storage::path($dir_path));
                    $zip->close();
                }

                RemoveAttachment::dispatch(Storage::path($dir_path . '.zip'))->delay(30);

                $dirs = Storage::disk('local')->allFiles($dir_path);

                $collection = collect($dirs);

                $chunks = $collection->chunk(80);


                $batchQueue = [];
                foreach ($chunks->all() as $key => $value) {
                    $options = [];
                    foreach ($value as $item) {
                        $options[] = [
                            'name' => 'body',
                            'contents' => Utils::tryFopen(storage_path('/app' . '/' . $item), 'r'),
                            'filename' => basename(storage_path('/app' . '/' . $item)),
                            'headers'  => [
                                'Content-Type' => 'multipart/form-data',
                            ]
                        ];
                    }
                    $optionsUpload = [
                        'multipart' => $options
                    ];
                    // return response()->json($optionsUpload, 422);
                    $batchQueue[] = new ProcessUploadImage($optionsUpload, $nvrList, $user_id);
                    // throw new \Exception('ok');
                }
                $batch = Bus::batch($batchQueue)
                    ->catch(function (Batch $batch, Throwable $e) use ($nvr) {
                        Log::info('error upload batch to nvr ' . $nvr->ip . '' . now(), [
                            'error' => $e->getTraceAsString(),
                        ]);
                    })
                    ->name('Upload Images ' . $nvrName)
                    ->onQueue('Medium')
                    ->allowFailures()
                    ->dispatch();
            }
        }


        return $this->success('Upload success', [
            'batch' => $batch
        ]);
    }
}
