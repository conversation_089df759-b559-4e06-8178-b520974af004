<?php

namespace App\Models\Settings;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Settings\Setting
 *
 * @method static \Illuminate\Database\Eloquent\Builder|Setting newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Setting newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Setting query()
 * @property int $id
 * @property string $option
 * @property string $value
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|Setting whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Setting whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Setting whereOption($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Setting whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Setting whereValue($value)
 * @mixin \Eloquent
 */
class Setting extends Model
{
    use HasFactory;

    protected $fillable = ['option', 'value'];

    public static function setSetting($option, $value)
    {
        $old = self::whereOption($option)->first();

        if ($old) {
            $old->value = $value;
            $old->save();
        }

        $set = new Setting();
        $set->option = $option;
        $set->value = $value;
        $set->save();
    }

    public static function setSettings($settings)
    {
        foreach ($settings as $key => $setting) {
            self::updateOrCreate([
                'option' => $key
            ], [
                'option' => $key,
                'value' => $setting
            ]);
        }
    }

    public static function getSetting($option)
    {
        $setting = static::whereOption($option)->first();
        return $setting ? $setting->value : null;
    }

    public static function getSettings($settings)
    {
        return static::whereIn('option', $settings)
            ->get()->mapWithKeys(function ($item) {
                return [$item['option'] => $item['value']];
            });
    }
}
