<?php

namespace App\Jobs\Upload;

use App\Services\Settings\Process;
use App\Services\Upload\BatchUploadZkbioService;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Intervention\Image\Facades\Image;
use Illuminate\Queue\SerializesModels;
use App\Models\Settings\Setting;
use Illuminate\Support\Facades\Storage;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Str;
use App\Models\User;
use App\Models\Settings\Organization;
use Illuminate\Support\Facades\DB;


class ProcessUploadZkbio implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use Batchable;

    public $row;
    public $accLevelIds;
    public $accLevelNames;
    public $user_id;
    /**
     * Create a new job instance.
     */
    public function __construct($row, $accLevelIds, $accLevelNames, $user_id)
    {
        $this->row = $row;
        $this->accLevelIds = $accLevelIds;
        $this->accLevelNames = $accLevelNames;
        $this->user_id = $user_id;
    }

    /**base
     * Execute the job.
     */
    public function handle(): void
    {
        $Services                   = new BatchUploadZkbioService();
        $rows                       = $this->row;
        $accLevelIds                = $this->accLevelIds;
        $accLevelNames              = $this->accLevelNames;
        $user_id                    = $this->user_id;
        $lvl                        = explode(",",$accLevelIds);
        $accessToken                = $Services->getAccessToken();
        $params_data                = array();

        Log::info('start upload person zkbio');
        $data_level = [];
        Log::info('Data Upload ' . now(), [
            'json' => $rows,
        ]);
        foreach ($rows as $row) {
            $pid = $row['strId'];

            Log::info('Data transform ' . now(), [
                'json' => $row,
            ]);

            $responseUser = $Services->getPerson($pid, $accessToken);

            /* Cek if Exist */
            $biometric = '';
            $setResponseUserCode = $responseUser->json();
            if (isset($setResponseUserCode['data']['accLevelIds'])) {
                $data_level = $setResponseUserCode['data']['accLevelIds'];
                $new_data_level = $data_level . ',' . $accLevelIds;
                $biometric = $setResponseUserCode['data']['personPhoto'];
                /* Not Update Personal Data */
            } else {
                $new_data_level = $accLevelIds;
            }

            /* Data Name */
            if (isset($setResponseUserCode['data']['name'])) {
                $data_name = $setResponseUserCode['data']['name'];
                $data_last_name = $setResponseUserCode['data']['lastName'];
            } else {
                $data_name = $row['name'];
                $data_last_name = '';
            }

            Artisan::call('cache:clear');
            Artisan::call('config:cache');

            /* Get Photo */
            $base64 = '';
         
            $compOrganization = Organization::where("name", "=", $row['company'])->first();
            
            /* If Datasource is Cherry */
            if ($compOrganization->data_source == 'cherry') {
                $userPhoto = $this->getUserPhoto($pid);
                if ($userPhoto == NULL) {
                    $base64 = '';
                } else {
                    $base64 = $userPhoto;
                }
                if(!isset($row['log_type'])){
                    $row['log_type'] = 'PERSON';
                }
                
            }
            else if($compOrganization->data_source == 'contractor'){
                $ftpEcon = Storage::disk('sftp-econ')->get($row['photoName']);

                
                if($ftpEcon){
                    $ftp_size = Storage::disk('sftp-econ')->size($row['photoName']);
                    $size = intval($ftp_size / 1024);

                    if($size < 100){
                        $ftpImage = Image::make($ftpEcon);
                    }else{
                        $ftpImage = Image::make($ftpEcon)->resize(null, 500, function ($constraint) {
                            $constraint->aspectRatio();
                        });
                    }

                    $base64 = preg_replace('#^data:image/\w+;base64,#i', '', $ftpImage->encode('data-url'));
                }

                if (!isset($row['log_type'])) {
                    $row['log_type'] = 'CONTRACTOR';
                }
            }
            else {
                $ftp = Storage::disk('ftp')->get('photo/' . $row['photoName']);
                $ftpHr = Storage::disk('sftp-hr')->get($row['org_alias'] . '/' . $row['photoName']);

                $size_validasi = Setting::getSetting('FilterSizeImage');
                if ($ftpHr) {
                    $ftp_size = Storage::disk('sftp-hr')->size($row['org_alias'] . '/' . $row['photoName']);
                    $size = intval($ftp_size / 1024);

                    if ($size < $size_validasi) {
                        $ftpImage = Image::make($ftpHr);
                    } else {
                        $ftpImage = Image::make($ftpHr)->resize(null, 500, function ($constraint) {
                            $constraint->aspectRatio();
                        });
                    }

                    $base64         = preg_replace('#^data:image/\w+;base64,#i', '', $ftpImage->encode('data-url'));

                }else if ($ftp){
                    $ftp_size       = Storage::disk('ftp')->size('photo/' . $row['photoName']);
                    $size           = intval($ftp_size / 1024);

                    if($size < $size_validasi){
                        $ftpImage   = Image::make($ftp);
                    }else{
                        $ftpImage   = Image::make($ftp)->resize(null, 500, function ($constraint) {
                            $constraint->aspectRatio();
                        });
                    }

                    $base64 = preg_replace('#^data:image/\w+;base64,#i', '', $ftpImage->encode('data-url'));
                }
                // $row['log_type'] = 'PERSON';
                if(!isset($row['log_type'])){
                    $row['log_type'] = 'PERSON';
                }
            }

            Log::info('Data LogType : ' .$pid. ' adalah ' . $row['log_type']);

            $data_emp = [
                'workLocation' => $row['work_location'],
                'company' => $row['org_alias'],
                'dept' => $row['dept_name'],
                'organization_group' => $row['org_group'],
                'compType' => $row['org_type'],
                'accessType' => ($compOrganization->organizationTypeList->name == 'INTERNAL') ? 'GENERAL' : $compOrganization->organizationTypeList->name,
            ];

            $deptCode = app('App\Helpers\DepartmentCode')->deptCode($data_emp, 3);
            $code_depart = $deptCode['deptCode'];
            $name_depart = $deptCode['deptName'];


            $personPhoto = $base64;

            $params_data = [
                'pin' => $pid,
                'deptCode' => $code_depart,
                'deptName' => $name_depart,
                'name' => $data_name,
                'lastName' => $data_last_name,
                'birthday' => $row['bornTime'],
                'gender' => $row['gender'],
                'cardNo' => NULL,
                'personPhoto' => $personPhoto,
                'accLevelIds' => $new_data_level,
                'log_type' => $row['log_type'],
            ];
            
            if($base64 == ''){
                if($biometric != ''){
                    $params_data['personPhoto']  = $biometric;
                    $Services->addPerson($accessToken, $params_data, $biometric, $row['company'], $new_data_level, $accLevelNames, $user_id);
                }else{
                    $Services->photoNotFound($accessToken, $params_data, $biometric, $row['company'], $new_data_level, $accLevelNames, $user_id);
                }
                
            }else{
                $Services->addPerson($accessToken, $params_data, $biometric, $row['company'], $new_data_level, $accLevelNames, $user_id);
            }

        }

        Log::info('Upload person zkbio Done ! Time : ' . date("Y-m-d H:i:s"));
    }

    public function getUserPhoto($nik)
    {
        $nik = (string) $nik;
        $responseCherry = DB::connection('sqlsrvcherry')->table('vw_employee_masterdata')
            ->select(DB::raw("Photo"))
            ->where('Nik', $nik)
            ->first();

        if (!$responseCherry || !$responseCherry->Photo) {
            return null;
        }
        $imageBinaryData = $responseCherry->Photo;
        $base64Image = base64_encode($imageBinaryData);
        return preg_replace('#^data:image/\w+;base64,#i', '', $base64Image);
    }
}
