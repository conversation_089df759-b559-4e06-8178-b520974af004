<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Settings\Setting;
use App\Helpers\GenerateInitials;
use Illuminate\Support\Facades\DB;
use App\Models\Settings\AccessType;
use App\Models\Settings\Department;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use App\Models\Settings\Organization;
use App\Models\Settings\OrganizationType;

class SyncMappingDepartment extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-mapping-department';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $getAll = Organization::orderBy('id', 'desc')->get();
       
        $responseCherry = DB::connection('sqlsrvcherry')->select("SELECT 
            'GENERAL' as accType,
            'INTERNAL' as compType,
            right(<PERSON><PERSON>WorkLoc<PERSON>, LEN(A.WorkLocation) - charindex(' ', A.WorkLocation)) as Work,
                A.WorkLocation,
                RIGHT(A.Company, LEN(A.Company) - 3) as Company, 
                A.Department 
            FROM vw_employee_masterdata A
            GROUP BY
                A.WorkLocation,
                A.Company,
                A.Department 
            ORDER BY A.Company, A.Department ASC
            ");
        
        // foreach($getAll as $allPT){
        //     $request = array(
        //         'workLocation'       => 'MOROWALI',
        //         'company'            => $allPT->alias,
        //         'dept'               => $allPT->Department,
        //         'accessType'         => $allPT->organization_type,
        //         'compType'           => $allPT->organization_type,
        //         'organization_group' => $allPT->organization_group,
        //         'list_id'            => (isset($allPT->organization_type_list)) ? $allPT->organization_type_list->id : '',     
        //         'id'                 => (isset($allPT->id)) ? $allPT->id : '',     
        //     );

        //     $this->processDepartment($request);
        // }

        foreach($responseCherry as $allDept){

            $request = array(
                'workLocation'       => $allDept->Work,
                'company'            => $allDept->Company,
                'dept'               => $allDept->Department,
                'accessType'         => $allDept->accType,
                'compType'           => $allDept->compType,
                'organization_group' => 1,
                'list_id'            => (isset($allDept->organization_type_list)) ? $allDept->organization_type_list->id : '',     
                'id'                 => (isset($allDept->id)) ? $allDept->id : '',     
            );

            $this->processDepartment($request);
        }
    }


    private function processDepartment($request){

        Log::info("message check ". $request['company']);

        $work               = $request['workLocation'];
        $company            = $request['company'];
        $dept               = str_replace(array( '\'', '"','&','/',',' , ';', '<', '>' ), '', $request['dept']);;
        $organization_group = $request['organization_group'];
        $compType           = $request['compType'];
        $accessType         = ($request['accessType'] == 'INTERNAL') ? 'GENERAL' : $request['accessType'];

        $level1Name = $work;
        $level1Code = GenerateInitials::generateInitials($work,4).crc32($work);

        if($organization_group == 1){
            $level2Name = $company;
            $level2Code = GenerateInitials::generateInitials($compType .' '.$work.' '.$company,2).crc32($company);
           
            $level3Name = $dept;
            $level3Code = GenerateInitials::generateInitials($compType .' '.$work.' '.$company.' '.$dept,2).crc32($dept);
        }else{
            $level2Name = $accessType;
            $level2Code = GenerateInitials::generateInitials($work.' '.$accessType,2).crc32($accessType);
           
            $level3Name = substr($compType , 0, 1).'-'.$company;
            $level3Code = GenerateInitials::generateInitials($work.' '.$accessType.' '.$company,2).crc32($company);
        }

        $Department = Department::where('dpt_code', $level3Code)->first();

        $urlAddDepartment = Setting::getSetting('UrlAddDepartmentZkbio');
        $accessToken      = Setting::getSetting('AccessTokenZkbio');

        if(!isset($Department) AND isset($level3Name)){
            Department::create([
                'dpt_name'         => $level3Name,
                'dpt_code'         => $level3Code,
                'dpt_parent_code'  => $level2Code,
                'dpt_sort_no'      => 999,
                'dpt_type'         => isset($request['list_id']) ? $request['list_id'] : '',
                'dpt_worklocation' => $work,
                'organization_id'  => (isset($request['id'])) ? $request['id'] : '',
                'created_by'       => NULL,
                'status'           => 1,
            ]);
        }
     
        Http::post($urlAddDepartment.'?access_token='.$accessToken, [
            'code'       => $level1Code,
            'name'       => $level1Name,
            'parentCode' => 0,
            'sortNo'     => 999,
        ]);
        Http::post($urlAddDepartment.'?access_token='.$accessToken, [
            'code'       => $level2Code,
            'name'       => $level2Name,
            'parentCode' => $level1Code,
            'sortNo'     => 999,
        ]);
        Http::post($urlAddDepartment.'?access_token='.$accessToken, [
            'code'       => $level3Code,
            'name'       => $level3Name,
            'parentCode' => $level2Code,
            'sortNo'     => 999,
        ]);

        Log::info('Success Organization : '.$level3Code);

    }
}
