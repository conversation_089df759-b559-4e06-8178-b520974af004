<?php

namespace App\Models\Settings;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Settings\Nvr
 *
 * @property int $id
 * @property string $name
 * @property string $ip
 * @property string $username
 * @property string $password
 * @property int $status
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read mixed $label
 * @property-read mixed $value
 * @method static \Illuminate\Database\Eloquent\Builder|Nvr newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Nvr newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Nvr query()
 * @method static \Illuminate\Database\Eloquent\Builder|Nvr whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Nvr whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Nvr whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Nvr whereIp($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Nvr whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Nvr wherePassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Nvr whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Nvr whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Nvr whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Nvr whereUsername($value)
 * @mixin \Eloquent
 */
class Nvr extends Model
{
    use HasFactory;
    protected $guarded = [];
    protected $table = 'm_nvr';

    public $appends = [
        'label',
        'value'
    ];

    public function getLabelAttribute()
    {
        return $this->name;
    }

    public function getValueAttribute()
    {
        return $this->id;
    }
}
