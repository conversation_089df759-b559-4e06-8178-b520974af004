<?php

namespace App\Http\Controllers\Settings;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Models\Settings\AccessType;
use App\Http\Controllers\Controller;
use App\Services\Settings\AccessTypeService;
use Illuminate\Http\JsonResponse;

class AccessTypeController extends Controller
{

    public AccessTypeService $service;

    public function __construct(AccessTypeService $service)
    {
        $this->service = $service;
        // $this->middleware(['direct_permission:Roles-index'])->only(['index', 'show', 'permissionRole']);
        // $this->middleware(['direct_permission:Roles-store'])->only(['store', 'storePermissionRole']);
        // $this->middleware(['direct_permission:Roles-edits'])->only('update');
        // $this->middleware(['direct_permission:Roles-erase'])->only('destroy');
    }
    
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            return $this->success('', $this->service->index($request));
        } catch (\Exception $exception) {
            throw new \Exception($exception->getMessage(), 1);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $item = AccessType::create($this->service->formData($request, 'store'));

            // $this->processItemDetails($category, $item['id']);

            DB::commit();

            return $this->success('Data inserted!', [
                'errors' => false,
            ]);
        } catch (\Exception $exception) {
            DB::rollBack();
            throw new \Exception($exception->getMessage(), 1);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show($id): JsonResponse
    {
        $data = AccessType::where("id", "=", $id)->get();

        return $this->success('Success', [
            'data' => $data
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id): JsonResponse
    {
        DB::beginTransaction();
        try {
            $data = AccessType::find($id);
            $forms = collect($this->service->formData($request, 'update'));
            //return $this->error('', 422, [$forms]);
            foreach ($forms as $index => $form) {
                $data->$index = $form;
            }
            $data->save();

            // $this->processItemDetails($category, $id);

            DB::commit();

            return $this->success('Data updated!', [
                'errors' => false,
            ]);
        } catch (\Exception $exception) {
            DB::rollBack();

            throw new \Exception($exception->getMessage(), 1);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Request $request, $id): JsonResponse
    {
        try {
            $collect = collect($request->selected);
            $selected = $collect->pluck('id');

            $details = AccessType::where('id', $id)->delete();

            return $this->success('Row deleted!', [
                'errors' => false,
            ]);
        } catch (\Exception $exception) {
            throw new \Exception($exception->getMessage(), 1);
        }
    }
}
