<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Modules\Simper\app\Http\Controllers\SimperController;

Route::group(['middleware' => ['external.app'], 'prefix' => 'v1'], function () {
    Route::get('simper', [SimperController::class, 'show']);
    Route::post('simper', [SimperController::class, 'store']);
    Route::put('simper', [SimperController::class, 'update']);
    Route::delete('simper', [SimperController::class, 'destroy']);
    Route::get('simper/logSimperAll', [SimperController::class, 'getLogSimperAll']);
    Route::get('simper/totalSimper', [SimperController::class, 'getTotalSimper']);
});
