[2025-07-01 14:35:59] local.INFO: Response check user in NVR: NVR_1 {"response":{"resultCode":0,"userId":503}} 
[2025-07-01 14:36:22] local.INFO: Response check user in NVR: NVR_2 {"response":{"resultCode":0,"userId":401}} 
[2025-07-01 14:36:50] local.ERROR: cURL error 28: Failed to connect to *********** port 18531 after 10014 ms: Timeout was reached (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://***********:18531/users/userid {"userId":3,"exception":"[object] (Illuminate\\Http\\Client\\ConnectionException(code: 0): cURL error 28: Failed to connect to *********** port 18531 after 10014 ms: Timeout was reached (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://***********:18531/users/userid at C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php:930)
[stacktrace]
#0 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(248): Illuminate\\Http\\Client\\PendingRequest->Illuminate\\Http\\Client\\{closure}()
#1 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(892): retry()
#2 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(770): Illuminate\\Http\\Client\\PendingRequest->send()
#3 C:\\xampp\\htdocs\\acc_control_backend\\app\\Services\\Upload\\BatchUploadService.php(67): Illuminate\\Http\\Client\\PendingRequest->get()
#4 C:\\xampp\\htdocs\\acc_control_backend\\app\\Services\\Dashboard\\SummaryCalculation.php(61): App\\Services\\Upload\\BatchUploadService->validateNvrCookie()
#5 C:\\xampp\\htdocs\\acc_control_backend\\app\\Http\\Controllers\\Dashboard\\TotalPersonController.php(15): App\\Services\\Dashboard\\SummaryCalculation->calculateTotalPersonHuawei()
#6 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Dashboard\\TotalPersonController->index()
#7 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#8 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#9 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#10 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#11 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#12 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#13 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#14 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#15 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#16 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#17 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#18 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#20 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#22 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#24 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#25 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then()
#27 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#28 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute()
#29 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#30 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#31 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#32 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#34 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#35 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#37 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#38 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#40 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#42 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#44 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#46 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#47 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#48 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#49 C:\\xampp\\htdocs\\acc_control_backend\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#50 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('...')
#51 {main}

[previous exception] [object] (GuzzleHttp\\Exception\\ConnectException(code: 0): cURL error 28: Failed to connect to *********** port 18531 after 10014 ms: Timeout was reached (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://***********:18531/users/userid at C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlFactory.php:275)
[stacktrace]
#0 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlFactory.php(205): GuzzleHttp\\Handler\\CurlFactory::createRejection()
#1 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlFactory.php(157): GuzzleHttp\\Handler\\CurlFactory::finishError()
#2 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlHandler.php(47): GuzzleHttp\\Handler\\CurlFactory::finish()
#3 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\Proxy.php(28): GuzzleHttp\\Handler\\CurlHandler->__invoke()
#4 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\Proxy.php(48): GuzzleHttp\\Handler\\Proxy::GuzzleHttp\\Handler\\{closure}()
#5 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(1256): GuzzleHttp\\Handler\\Proxy::GuzzleHttp\\Handler\\{closure}()
#6 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(1222): Illuminate\\Http\\Client\\PendingRequest->Illuminate\\Http\\Client\\{closure}()
#7 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(1208): Illuminate\\Http\\Client\\PendingRequest->Illuminate\\Http\\Client\\{closure}()
#8 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\PrepareBodyMiddleware.php(35): Illuminate\\Http\\Client\\PendingRequest->Illuminate\\Http\\Client\\{closure}()
#9 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(38): GuzzleHttp\\PrepareBodyMiddleware->__invoke()
#10 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\RedirectMiddleware.php(71): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}()
#11 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(63): GuzzleHttp\\RedirectMiddleware->__invoke()
#12 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\HandlerStack.php(75): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}()
#13 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(333): GuzzleHttp\\HandlerStack->__invoke()
#14 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(169): GuzzleHttp\\Client->transfer()
#15 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(189): GuzzleHttp\\Client->requestAsync()
#16 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(1053): GuzzleHttp\\Client->request()
#17 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(894): Illuminate\\Http\\Client\\PendingRequest->sendRequest()
#18 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(248): Illuminate\\Http\\Client\\PendingRequest->Illuminate\\Http\\Client\\{closure}()
#19 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(892): retry()
#20 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(770): Illuminate\\Http\\Client\\PendingRequest->send()
#21 C:\\xampp\\htdocs\\acc_control_backend\\app\\Services\\Upload\\BatchUploadService.php(67): Illuminate\\Http\\Client\\PendingRequest->get()
#22 C:\\xampp\\htdocs\\acc_control_backend\\app\\Services\\Dashboard\\SummaryCalculation.php(61): App\\Services\\Upload\\BatchUploadService->validateNvrCookie()
#23 C:\\xampp\\htdocs\\acc_control_backend\\app\\Http\\Controllers\\Dashboard\\TotalPersonController.php(15): App\\Services\\Dashboard\\SummaryCalculation->calculateTotalPersonHuawei()
#24 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Dashboard\\TotalPersonController->index()
#25 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#26 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#27 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#28 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#29 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#30 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#32 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#34 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#35 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#36 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#38 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#40 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#42 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#43 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then()
#45 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#46 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute()
#47 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#48 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#49 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#50 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#52 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#53 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#55 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#56 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#57 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#58 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#59 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#60 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#61 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#62 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#63 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#64 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#65 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#66 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#67 C:\\xampp\\htdocs\\acc_control_backend\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#68 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('...')
#69 {main}
"} 
[2025-07-01 14:36:50] local.INFO: Response check user in NVR: NVR_1 {"response":{"resultCode":0,"userId":503}} 
[2025-07-01 14:37:00] local.INFO: Response check user in NVR: NVR_2 {"response":{"resultCode":0,"userId":401}} 
[2025-07-01 14:37:19] local.ERROR: cURL error 28: Failed to connect to *********** port 18531 after 10011 ms: Timeout was reached (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://***********:18531/users/userid {"userId":3,"exception":"[object] (Illuminate\\Http\\Client\\ConnectionException(code: 0): cURL error 28: Failed to connect to *********** port 18531 after 10011 ms: Timeout was reached (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://***********:18531/users/userid at C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php:930)
[stacktrace]
#0 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(248): Illuminate\\Http\\Client\\PendingRequest->Illuminate\\Http\\Client\\{closure}()
#1 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(892): retry()
#2 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(770): Illuminate\\Http\\Client\\PendingRequest->send()
#3 C:\\xampp\\htdocs\\acc_control_backend\\app\\Services\\Upload\\BatchUploadService.php(67): Illuminate\\Http\\Client\\PendingRequest->get()
#4 C:\\xampp\\htdocs\\acc_control_backend\\app\\Services\\Dashboard\\SummaryCalculation.php(25): App\\Services\\Upload\\BatchUploadService->validateNvrCookie()
#5 C:\\xampp\\htdocs\\acc_control_backend\\app\\Http\\Controllers\\Dashboard\\TotalPersonController.php(22): App\\Services\\Dashboard\\SummaryCalculation->getTotalPersonNvr()
#6 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Dashboard\\TotalPersonController->getTotalPersonNvr()
#7 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#8 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#9 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#10 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#11 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#12 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#13 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#14 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#15 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#16 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#17 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#18 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#20 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#22 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#24 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#25 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then()
#27 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#28 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute()
#29 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#30 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#31 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#32 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#34 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#35 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#37 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#38 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#40 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#42 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#44 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#46 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#47 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#48 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#49 C:\\xampp\\htdocs\\acc_control_backend\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#50 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('...')
#51 {main}

[previous exception] [object] (GuzzleHttp\\Exception\\ConnectException(code: 0): cURL error 28: Failed to connect to *********** port 18531 after 10011 ms: Timeout was reached (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://***********:18531/users/userid at C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlFactory.php:275)
[stacktrace]
#0 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlFactory.php(205): GuzzleHttp\\Handler\\CurlFactory::createRejection()
#1 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlFactory.php(157): GuzzleHttp\\Handler\\CurlFactory::finishError()
#2 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlHandler.php(47): GuzzleHttp\\Handler\\CurlFactory::finish()
#3 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\Proxy.php(28): GuzzleHttp\\Handler\\CurlHandler->__invoke()
#4 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\Proxy.php(48): GuzzleHttp\\Handler\\Proxy::GuzzleHttp\\Handler\\{closure}()
#5 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(1256): GuzzleHttp\\Handler\\Proxy::GuzzleHttp\\Handler\\{closure}()
#6 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(1222): Illuminate\\Http\\Client\\PendingRequest->Illuminate\\Http\\Client\\{closure}()
#7 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(1208): Illuminate\\Http\\Client\\PendingRequest->Illuminate\\Http\\Client\\{closure}()
#8 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\PrepareBodyMiddleware.php(35): Illuminate\\Http\\Client\\PendingRequest->Illuminate\\Http\\Client\\{closure}()
#9 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(38): GuzzleHttp\\PrepareBodyMiddleware->__invoke()
#10 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\RedirectMiddleware.php(71): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}()
#11 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(63): GuzzleHttp\\RedirectMiddleware->__invoke()
#12 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\HandlerStack.php(75): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}()
#13 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(333): GuzzleHttp\\HandlerStack->__invoke()
#14 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(169): GuzzleHttp\\Client->transfer()
#15 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(189): GuzzleHttp\\Client->requestAsync()
#16 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(1053): GuzzleHttp\\Client->request()
#17 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(894): Illuminate\\Http\\Client\\PendingRequest->sendRequest()
#18 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(248): Illuminate\\Http\\Client\\PendingRequest->Illuminate\\Http\\Client\\{closure}()
#19 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(892): retry()
#20 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(770): Illuminate\\Http\\Client\\PendingRequest->send()
#21 C:\\xampp\\htdocs\\acc_control_backend\\app\\Services\\Upload\\BatchUploadService.php(67): Illuminate\\Http\\Client\\PendingRequest->get()
#22 C:\\xampp\\htdocs\\acc_control_backend\\app\\Services\\Dashboard\\SummaryCalculation.php(25): App\\Services\\Upload\\BatchUploadService->validateNvrCookie()
#23 C:\\xampp\\htdocs\\acc_control_backend\\app\\Http\\Controllers\\Dashboard\\TotalPersonController.php(22): App\\Services\\Dashboard\\SummaryCalculation->getTotalPersonNvr()
#24 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Dashboard\\TotalPersonController->getTotalPersonNvr()
#25 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#26 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#27 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#28 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#29 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#30 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#32 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#34 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#35 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#36 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#38 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#40 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#42 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#43 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then()
#45 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#46 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute()
#47 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#48 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#49 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#50 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#52 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#53 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#55 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#56 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#57 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#58 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#59 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#60 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#61 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#62 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#63 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#64 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#65 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#66 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#67 C:\\xampp\\htdocs\\acc_control_backend\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#68 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('...')
#69 {main}
"} 
[2025-07-01 14:38:28] local.ERROR: Maximum execution time of 60 seconds exceeded {"userId":3,"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 60 seconds exceeded at C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:430)
[stacktrace]
#0 {main}
"} 
[2025-07-01 14:38:30] local.INFO: Response check user in NVR: NVR_1 {"response":{"resultCode":0,"userId":503}} 
[2025-07-01 14:38:53] local.INFO: Response check user in NVR: NVR_2 {"response":{"resultCode":0,"userId":401}} 
[2025-07-01 14:39:24] local.ERROR: cURL error 28: Failed to connect to *********** port 18531 after 10008 ms: Timeout was reached (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://***********:18531/users/userid {"userId":3,"exception":"[object] (Illuminate\\Http\\Client\\ConnectionException(code: 0): cURL error 28: Failed to connect to *********** port 18531 after 10008 ms: Timeout was reached (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://***********:18531/users/userid at C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php:930)
[stacktrace]
#0 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(248): Illuminate\\Http\\Client\\PendingRequest->Illuminate\\Http\\Client\\{closure}()
#1 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(892): retry()
#2 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(770): Illuminate\\Http\\Client\\PendingRequest->send()
#3 C:\\xampp\\htdocs\\acc_control_backend\\app\\Services\\Upload\\BatchUploadService.php(67): Illuminate\\Http\\Client\\PendingRequest->get()
#4 C:\\xampp\\htdocs\\acc_control_backend\\app\\Services\\Dashboard\\SummaryCalculation.php(61): App\\Services\\Upload\\BatchUploadService->validateNvrCookie()
#5 C:\\xampp\\htdocs\\acc_control_backend\\app\\Http\\Controllers\\Dashboard\\TotalPersonController.php(15): App\\Services\\Dashboard\\SummaryCalculation->calculateTotalPersonHuawei()
#6 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Dashboard\\TotalPersonController->index()
#7 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#8 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#9 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#10 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#11 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#12 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#13 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#14 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#15 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#16 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#17 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#18 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#20 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#22 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#24 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#25 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then()
#27 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#28 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute()
#29 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#30 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#31 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#32 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#34 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#35 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#37 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#38 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#40 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#42 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#44 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#46 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#47 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#48 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#49 C:\\xampp\\htdocs\\acc_control_backend\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#50 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('...')
#51 {main}

[previous exception] [object] (GuzzleHttp\\Exception\\ConnectException(code: 0): cURL error 28: Failed to connect to *********** port 18531 after 10008 ms: Timeout was reached (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://***********:18531/users/userid at C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlFactory.php:275)
[stacktrace]
#0 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlFactory.php(205): GuzzleHttp\\Handler\\CurlFactory::createRejection()
#1 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlFactory.php(157): GuzzleHttp\\Handler\\CurlFactory::finishError()
#2 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlHandler.php(47): GuzzleHttp\\Handler\\CurlFactory::finish()
#3 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\Proxy.php(28): GuzzleHttp\\Handler\\CurlHandler->__invoke()
#4 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\Proxy.php(48): GuzzleHttp\\Handler\\Proxy::GuzzleHttp\\Handler\\{closure}()
#5 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(1256): GuzzleHttp\\Handler\\Proxy::GuzzleHttp\\Handler\\{closure}()
#6 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(1222): Illuminate\\Http\\Client\\PendingRequest->Illuminate\\Http\\Client\\{closure}()
#7 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(1208): Illuminate\\Http\\Client\\PendingRequest->Illuminate\\Http\\Client\\{closure}()
#8 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\PrepareBodyMiddleware.php(35): Illuminate\\Http\\Client\\PendingRequest->Illuminate\\Http\\Client\\{closure}()
#9 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(38): GuzzleHttp\\PrepareBodyMiddleware->__invoke()
#10 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\RedirectMiddleware.php(71): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}()
#11 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(63): GuzzleHttp\\RedirectMiddleware->__invoke()
#12 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\HandlerStack.php(75): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}()
#13 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(333): GuzzleHttp\\HandlerStack->__invoke()
#14 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(169): GuzzleHttp\\Client->transfer()
#15 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(189): GuzzleHttp\\Client->requestAsync()
#16 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(1053): GuzzleHttp\\Client->request()
#17 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(894): Illuminate\\Http\\Client\\PendingRequest->sendRequest()
#18 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(248): Illuminate\\Http\\Client\\PendingRequest->Illuminate\\Http\\Client\\{closure}()
#19 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(892): retry()
#20 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(770): Illuminate\\Http\\Client\\PendingRequest->send()
#21 C:\\xampp\\htdocs\\acc_control_backend\\app\\Services\\Upload\\BatchUploadService.php(67): Illuminate\\Http\\Client\\PendingRequest->get()
#22 C:\\xampp\\htdocs\\acc_control_backend\\app\\Services\\Dashboard\\SummaryCalculation.php(61): App\\Services\\Upload\\BatchUploadService->validateNvrCookie()
#23 C:\\xampp\\htdocs\\acc_control_backend\\app\\Http\\Controllers\\Dashboard\\TotalPersonController.php(15): App\\Services\\Dashboard\\SummaryCalculation->calculateTotalPersonHuawei()
#24 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Dashboard\\TotalPersonController->index()
#25 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#26 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#27 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#28 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#29 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#30 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#32 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#34 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#35 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#36 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#38 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#40 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#42 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#43 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then()
#45 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#46 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute()
#47 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#48 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#49 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#50 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#52 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#53 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#55 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#56 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#57 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#58 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#59 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#60 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#61 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#62 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#63 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#64 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#65 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#66 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#67 C:\\xampp\\htdocs\\acc_control_backend\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#68 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('...')
#69 {main}
"} 
[2025-07-01 14:39:25] local.INFO: Response check user in NVR: NVR_1 {"response":{"resultCode":0,"userId":503}} 
[2025-07-01 14:39:32] local.INFO: Response check user in NVR: NVR_2 {"response":{"resultCode":0,"userId":401}} 
[2025-07-01 14:40:22] local.ERROR: cURL error 28: Failed to connect to *********** port 18531 after 10003 ms: Timeout was reached (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://***********:18531/users/userid {"userId":3,"exception":"[object] (Illuminate\\Http\\Client\\ConnectionException(code: 0): cURL error 28: Failed to connect to *********** port 18531 after 10003 ms: Timeout was reached (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://***********:18531/users/userid at C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php:930)
[stacktrace]
#0 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(248): Illuminate\\Http\\Client\\PendingRequest->Illuminate\\Http\\Client\\{closure}()
#1 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(892): retry()
#2 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(770): Illuminate\\Http\\Client\\PendingRequest->send()
#3 C:\\xampp\\htdocs\\acc_control_backend\\app\\Services\\Upload\\BatchUploadService.php(67): Illuminate\\Http\\Client\\PendingRequest->get()
#4 C:\\xampp\\htdocs\\acc_control_backend\\app\\Services\\Dashboard\\SummaryCalculation.php(25): App\\Services\\Upload\\BatchUploadService->validateNvrCookie()
#5 C:\\xampp\\htdocs\\acc_control_backend\\app\\Http\\Controllers\\Dashboard\\TotalPersonController.php(22): App\\Services\\Dashboard\\SummaryCalculation->getTotalPersonNvr()
#6 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Dashboard\\TotalPersonController->getTotalPersonNvr()
#7 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#8 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#9 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#10 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#11 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#12 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#13 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#14 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#15 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#16 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#17 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#18 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#20 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#22 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#24 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#25 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then()
#27 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#28 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute()
#29 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#30 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#31 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#32 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#34 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#35 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#37 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#38 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#40 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#42 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#44 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#46 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#47 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#48 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#49 C:\\xampp\\htdocs\\acc_control_backend\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#50 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('...')
#51 {main}

[previous exception] [object] (GuzzleHttp\\Exception\\ConnectException(code: 0): cURL error 28: Failed to connect to *********** port 18531 after 10003 ms: Timeout was reached (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://***********:18531/users/userid at C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlFactory.php:275)
[stacktrace]
#0 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlFactory.php(205): GuzzleHttp\\Handler\\CurlFactory::createRejection()
#1 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlFactory.php(157): GuzzleHttp\\Handler\\CurlFactory::finishError()
#2 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlHandler.php(47): GuzzleHttp\\Handler\\CurlFactory::finish()
#3 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\Proxy.php(28): GuzzleHttp\\Handler\\CurlHandler->__invoke()
#4 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\Proxy.php(48): GuzzleHttp\\Handler\\Proxy::GuzzleHttp\\Handler\\{closure}()
#5 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(1256): GuzzleHttp\\Handler\\Proxy::GuzzleHttp\\Handler\\{closure}()
#6 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(1222): Illuminate\\Http\\Client\\PendingRequest->Illuminate\\Http\\Client\\{closure}()
#7 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(1208): Illuminate\\Http\\Client\\PendingRequest->Illuminate\\Http\\Client\\{closure}()
#8 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\PrepareBodyMiddleware.php(35): Illuminate\\Http\\Client\\PendingRequest->Illuminate\\Http\\Client\\{closure}()
#9 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(38): GuzzleHttp\\PrepareBodyMiddleware->__invoke()
#10 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\RedirectMiddleware.php(71): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}()
#11 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(63): GuzzleHttp\\RedirectMiddleware->__invoke()
#12 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\HandlerStack.php(75): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}()
#13 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(333): GuzzleHttp\\HandlerStack->__invoke()
#14 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(169): GuzzleHttp\\Client->transfer()
#15 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(189): GuzzleHttp\\Client->requestAsync()
#16 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(1053): GuzzleHttp\\Client->request()
#17 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(894): Illuminate\\Http\\Client\\PendingRequest->sendRequest()
#18 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(248): Illuminate\\Http\\Client\\PendingRequest->Illuminate\\Http\\Client\\{closure}()
#19 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(892): retry()
#20 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(770): Illuminate\\Http\\Client\\PendingRequest->send()
#21 C:\\xampp\\htdocs\\acc_control_backend\\app\\Services\\Upload\\BatchUploadService.php(67): Illuminate\\Http\\Client\\PendingRequest->get()
#22 C:\\xampp\\htdocs\\acc_control_backend\\app\\Services\\Dashboard\\SummaryCalculation.php(25): App\\Services\\Upload\\BatchUploadService->validateNvrCookie()
#23 C:\\xampp\\htdocs\\acc_control_backend\\app\\Http\\Controllers\\Dashboard\\TotalPersonController.php(22): App\\Services\\Dashboard\\SummaryCalculation->getTotalPersonNvr()
#24 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Dashboard\\TotalPersonController->getTotalPersonNvr()
#25 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#26 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#27 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#28 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#29 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#30 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#32 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(159): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(125): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest()
#34 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(87): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter()
#35 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle()
#36 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#38 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#40 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#42 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#43 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then()
#45 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack()
#46 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute()
#47 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute()
#48 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#49 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#50 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#51 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#52 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#53 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#55 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#56 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#57 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#58 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#59 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#60 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#61 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#62 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#63 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#64 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#65 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#66 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#67 C:\\xampp\\htdocs\\acc_control_backend\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#68 C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(16): require_once('...')
#69 {main}
"} 
[2025-07-01 14:41:39] local.ERROR: Maximum execution time of 60 seconds exceeded {"userId":3,"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 60 seconds exceeded at C:\\xampp\\htdocs\\acc_control_backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:430)
[stacktrace]
#0 {main}
"} 
[2025-07-01 15:07:47] local.ERROR: Cannot declare class App\Http\Controllers\Reports\ReportVisitorController, because the name is already in use {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Cannot declare class App\\Http\\Controllers\\Reports\\ReportVisitorController, because the name is already in use at C:\\xampp\\htdocs\\acc_control_backend\\app\\Http\\Controllers\\Dashboard\\ReportVisitorController.php:10)
[stacktrace]
#0 {main}
"} 
[2025-07-01 15:11:39] local.ERROR: Cannot declare class App\Http\Controllers\Reports\ReportVisitorController, because the name is already in use {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Cannot declare class App\\Http\\Controllers\\Reports\\ReportVisitorController, because the name is already in use at C:\\xampp\\htdocs\\acc_control_backend\\app\\Http\\Controllers\\Dashboard\\ReportVisitorController.php:10)
[stacktrace]
#0 {main}
"} 
[2025-07-01 16:01:44] local.ERROR: Cannot declare class App\Http\Controllers\Reports\ReportVisitorController, because the name is already in use {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Cannot declare class App\\Http\\Controllers\\Reports\\ReportVisitorController, because the name is already in use at C:\\xampp\\htdocs\\acc_control_backend\\app\\Http\\Controllers\\Dashboard\\ReportVisitorController.php:10)
[stacktrace]
#0 {main}
"} 
[2025-07-01 16:16:46] local.ERROR: Cannot declare class App\Http\Controllers\Reports\ReportVisitorController, because the name is already in use {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Cannot declare class App\\Http\\Controllers\\Reports\\ReportVisitorController, because the name is already in use at C:\\xampp\\htdocs\\acc_control_backend\\app\\Http\\Controllers\\Dashboard\\ReportVisitorController.php:10)
[stacktrace]
#0 {main}
"} 
[2025-07-01 16:16:51] local.ERROR: Cannot declare class App\Http\Controllers\Reports\ReportVisitorController, because the name is already in use {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Cannot declare class App\\Http\\Controllers\\Reports\\ReportVisitorController, because the name is already in use at C:\\xampp\\htdocs\\acc_control_backend\\app\\Http\\Controllers\\Dashboard\\ReportVisitorController.php:10)
[stacktrace]
#0 {main}
"} 
[2025-07-01 16:16:54] local.ERROR: Cannot declare class App\Http\Controllers\Reports\ReportVisitorController, because the name is already in use {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Cannot declare class App\\Http\\Controllers\\Reports\\ReportVisitorController, because the name is already in use at C:\\xampp\\htdocs\\acc_control_backend\\app\\Http\\Controllers\\Dashboard\\ReportVisitorController.php:10)
[stacktrace]
#0 {main}
"} 
[2025-07-01 16:16:56] local.ERROR: Cannot declare class App\Http\Controllers\Reports\ReportVisitorController, because the name is already in use {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Cannot declare class App\\Http\\Controllers\\Reports\\ReportVisitorController, because the name is already in use at C:\\xampp\\htdocs\\acc_control_backend\\app\\Http\\Controllers\\Dashboard\\ReportVisitorController.php:10)
[stacktrace]
#0 {main}
"} 
[2025-07-01 16:16:59] local.ERROR: Cannot declare class App\Http\Controllers\Reports\ReportVisitorController, because the name is already in use {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Cannot declare class App\\Http\\Controllers\\Reports\\ReportVisitorController, because the name is already in use at C:\\xampp\\htdocs\\acc_control_backend\\app\\Http\\Controllers\\Dashboard\\ReportVisitorController.php:10)
[stacktrace]
#0 {main}
"} 
[2025-07-01 16:18:22] local.ERROR: Cannot declare class App\Http\Controllers\Reports\ReportVisitorController, because the name is already in use {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Cannot declare class App\\Http\\Controllers\\Reports\\ReportVisitorController, because the name is already in use at C:\\xampp\\htdocs\\acc_control_backend\\app\\Http\\Controllers\\Dashboard\\ReportVisitorController.php:10)
[stacktrace]
#0 {main}
"} 
[2025-07-01 16:18:24] local.ERROR: Cannot declare class App\Http\Controllers\Reports\ReportVisitorController, because the name is already in use {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Cannot declare class App\\Http\\Controllers\\Reports\\ReportVisitorController, because the name is already in use at C:\\xampp\\htdocs\\acc_control_backend\\app\\Http\\Controllers\\Dashboard\\ReportVisitorController.php:10)
[stacktrace]
#0 {main}
"} 
[2025-07-01 16:18:27] local.ERROR: Cannot declare class App\Http\Controllers\Reports\ReportVisitorController, because the name is already in use {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Cannot declare class App\\Http\\Controllers\\Reports\\ReportVisitorController, because the name is already in use at C:\\xampp\\htdocs\\acc_control_backend\\app\\Http\\Controllers\\Dashboard\\ReportVisitorController.php:10)
[stacktrace]
#0 {main}
"} 
[2025-07-01 16:19:05] local.ERROR: Cannot declare class App\Http\Controllers\Reports\ReportVisitorController, because the name is already in use {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Cannot declare class App\\Http\\Controllers\\Reports\\ReportVisitorController, because the name is already in use at C:\\xampp\\htdocs\\acc_control_backend\\app\\Http\\Controllers\\Dashboard\\ReportVisitorController.php:10)
[stacktrace]
#0 {main}
"} 
[2025-07-01 16:20:49] local.ERROR: Cannot declare class App\Http\Controllers\Reports\ReportVisitorController, because the name is already in use {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Cannot declare class App\\Http\\Controllers\\Reports\\ReportVisitorController, because the name is already in use at C:\\xampp\\htdocs\\acc_control_backend\\app\\Http\\Controllers\\Dashboard\\ReportVisitorController.php:10)
[stacktrace]
#0 {main}
"} 
[2025-07-01 16:22:27] local.ERROR: Cannot declare class App\Http\Controllers\Reports\ReportVisitorController, because the name is already in use {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Cannot declare class App\\Http\\Controllers\\Reports\\ReportVisitorController, because the name is already in use at C:\\xampp\\htdocs\\acc_control_backend\\app\\Http\\Controllers\\Dashboard\\ReportVisitorController.php:10)
[stacktrace]
#0 {main}
"} 
[2025-07-01 16:23:32] local.ERROR: Cannot declare class App\Http\Controllers\Reports\ReportVisitorController, because the name is already in use {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Cannot declare class App\\Http\\Controllers\\Reports\\ReportVisitorController, because the name is already in use at C:\\xampp\\htdocs\\acc_control_backend\\app\\Http\\Controllers\\Dashboard\\ReportVisitorController.php:10)
[stacktrace]
#0 {main}
"} 
[2025-07-01 16:23:54] local.ERROR: Cannot declare class App\Http\Controllers\Reports\ReportVisitorController, because the name is already in use {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Cannot declare class App\\Http\\Controllers\\Reports\\ReportVisitorController, because the name is already in use at C:\\xampp\\htdocs\\acc_control_backend\\app\\Http\\Controllers\\Dashboard\\ReportVisitorController.php:10)
[stacktrace]
#0 {main}
"} 
[2025-07-01 16:23:57] local.ERROR: Cannot declare class App\Http\Controllers\Reports\ReportVisitorController, because the name is already in use {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Cannot declare class App\\Http\\Controllers\\Reports\\ReportVisitorController, because the name is already in use at C:\\xampp\\htdocs\\acc_control_backend\\app\\Http\\Controllers\\Dashboard\\ReportVisitorController.php:10)
[stacktrace]
#0 {main}
"} 
[2025-07-01 16:24:23] local.ERROR: Cannot declare class App\Http\Controllers\Reports\ReportVisitorController, because the name is already in use {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Cannot declare class App\\Http\\Controllers\\Reports\\ReportVisitorController, because the name is already in use at C:\\xampp\\htdocs\\acc_control_backend\\app\\Http\\Controllers\\Dashboard\\ReportVisitorController.php:10)
[stacktrace]
#0 {main}
"} 
[2025-07-01 16:24:25] local.ERROR: Cannot declare class App\Http\Controllers\Reports\ReportVisitorController, because the name is already in use {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Cannot declare class App\\Http\\Controllers\\Reports\\ReportVisitorController, because the name is already in use at C:\\xampp\\htdocs\\acc_control_backend\\app\\Http\\Controllers\\Dashboard\\ReportVisitorController.php:10)
[stacktrace]
#0 {main}
"} 
[2025-07-01 16:25:18] local.ERROR: Cannot declare class App\Http\Controllers\Reports\ReportVisitorController, because the name is already in use {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Cannot declare class App\\Http\\Controllers\\Reports\\ReportVisitorController, because the name is already in use at C:\\xampp\\htdocs\\acc_control_backend\\app\\Http\\Controllers\\Dashboard\\ReportVisitorController.php:10)
[stacktrace]
#0 {main}
"} 
[2025-07-01 16:25:22] local.ERROR: Cannot declare class App\Http\Controllers\Reports\ReportVisitorController, because the name is already in use {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Cannot declare class App\\Http\\Controllers\\Reports\\ReportVisitorController, because the name is already in use at C:\\xampp\\htdocs\\acc_control_backend\\app\\Http\\Controllers\\Dashboard\\ReportVisitorController.php:10)
[stacktrace]
#0 {main}
"} 
[2025-07-01 16:25:27] local.ERROR: Cannot declare class App\Http\Controllers\Reports\ReportVisitorController, because the name is already in use {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Cannot declare class App\\Http\\Controllers\\Reports\\ReportVisitorController, because the name is already in use at C:\\xampp\\htdocs\\acc_control_backend\\app\\Http\\Controllers\\Dashboard\\ReportVisitorController.php:10)
[stacktrace]
#0 {main}
"} 
[2025-07-01 16:25:29] local.ERROR: Cannot declare class App\Http\Controllers\Reports\ReportVisitorController, because the name is already in use {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Cannot declare class App\\Http\\Controllers\\Reports\\ReportVisitorController, because the name is already in use at C:\\xampp\\htdocs\\acc_control_backend\\app\\Http\\Controllers\\Dashboard\\ReportVisitorController.php:10)
[stacktrace]
#0 {main}
"} 
[2025-07-01 16:25:31] local.ERROR: Cannot declare class App\Http\Controllers\Reports\ReportVisitorController, because the name is already in use {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Cannot declare class App\\Http\\Controllers\\Reports\\ReportVisitorController, because the name is already in use at C:\\xampp\\htdocs\\acc_control_backend\\app\\Http\\Controllers\\Dashboard\\ReportVisitorController.php:10)
[stacktrace]
#0 {main}
"} 
[2025-07-01 16:25:54] local.ERROR: Cannot declare class App\Http\Controllers\Reports\ReportVisitorController, because the name is already in use {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Cannot declare class App\\Http\\Controllers\\Reports\\ReportVisitorController, because the name is already in use at C:\\xampp\\htdocs\\acc_control_backend\\app\\Http\\Controllers\\Dashboard\\ReportVisitorController.php:10)
[stacktrace]
#0 {main}
"} 
[2025-07-01 16:25:55] local.ERROR: Cannot declare class App\Http\Controllers\Reports\ReportVisitorController, because the name is already in use {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Cannot declare class App\\Http\\Controllers\\Reports\\ReportVisitorController, because the name is already in use at C:\\xampp\\htdocs\\acc_control_backend\\app\\Http\\Controllers\\Dashboard\\ReportVisitorController.php:10)
[stacktrace]
#0 {main}
"} 
[2025-07-01 16:25:59] local.ERROR: Cannot declare class App\Http\Controllers\Reports\ReportVisitorController, because the name is already in use {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Cannot declare class App\\Http\\Controllers\\Reports\\ReportVisitorController, because the name is already in use at C:\\xampp\\htdocs\\acc_control_backend\\app\\Http\\Controllers\\Dashboard\\ReportVisitorController.php:10)
[stacktrace]
#0 {main}
"} 
[2025-07-01 16:26:39] local.ERROR: Cannot declare class App\Http\Controllers\Reports\ReportVisitorController, because the name is already in use {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Cannot declare class App\\Http\\Controllers\\Reports\\ReportVisitorController, because the name is already in use at C:\\xampp\\htdocs\\acc_control_backend\\app\\Http\\Controllers\\Dashboard\\ReportVisitorController.php:10)
[stacktrace]
#0 {main}
"} 
[2025-07-01 16:28:17] local.ERROR: Cannot declare class App\Http\Controllers\Reports\ReportVisitorController, because the name is already in use {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Cannot declare class App\\Http\\Controllers\\Reports\\ReportVisitorController, because the name is already in use at C:\\xampp\\htdocs\\acc_control_backend\\app\\Http\\Controllers\\Dashboard\\ReportVisitorController.php:10)
[stacktrace]
#0 {main}
"} 
