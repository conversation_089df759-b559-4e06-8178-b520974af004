<?php

namespace App\Services\Dashboard;

use Illuminate\Support\Facades\DB;

class SummaryEmployeeService
{
    public function getSummaryHris()
    {
        $data = DB::connection('mysqlhris')
            ->select("
            select
                count(idNumber) as total,
                departementName as company,
                'HRIS' as source
            from vw_access_control A
            where isActive COLLATE utf8mb4_general_ci='YES'
            group by company, departementName
            order by departementName
        ");
        $result = [];
        foreach ($data as $value) {
            if (!str($value->company)->contains(["PT BDM", "PT BDT", "PT IMIP", "PT MSS"])) {
                $result[] = $value;
            }
        }

        return $result;
    }

    public function getSummaryCherry()
    {
        return DB::connection('sqlsrvcherry')
            ->select("
                SELECT
                    COUNT(*) as total,
                    Company as company,
                    'Cherry' as source
                from vw_employee_masterdata
                where IsActive='True'
                    and Company in ('PT BDM', 'PT BDT','PT IMIP')
                    and WorkLocation like '%MOROWALI%'
                group by Company
                order by Company
            ");
    }

    private function getEmployeeByCredentialNumber($credentialNumber, $company): ?array
    {
        $result = DB::connection('sqlsrvcherry')
            ->select("
                SELECT
                    Nik as credential_number,
                    Name as name,
                    Company as company
                from vw_employee_masterdata
                where IsActive='True'
                    and Nik='$credentialNumber'
                    and Company='$company'
                    and WorkLocation like '%MOROWALI%'
                order by Company
            ");
        return $result ? (array) $result[0] : null;
    }

    public function getSummaryZkbio()
    {
        return DB::connection("sqlsrv")
            ->select("
            SELECT
                count(*) as total,
                nameDept,
                REPLACE(nameDept, 'I-', 'PT ') as company
            FROM [ZKTECO].[dbo].[vw_personnel_masterdata]
            where nameDept like '%I-%'
            GROUP BY nameDept, nameDept

            union all

            select
                count(*) as total,
                parentDept as nameDept,
                CONCAT('PT ', parentDept) as company
            FROM [ZKTECO].[dbo].[vw_personnel_masterdata]
            where parentDept IN ('IMIP','BDT','BDM')
            GROUP BY parentDept

            order by company
        ");
    }

    public function getTotalZkbio()
    {
        return DB::connection("sqlsrv")
            ->select("
            SELECT
                count(*) as total
            FROM [ZKTECO].[dbo].[vw_personnel_masterdata]
            where  nameDept LIKE 'I-%' OR codeDept LIKE 'IN%'
        ")[0]->total;
    }

    public function getTotalHris(): float|int
    {
        $summary = $this->getSummaryHris();
        $total = 0;
        foreach ($summary as $key => $value) {
            $total += $value->total;
        }

        return $total;
    }

    public function getTotalCherry(): float|int
    {
        $summary = $this->getSummaryCherry();
        $total = 0;
        foreach ($summary as $key => $value) {
            $total += $value->total;
        }
        return $total;
    }

    public function getDetailHris($id)
    {
        $data = DB::connection('mysqlhris')
            ->select("
                select
                    idNumber as credential_number,
                    LEFT(Name, 15) as name,
                    LEFT(departementName, 10) as company
                from vw_access_control A
                where isActive COLLATE utf8mb4_general_ci='YES'
                and departementName='$id'
                order by idNumber
            ");

        $result = [];
        foreach ($data as $value) {
            if (!str($value->company)->contains(["PT BDM", "PT BDT", "PT IMIP"])) {
                $result[] = $value;
            }
        }

        return $result;
    }

    public function getDetailCherry($id)
    {
        return DB::connection('sqlsrvcherry')
            ->select("
                SELECT
                    Nik as credential_number,
                    LEFT(Name, 15) as name,
                    LEFT(Company, 10) as company
                from vw_employee_masterdata
                where IsActive='True'
                    and Company = '$id'
                    and WorkLocation like '%MOROWALI%'
                order by Nik
            ");
    }

    public function getDetailZkbioInternal($id)
    {
        $company = str($id)->replace("PT ", "I-");
        return DB::connection("sqlsrv")
            ->select("
            SELECT
                cardNumber as credential_number,
                LEFT(fullName, 15) as name,
                LEFT(nameDept, 10) as nameDept,
                LEFT(REPLACE(nameDept, 'I-', 'PT '), 10) as company
            FROM [ZKTECO].[dbo].[vw_personnel_masterdata]
            where nameDept ='$company'
            order by cardNumber
        ");
    }

    private function mappingCodeImipGroup($company): string
    {
        switch ($company) {
            case 'PT IMIP':
                return 'INMOIM';
            case 'PT BDM':
            case 'PT BDT':
                return 'INMOBD';
            default:
                return '';
        }
    }

    public function getDetailZkbioImipGroup($id): array
    {
        $deptCode = $this->mappingCodeImipGroup($id);
        $department = (str($id)->contains(["PT BDM", "PT BDT"])) ? $this->getDepartmentByCompany($id) : [];

        $departmentCondition = '';
        if (in_array($id, ["PT BDM", "PT BDT"]) && !empty($department)) {
            $departments = implode("','", $department);
            $departmentCondition = "and nameDept IN ('$departments')";
        }

        $data = DB::connection("sqlsrv")
            ->select("
            SELECT
                cardNumber as credential_number,
                LEFT(fullName, 15) as name,
                LEFT(nameDept, 10) as nameDept,
                REPLACE(nameDept, 'I-', 'PT ') as company
            FROM [ZKTECO].[dbo].[vw_personnel_masterdata] as personel
            left join [AC_PRD].[dbo].[departments] as dept on dept.dpt_code = personel.codeDept
            left join [AC_PRD].[dbo].[organizations] as org on org.id = dept.organization_id
            where LEFT(codeDept, 6) = '$deptCode'
            and concat('PT ', org.alias) = '$id'
            order by cardNumber
        ");

        return $data;
    }

    private function getDepartmentByCompany($company): ?array
    {
        $result = DB::connection('sqlsrvcherry')
            ->select("
                SELECT
                    Department
                from vw_employee_masterdata
                where IsActive='True'
                    and Company='$company'
                order by Company
            ");
        return collect($result)->pluck('Department')->toArray();
    }
}
