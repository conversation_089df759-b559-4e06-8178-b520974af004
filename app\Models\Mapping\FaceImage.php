<?php

namespace App\Models\Mapping;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Mapping\FaceImage
 *
 * @property int $id
 * @property int $file_id
 * @property string $file_name
 * @property int $created_by
 * @property int $nvr_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|FaceImage newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|FaceImage newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|FaceImage query()
 * @method static \Illuminate\Database\Eloquent\Builder|FaceImage whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FaceImage whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FaceImage whereFileId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FaceImage whereFileName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FaceImage whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FaceImage whereNvrId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FaceImage whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class FaceImage extends Model
{
    use HasFactory;
    protected $guarded = [];
}
