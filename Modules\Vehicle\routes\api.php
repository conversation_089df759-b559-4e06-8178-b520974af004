<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Modules\Vehicle\app\Http\Controllers\VehicleController;

/*
    |--------------------------------------------------------------------------
    | API Routes
    |--------------------------------------------------------------------------
    |
    | Here is where you can register API routes for your application. These
    | routes are loaded by the RouteServiceProvider within a group which
    | is assigned the "api" middleware group. Enjoy building your API!
    |
*/

Route::group(['middleware' => ['external.app'], 'prefix' => 'v1'], function () {
    Route::get('vehicle', [VehicleController::class, 'show']);
    Route::post('vehicle', [VehicleController::class, 'store']);
    Route::put('vehicle', [VehicleController::class, 'update']);
    Route::delete('vehicle', [VehicleController::class, 'destroy']);
    Route::get('vehicle/logVehicle', [VehicleController::class, 'getLogVehicle']);
    Route::get('vehicle/logVehicleAll', [VehicleController::class, 'getLogVehicleAll']);
    Route::get('vehicle/totalVehicle', [VehicleController::class, 'getTotalVehicle']);
});

