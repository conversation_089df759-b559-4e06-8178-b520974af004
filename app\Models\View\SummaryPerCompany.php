<?php

namespace App\Models\View;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\View\SummaryPerCompany
 *
 * @method static \Illuminate\Database\Eloquent\Builder|SummaryPerCompany newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SummaryPerCompany newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SummaryPerCompany query()
 * @mixin \Eloquent
 */
class SummaryPerCompany extends Model
{
    use HasFactory;

    protected $connection = 'sqlsrv';
    protected $table = 'Summary_per_pt';

    protected $casts = [
        "Zkbio" => 'integer',
        "NVR1" => 'integer',
        "NVR2" => 'integer',
        "NVR3" => 'integer',
        "NVR4" => 'integer',
    ];
}
