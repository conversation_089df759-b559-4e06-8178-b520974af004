<?php

namespace App\Events\Common;

use App\Models\User;
use App\Traits\MenuUser;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class UserMenuUpdated implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;
    use MenuUser;

    /**
     * The name of the connection the job should be sent to.
     *
     * @var string|null
     */
    // public $connection = 'sync';

    public string $appName;
    public int $userId;
    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct($appName, $userId)
    {
        $this->appName = $appName;
        $this->userId = $userId;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel('MenuUser.' . $this->userId);
    }

    public function broadcastAs()
    {
        return 'MenuUpdated';
    }

    /**
     * Get the data to broadcast.
     *
     * @return array
     */
    public function broadcastWith()
    {
        $user = User::find($this->userId);
        return ['menu' => $this->menuUser($this->appName, $user)];
    }
}
