<?php

namespace App\Http\Controllers\Settings;

use Throwable;
use Illuminate\Bus\Batch;
use App\Models\Applications;
use Illuminate\Http\Request;
use App\Models\Settings\Persons;
use App\Models\Settings\Setting;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use App\Jobs\Upload\ProcessUpdateZkbio;
use App\Services\Settings\EmployeeService;
use Modules\ExternalApi\app\Services\TransformDataApiService;
use Modules\ExternalApi\app\Services\ProcessDataExternalApiService;

class PersonsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request){
        $tags = [];
        $filter = json_decode($request->get('filters'));
       
        $data_persons = DB::table('log_transaction')
        ->select(
            'log_transaction.credential_number',
            'log_transaction.name',
            'log_transaction.log_type'
        )
        ->where(function ($q) use ($filter) {    
            if ($filter != '') {
                foreach ($filter as $tags) {
                    if ($tags != null) {   
                        $q->where('log_transaction.' . $tags->id, 'LIKE', '%' . $tags->value . '%');
                    }
                }
            }
        })
        ->whereIn('log_type',['PERSON','CONTRACTOR'])
        ->skip($request->get('start'))
        ->take($request->get('size'))
        ->limit(20)
        ->groupBy(
            'log_transaction.credential_number',
            'log_transaction.name',
            'log_transaction.log_type'
        )
        ->get();
        

        $dataAll = DB::table('log_transaction')
        ->select('log_transaction.name','log_transaction.credential_number','log_transaction.company')
        ->groupBy('log_transaction.name','log_transaction.credential_number','log_transaction.company')->get();
      
        try{
            return response()->json([
                'status'   => true,
                'message'  => 'Success',
                'data'     => $data_persons,
                'total'    => $dataAll->count(),
                'response' => $request->all(),
            ], 200);
        } catch (\Throwable $th) {
            return response()->json([
                'status'  => false,
                'message' => $th->getMessage(),
                'data'    => []
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
       
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {

        $data_history = DB::table('log_transaction')
        ->select(
            'log_transaction.credential_number',
            'log_transaction.name',
            'log_transaction.company',
            'log_transaction.type',
            'log_transaction.service_code',
            'log_transaction.service_message',
            'log_transaction.access_level_code',
            'log_transaction.access_level_name',
            'm_nvr.name as nvr_name',
            'm_nvr.id as nvr_id',
            'face_lists.face_list_name'
        )
        ->selectRaw("FORMAT(log_transaction.created_at, 'dd MMMM yyyy HH:mm:ss') as date_transaction")
        ->leftJoin('m_nvr', 'm_nvr.id', '=', 'log_transaction.nvr_id')
        ->leftJoin('face_lists', 'face_lists.face_list_id', '=', 'log_transaction.face_list_id')
        ->where('log_transaction.credential_number', base64_decode($id))
        ->whereIn('log_type',['PERSON','CONTRACTOR'])
        ->orderBy('log_transaction.created_at', 'desc')
        ->get();

        $data_profile = DB::table('log_transaction')
        ->select(
            'credential_number',
            'name',
            'company',
            'access_level_code',
            'access_level_name',
            'log_type',
        )
        ->where('credential_number', base64_decode($id))
        ->where('log_type','PERSON')
        ->where(function ($query) {
            $query->where('type', 'ZKBIO')
                  ->orWhere('type', 'HUAWEI');
        })
        ->orderBy('created_at', 'desc')
        ->first();
    
        $data_profile_zkbio = DB::table('ZKTECO.dbo.vw_personnel_masterdata')->where('cardNumber',base64_decode($id))->first();
        $dept               = (isset($data_profile_zkbio->nameDept)) ? $data_profile_zkbio->nameDept : '';
        $accessNames        = (isset($data_profile_zkbio->accLevelNames)) ? $data_profile_zkbio->accLevelNames : '';
        $accessCode         = (isset($data_profile_zkbio->accLevelCodes)) ? $data_profile_zkbio->accLevelCodes : '';
        $dataArray          = array();
        $dataArray[]        = array(
            'credential_number' => $data_profile->credential_number,
            'name'              => $data_profile->name,
            'company'           => $data_profile->company,
            'zkbioDept'         => $dept,
            'biometric'         => (isset($data_profile_zkbio->bioPhoto)) ? $data_profile_zkbio->bioPhoto : NULL ,
            'access_level_code' => explode(",",($accessCode!='') ? $accessCode : NULL ),
            'access_level_name' => explode(",",($accessNames!='') ? $accessNames : NULL )
        );

       try{
            return response()->json([
                'status'       => true,
                'message'      => 'Success',
                'data_profile' => $dataArray,
                'data_history' => $data_history,
            ], 200);
        } catch (\Throwable $th) {
            return response()->json([
                'status'  => false,
                'message' => $th->getMessage(),
                'data'    => []
            ], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        try{  
            $data = [
                'name'              => $request->name,
                'company'           => $request->company,
                'credentialNumber'  => $request->credential_number,
                'credentialType'    => 0,
                'access_level_code' => implode(',', $request->access_level_code),
                'access_level_name' => implode(',', $request->access_level_name),
                'nvr_id'            => implode(',', $request->nvr_id),
                'start_time'        => date('Y-m-d H:i:s', strtotime($request->start_time)),
                'end_time'          => date('Y-m-d H:i:s', strtotime($request->end_time)),
                'is_disabled'       => $request->is_disabled,
                'is_leave'          => $request->is_leave,
                'leave_date'          => date('Y-m-d', strtotime($request->leave_date)),
                'application_id'    => null,
                'birthday'          => null,
            ];

            $batch   = Bus::batch([])
                ->catch(function (Batch $batch, Throwable $e) {
                    Log::info('error upload batch ' . now(), [
                        'error'             => $e->getTraceAsString(),
                    ]);
                })
                ->name('Edit Zkbio '. date('Y-m-d H:i:s'))
                ->onQueue('ZkbioUpdatePerson')
                ->allowFailures()
                ->dispatch();

            $batch->add(new ProcessUpdateZkbio(row : $data, batch_id: $batch->id));

            return response()->json([
                'status'                    => true,
                'message'                   => 'Data Processing',
            ], 200);
        
        }catch(\Exception $e){
            return $this->success('Upload failed', [
                'batch'  => $e->getMessage()
            ]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }

    public function sync(Request $request)
    {
        $getEmployee = new EmployeeService();
        $employee    = $getEmployee->employee($request);

        $application = Applications::where('token', 'ee0d636b9781b5d673e1b129ffc3ee05ac913d3c8bf33d390750e8f507440f99')
            ->where('status', '1')
            ->first();

        $type        = 'GENERAL';

        $transform   = new TransformDataApiService();
        $service     = new ProcessDataExternalApiService();
        $data        = $transform->transform('ZkbioSync', $employee, $application, $type);
        $batch       = $service->processQueueZkbio(data: $data, application: $application);

        return response()->json([
            'message' => 'Data Face Records is currently being processed in the queue',
            'status' => 'processing',
            'worker' => [
                [
                    'service' => 'zkbio',
                    'device' => $batch
                ]
            ]
        ]);
    }


}
