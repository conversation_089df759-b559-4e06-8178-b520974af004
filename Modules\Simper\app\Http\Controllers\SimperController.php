<?php

namespace Modules\Simper\app\Http\Controllers;

use Carbon\Carbon;
use GuzzleHttp\Client;
use App\Models\Applications;
use Illuminate\Http\Request;
use GuzzleHttp\Promise\Utils;
use App\Models\Settings\Setting;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use App\Models\Settings\Log_transaction;
use Modules\Simper\app\Models\SimperDetail;
use Modules\Simper\app\Http\Requests\StoreSimperRequest;
use Modules\Simper\app\Http\Requests\DeleteSimperRequest;
use Modules\Simper\app\Http\Requests\UpdateSimperRequest;
use Modules\Simper\app\Services\ProcessDataSimperService;
use Modules\ExternalApi\app\Services\TransformDataApiService;
use Modules\Vehicle\app\Services\ProcessUpdateVehicleApiService;

class SimperController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {

    }

    /**
     * @param StoreSimperRequest $request
     * @return \Illuminate\Http\JsonResponse
     * @throws Throwable
     */
    public function store(StoreSimperRequest $request): JsonResponse
    {
        // return response()->json($request->all());
        $application = Applications::where('token', $request->bearerToken())
            ->where('status', '1')
            ->first();

        $transform = new TransformDataApiService();

        $service = new ProcessDataSimperService();
        if (str($request->service)->contains(["ALL", "Zkbio"])) {
            $data = $transform->transform('Zkbio', $request->form, $application, $request->accessType, true);
            // return response()->json($data);
            $batch = $service->processQueueZkbio($data, $application, 'simper');
            return response()->json([
                'message' => 'Data Face Records is currently being processed in the queue',
                'status' => 'processing',
                'worker' => [
                    [
                        'service' => 'zkbio',
                        'device' => $batch
                    ]
                ]
            ]);
        } else {
            return $this->error('Service Not Found!');
        }
    }

    /**
     * Show the specified resource.
     */
    public function show(Request $request)
    {
        $batchId = $request->batchId;
        Log::info('request', [
            'request all' => $request->all(),
            'batchId' => $request->batchId
        ]);
        $params = [];
        $application = Applications::where('token', $request->bearerToken())
            ->where('status', '1')
            ->first();


        foreach ($batchId as $id) {
            $batch = Bus::findBatch($id);

            if ($batch->finished()) {
                $logTransaction = Log_transaction::where('batch_id', $batch->id)
                    ->select(
                        "credential_number",
                        "name",
                        "company",
                        "access_level_code",
                        "access_level_name",
                        "service_code",
                        "service_message",
                        "batch_id"
                    )
                    // ->where("service_code", "=", "0")
                    ->whereApplicationId($application->id)
                    ->whereIn("type", ["HUAWEI", "ZKBIO"])
                    ->get();

                $params[] = [
                    'message' => 'success',
                    'batch_id' => $batch->id,
                    'status' => 'completed',
                    'name' => $batch->name,
                    "data" => $logTransaction
                ];
            } else {
                $logTransaction = Log_transaction::where('batch_id', $batch->id)
                    ->select(
                        "credential_number",
                        "name",
                        "company",
                        "access_level_code",
                        "access_level_name",
                        "service_code",
                        "service_message",
                        "batch_id"
                    )
                    ->where("service_code", "<>", "0")
                    ->whereApplicationId($application->id)
                    ->whereIn("type", ["HUAWEI", "ZKBIO"])
                    ->get();
                $params[] = [
                    'message' => 'success',
                    'batch_id' => $batch->id,
                    'name' => $batch->name,
                    'status' => 'error',
                    "data" => $logTransaction
                ];
            }
        }

        return response()->json($params);
    }

    /**
     * @param StoreSimperRequest $request
     * @return \Illuminate\Http\JsonResponse
     * @throws Throwable
     */
    public function update(StoreSimperRequest $request): JsonResponse
    {
        $application = Applications::where('token', $request->bearerToken())
            ->where('status', '1')
            ->first();

        $transform = new TransformDataApiService();
        $accLevelIds = $application->access_level_code;

        $service = new ProcessDataSimperService();
        if (str($request->service)->contains(['ALL', 'Zkbio'])) {
            $data = $transform->transform('Zkbio', $request->form, $application, $request->accessType, true);
            $batch = $service->processQueueZkbio($data, $application, "simper");
            return response()->json([
                'message' => 'Data Face Records is currently being processed in the queue',
                'status' => 'processing',
                'worker' => [
                    [
                        'service' => 'zkbio',
                        'device' => $batch
                    ]
                ]
            ]);
        } else {
            return $this->error('Service Not Found!');
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(DeleteSimperRequest $request): JsonResponse
    {
        $application = Applications::where('token', $request->bearerToken())
            ->where('status', '1')
            ->first();

        $transform = new TransformDataApiService();
        $accLevelIds = $application->access_level_code;

        $service = new ProcessDataSimperService();
        if (str($request->service)->contains(['ALL', 'Zkbio'])) {
            info("delete simper");
            $data = $transform->transform('Zkbio', $request->form, $application, $request->accessType, true);

            // return response()->json($data);
            $batch = $service->processQueueZkbio($data, $application, 'simper');
            return response()->json([
                'message' => 'Data Face Records is currently being processed in the queue',
                'status' => 'processing',
                'worker' => [
                    [
                        'service' => 'zkbio',
                        'device' => $batch
                    ]
                ]
            ]);
        } else {
            return $this->error('Service Not Found!');
        }
    }


    public function getLogSimperAll(Request $request)
    {
        $accessToken = Setting::getSetting('AccessTokenZkbio');
        $urlGetTransaction = Setting::getSetting('UrlGetTransactionZkbio');
        $client = new Client();
        
        $serialNumbers = array_map('trim', explode(',', $request->device));
        $noPage = $request->pageSize;
        $noSize = $request->noSize;

        $promises = [];
        
        foreach ($serialNumbers as $serialNumber) {
            $promises[$serialNumber] = $client->getAsync("{$urlGetTransaction}/{$serialNumber}", [
                'query' => [
                    'pageNo' => $noPage,
                    'pageSize' => $noSize,
                    'access_token' => $accessToken,
                ],
                'headers' => [
                    'Content-Type' => 'application/json',
                ],
            ]);
        }
        
        $responses = Utils::all($promises)->wait();
        $combinedData = [];

        foreach ($responses as $serialNumber => $response) {
            $data = json_decode($response->getBody()->getContents(), true);
            if (isset($data['data']['data'])) {
                $combinedData = array_merge($combinedData, $data['data']['data']);
            }
        }

        if (empty($combinedData)) {
            return response()->json([
                'message' => 'No data found',
                'service' => 'ZKBIO',
                'data'    => [],
                'total'   => 0
            ]);
        }

        $log = [];

        foreach ($combinedData as $item) {
            if ($item['pin'] == null) {
                continue;
            }

            $simper = SimperDetail::where('spr_nik', $item['pin'])->first();

            if (isset($simper)) {
                $log[] = [
                    'pin'       => $item['pin'],
                    'name'      => $item['name'],
                    'devName'   => $item['devName'],
                    'eventTime' => $item['eventTime'],
                    'eventName' => $item['eventName'],
                    'data'  =>  [
                        'company'     => $simper->perusahaan,
                        'simGroup'    => $simper->spr_gol_sim,
                        'noSimper'    => $simper->spr_kode,
                        'noIdCard'    => $simper->spr_nik,
                        'noPolice'    => $simper->spr_sim_polisi,
                        'typeVehicle' => $simper->spr_type_kend,
                        'name'        => $simper->spr_name,
                        'photo'       => "http://192.168.234.106:3080/hris/upload/emp_foto/".$simper->spr_nik.".jpg",
                        'expired'     => $this->getExpired($simper->spr_tgl_terbit, $simper->spr_tgl_berakhir)
                    ]
                ];
            } else {
                $log[] = [
                    'pin'       => $item['pin'],
                    'name'      => $item['name'],
                    'devName'   => $item['devName'],
                    'eventTime' => $item['eventTime'],
                    'eventName' => $item['eventName'],
                    'data'  =>  null
                ];
            }
        }

        return response()->json([
            'message' => 'Success',
            'service' => 'ZKBIO',
            'data'    => $log,
            'total'   => count($log)
        ]);
    }

    
    public function getTotalSimper(Request $request){

        $data = SimperDetail::selectRaw("
            FORMAT(spr_insert_date, 'MMMM yyyy') as month_year, 
            COUNT(id_simper) as total, 
            MONTH(spr_insert_date) as month_num
        ")
        ->whereYear('spr_insert_date', date('Y'))
        ->groupByRaw("FORMAT(spr_insert_date, 'MMMM yyyy'), MONTH(spr_insert_date)")
        ->orderBy('month_num') 
        ->get();
        
        $runningTotal = 0;
        foreach ($data as $item) {
            $runningTotal += $item->total;
            $item->running_total = $runningTotal;
        }

        return response()->json([
            'message' => 'Success',
            'data'    => $data
        ]);
    }


    private function getExpired($terbit = null, $berakhir = null){
        $spr_terbit = $terbit;
        $spr_berakhir = Carbon::parse($berakhir); 
        
        $today = Carbon::today();
        $oneMonthBefore = $today->copy()->addMonth();
        $oneWeekBefore = $today->copy()->addWeek();
        
        if ($spr_berakhir->equalTo($oneMonthBefore)) {
            return "Akan expired dalam 1 bulan.";
        } elseif ($spr_berakhir->equalTo($oneWeekBefore)) {
            return "Akan expired dalam 1 minggu.";
        } elseif ($spr_berakhir->equalTo($today)) {
            return "Hari ini adalah tanggal expired.";
        } elseif ($spr_berakhir->lessThan($today)) { 
            return "EXPIRED " . $spr_berakhir->toDateString();
        } else {
            return $spr_berakhir->toDateString(); 
        }
        
    }
}
