<?php

namespace App\Traits;

use App\Models\Settings\Config;

trait AppConfig
{
    public function storeConfig($name, $type, $value)
    {
        Config::create([
            'config_key' => $name,
            'config_type' => $type,
            'config_value' => $value
        ]);
    }

    public function getConfig($name, $type)
    {
        return Config::where('config_key', $name)
            ->where('config_type', $type)
            ->orderBy('created_at', 'desc')
            ->first();
    }
}
