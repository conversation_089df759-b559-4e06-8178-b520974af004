<?php

namespace App\Http\Controllers\Api\Service;

use Carbon\Carbon;
use App\Jobs\TestingJob;
use Illuminate\Bus\Batch;
use App\Models\Applications;
use App\Models\Transactions;
use Illuminate\Http\Request;
use App\Jobs\Service\PersonJob;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use App\Models\Settings\MappingDepartment;

class PersonController extends Controller
{
    //
    public function index(Request $request){
        
    }

    public function store(Request $request)
    {
        if($request->TypeMapping == "PRIVATE")
        {
            $dataMapping = MappingDepartment::where('m_mapping_dept.name',$request->MappingName)
            ->where('flag', 'PRIVATE')
            ->where('status', 1)
            ->first();
        }
        else
        {
            $dataMapping = MappingDepartment::where('m_mapping_dept.department_code','LIKE','%'.$request->DepartmentCode.'%')
            ->where('flag', 'GENERAL')
            ->where('status', 1)
            ->first();
        }

        $app = Applications::where('token',$request->bearerToken())->first();
        $dataTransaction = Transactions::create([
            'mapping_id'      => $dataMapping->id,
            'type'            => $request->TypeService,
            'credential'      => "",
            'applications_id' => $app->id,
            'nik'             => $request->Id,
            'created_at'      => Carbon::now(),
            'status'          => 1
        ]);

        $data           = $request->all();
        $data['Method'] = 'POST';
        $data['Action'] = '';
        $data['Token']  = $request->bearerToken();
         $data['DataMapping'] = $dataMapping;

        Bus::batch([
            new PersonJob($data)
        ])
        ->then(function (Batch $batch) use ($dataTransaction) {
            Transactions::where('id',$dataTransaction->id)
            ->update([
                'status'          => 2
            ]);
            Log::emergency($batch);
        })->catch(function (Batch $batch, Throwable $e) {

        })
        ->name('AddPerson-'.$data['Id'])
        ->onQueue($data['Priority'])
        ->dispatch();

        return response()->json([
            'status'     => true,
            'message'    => 'Success',
            'data'       => $data
        ], 200);
    }

    public function update(Request $request)
    {
        if($request->TypeMapping == "PRIVATE")
        {
            $dataMapping = MappingDepartment::where('m_mapping_dept.name',$request->MappingName)
            ->where('flag', 'PRIVATE')
            ->where('status', 1)
            ->first();
        }
        else
        {
            $dataMapping = MappingDepartment::where('m_mapping_dept.department_code','LIKE','%'.$request->DepartmentCode.'%')
            ->where('flag', 'GENERAL')
            ->where('status', 1)
            ->first();
        }

        $app = Applications::where('token',$request->bearerToken())->first();
        $dataTransaction = Transactions::create([
            'mapping_id'      => $dataMapping->id,
            'type'            => $request->TypeService,
            'credential'      => "",
            'applications_id' => $app->id,
            'nik'             => $request->Id,
            'created_at'      => Carbon::now(),
            'status'          => 1
        ]);

        $data                = $request->all();
        $data['Method']      = 'PUT';
        $data['Action']      = '';
        $data['Token']       = $request->bearerToken();
        $data['DataMapping'] = $dataMapping;

       $batch = Bus::batch([
            new PersonJob($data)
        ])
        ->then(function (Batch $batch) use ($dataTransaction) {
            Transactions::where('id',$dataTransaction->id)
            ->update([
                'status'          => 2
            ]);
        })->catch(function (Batch $batch, Throwable $e) {

        })
        ->onQueue($data['Priority'])
        ->name('UpdatePerson-'.$data['Id'])
        ->dispatch();

        return response()->json([
            'status'     => true,
            'message'    => 'Success',
            'data'       => $data
        ], 200);
    }

    public function disabled(Request $request)
    {
        $app = Applications::where('token',$request->bearerToken())->first();
        $dataTransaction = Transactions::create([
            'mapping_id'      => $dataMapping->id,
            'type'            => $request->TypeService,
            'credential'      => "",
            'applications_id' => $app->id,
            'nik'             => $request->Id,
            'created_at'      => Carbon::now(),
            'status'          => 1
        ]);

        $data           = $request->all();
        $data['Method'] = 'PUT';
        $data['Action'] = 'Disabled';
        $data['Token']  = $request->bearerToken();
        
        Bus::batch([
            new PersonJob($data)
        ])
        ->then(function (Batch $batch) use ($dataTransaction) {
            Transactions::where('id',$dataTransaction->id)
            ->update([
                'status'          => 2
            ]);
        })->catch(function (Batch $batch, Throwable $e) {

        })
        ->name('DisabledPerson-'.$data['Id'])
        ->onQueue($data['Priority'])
        ->dispatch();

        return response()->json([
            'status'     => true,
            'message'    => 'Success',
            'data'       => $data
        ], 200);
    }

    public function active(Request $request)
    {
        $app = Applications::where('token',$request->bearerToken())->first();
        $dataTransaction = Transactions::create([
            'mapping_id'      => $dataMapping->id,
            'type'            => $request->TypeService,
            'credential'      => "",
            'applications_id' => $app->id,
            'nik'             => $request->Id,
            'created_at'      => Carbon::now(),
            'status'          => 1
        ]);

        $data           = $request->all();
        $data['Method'] = 'PUT';
        $data['Action'] = 'Active';
        $data['Token']  = $request->bearerToken();
        
        Bus::batch([
            new PersonJob($data)
        ])
        ->then(function (Batch $batch) use ($dataTransaction) {
            Transactions::where('id',$dataTransaction->id)
            ->update([
                'status'          => 2
            ]);
        })->catch(function (Batch $batch, Throwable $e) {

        })
        ->name('ActivePerson-'.$data['Id'])
        ->onQueue($data['Priority'])
        ->dispatch();

        return response()->json([
            'status'     => true,
            'message'    => 'Success',
            'data'       => $data
        ], 200);
    }

    public function destroy($nvr, Request $request)
    {
        $app = Applications::where('token',$request->bearerToken())->first();
        $dataTransaction = Transactions::create([
            'mapping_id'      => $dataMapping->id,
            'type'            => $request->TypeService,
            'credential'      => "",
            'applications_id' => $app->id,
            'nik'             => $request->Id,
            'created_at'      => Carbon::now(),
            'status'          => 1
        ]);
        
        $data           = $request->all();
        $data['Method'] = 'DELETE';
        $data['Action'] = 'Delete';
        $data['Token']  = $request->bearerToken();
        
        Bus::batch([
            new PersonJob($data)
        ])
        ->then(function (Batch $batch) use ($dataTransaction) {
            Transactions::where('id',$dataTransaction->id)
            ->update([
                'status'          => 2
            ]);
        })->catch(function (Batch $batch, Throwable $e) {

        })
        ->name('DeletePerson-'.$data['Id'])
        ->onQueue($data['Priority'])
        ->dispatch();

        return response()->json([
            'status'     => true,
            'message'    => 'Success',
            'data'       => $data
        ], 200);
    }
}
