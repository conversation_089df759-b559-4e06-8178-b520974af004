<?php

namespace App\Http\Controllers\Api\Config;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;

class DepartmentContoller extends Controller
{
   public function index(Request $request){
        $response = DB::connection('sqlsrvcherry')->select("SELECT 'ALL' Department

        UNION  ALL
        
        SELECT 
            A.Department 
        FROM 
            vw_employee_masterdata A
        GROUP BY
            A.Department
        
        ");
        $data = array();
        foreach($response as $res){
            $data[] = array(
                'value' => $res->Department,
                'label' => $res->Department,
            );
        }
        try{
            return response()->json([
                'status'   => true,
                'message'  => 'Success',
                'data'     => $data,
            ], 200);
        } catch (\Throwable $th) {
            return response()->json([
                'status'  => false,
                'message' => $th->getMessage(),
                'data'    => []
            ], 500);
        }
   }
}
