import {
    formatDateForAPI,
    isValidDateRange,
    useCurrentMonthVisitorReport,
    useExportVisitorReport,
    useLastMonthVisitorReport,
    useVisitorReport,
    useVisitorReportSummary
} from '@/hooks/useVisitorReport';
import { DateRange } from '@/types/visitorReportDto';
import {
    ActionIcon,
    Alert,
    Badge,
    Box,
    Button,
    Card,
    Group,
    LoadingOverlay,
    NumberFormatter,
    ScrollArea,
    Skeleton,
    Stack,
    Table,
    Tabs,
    Text,
    Title,
    Tooltip
} from '@mantine/core';
import { DatePickerInput } from '@mantine/dates';
import { IconAlertCircle, IconCalendar, IconChartBar, IconDownload, IconFileExport, IconRefresh } from '@tabler/icons-react';
import { useState } from 'react';
import * as XLSX from 'xlsx';

export default function VisitorReport() {
    const [dateRange, setDateRange] = useState<DateRange>({
        startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1), // First day of current month
        endDate: new Date() // Today
    });
    const [activeTab, setActiveTab] = useState<string>('custom');

    // Prepare request object
    const reportRequest = {
        start_date: dateRange.startDate ? formatDateForAPI(dateRange.startDate) : '',
        end_date: dateRange.endDate ? formatDateForAPI(dateRange.endDate) : ''
    };

    // Fetch visitor report data based on active tab
    const {
        data: customReportData,
        isLoading: isCustomLoading,
        error: customError,
        refetch: refetchCustom,
        isFetching: isCustomFetching
    } = useVisitorReport(
        reportRequest,
        activeTab === 'custom' && isValidDateRange(dateRange.startDate, dateRange.endDate)
    );

    const {
        data: currentMonthData,
        isLoading: isCurrentMonthLoading,
        error: currentMonthError,
        refetch: refetchCurrentMonth,
        isFetching: isCurrentMonthFetching
    } = useCurrentMonthVisitorReport();

    const {
        data: lastMonthData,
        isLoading: isLastMonthLoading,
        error: lastMonthError,
        refetch: refetchLastMonth,
        isFetching: isLastMonthFetching
    } = useLastMonthVisitorReport();

    // Get summary data
    const {
        data: summaryData
    } = useVisitorReportSummary(
        reportRequest,
        activeTab === 'custom' && isValidDateRange(dateRange.startDate, dateRange.endDate)
    );

    // Export mutation
    const exportMutation = useExportVisitorReport();

    // Get current data based on active tab
    const getCurrentData = () => {
        switch (activeTab) {
            case 'current-month':
                return {
                    data: currentMonthData,
                    isLoading: isCurrentMonthLoading,
                    error: currentMonthError,
                    isFetching: isCurrentMonthFetching,
                    refetch: refetchCurrentMonth
                };
            case 'last-month':
                return {
                    data: lastMonthData,
                    isLoading: isLastMonthLoading,
                    error: lastMonthError,
                    isFetching: isLastMonthFetching,
                    refetch: refetchLastMonth
                };
            default:
                return {
                    data: customReportData,
                    isLoading: isCustomLoading,
                    error: customError,
                    isFetching: isCustomFetching,
                    refetch: refetchCustom
                };
        }
    };

    const { data: reportData, isLoading, error, isFetching, refetch } = getCurrentData();

    // Get dynamic columns from the first data row
    const getTableColumns = (): string[] => {
        if (!reportData?.data || reportData.data.length === 0) return [];
        return Object.keys(reportData.data[0]);
    };

    // Format column header for display
    const formatColumnHeader = (column: string): string => {
        return column
            .replace(/([A-Z])/g, ' $1') // Add space before capital letters
            .replace(/^./, str => str.toUpperCase()) // Capitalize first letter
            .trim();
    };

    // Export to Excel function
    const handleExportToExcel = () => {
        if (!reportData?.data || reportData.data.length === 0) {
            return;
        }

        const worksheet = XLSX.utils.json_to_sheet(reportData.data);
        const workbook = XLSX.utils.book_new();

        // Add title and date range info
        const getDateRangeText = () => {
            if (activeTab === 'current-month') return 'Current Month';
            if (activeTab === 'last-month') return 'Last Month';
            return `${dateRange.startDate?.toLocaleDateString()} to ${dateRange.endDate?.toLocaleDateString()}`;
        };

        XLSX.utils.book_append_sheet(workbook, worksheet, 'Visitor Report');

        // Generate filename
        const getFilename = () => {
            if (activeTab === 'current-month') return 'visitor_report_current_month.xlsx';
            if (activeTab === 'last-month') return 'visitor_report_last_month.xlsx';
            return `visitor_report_${reportRequest.start_date}_to_${reportRequest.end_date}.xlsx`;
        };

        XLSX.writeFile(workbook, getFilename());
    };

    // Export via API (CSV format)
    const handleExportCSV = () => {
        if (activeTab === 'custom' && (!dateRange.startDate || !dateRange.endDate)) {
            return;
        }

        const exportRequest = activeTab === 'custom'
            ? { ...reportRequest, format: 'csv' as const }
            : { start_date: '', end_date: '', format: 'csv' as const }; // Will be handled by API for current/last month

        exportMutation.mutate(exportRequest);
    };

    // Handle date range change
    const handleDateRangeChange = (field: 'startDate' | 'endDate', value: Date | null) => {
        setDateRange(prev => ({
            ...prev,
            [field]: value
        }));
    };

    // Render table content
    const renderTable = () => {
        if (isLoading) {
            return (
                <Box pos="relative" mih={200}>
                    <LoadingOverlay visible={true} />
                    <Skeleton height={200} />
                </Box>
            );
        }

        if (error) {
            return (
                <Alert 
                    icon={<IconAlertCircle size={16} />} 
                    color="red" 
                    variant="light"
                    title="Error Loading Data"
                >
                    {error instanceof Error ? error.message : 'Failed to load visitor report data'}
                </Alert>
            );
        }

        if (!reportData?.data || reportData.data.length === 0) {
            return (
                <Alert 
                    icon={<IconAlertCircle size={16} />} 
                    color="blue" 
                    variant="light"
                    title="No Data Found"
                >
                    No visitor data found for the selected date range.
                </Alert>
            );
        }

        const columns = getTableColumns();

        return (
            <ScrollArea>
                <Table striped highlightOnHover withTableBorder withColumnBorders>
                    <Table.Thead>
                        <Table.Tr>
                            <Table.Th style={{ minWidth: 50 }}>No</Table.Th>
                            {columns.map((column) => (
                                <Table.Th key={column} style={{ minWidth: 120 }}>
                                    {formatColumnHeader(column)}
                                </Table.Th>
                            ))}
                        </Table.Tr>
                    </Table.Thead>
                    <Table.Tbody>
                        {reportData.data.map((row, index) => (
                            <Table.Tr key={index}>
                                <Table.Td>{index + 1}</Table.Td>
                                {columns.map((column) => (
                                    <Table.Td key={column}>
                                        {row[column] !== null && row[column] !== undefined 
                                            ? String(row[column]) 
                                            : '-'
                                        }
                                    </Table.Td>
                                ))}
                            </Table.Tr>
                        ))}
                    </Table.Tbody>
                </Table>
            </ScrollArea>
        );
    };

    return (
        <Card shadow="sm" padding="lg" radius="md" withBorder>
            <Stack gap="md">
                {/* Header */}
                <Group justify="space-between" align="center">
                    <Title order={3}>Visitor Report</Title>
                    <Group gap="xs">
                        <Tooltip label="Refresh Data">
                            <ActionIcon
                                variant="light"
                                onClick={() => refetch()}
                                loading={isFetching}
                            >
                                <IconRefresh size={16} />
                            </ActionIcon>
                        </Tooltip>
                        <Button
                            leftSection={<IconDownload size={16} />}
                            variant="filled"
                            onClick={handleExportToExcel}
                            disabled={!reportData?.data || reportData.data.length === 0 || isLoading}
                        >
                            Export Excel
                        </Button>
                        <Button
                            leftSection={<IconFileExport size={16} />}
                            variant="outline"
                            onClick={handleExportCSV}
                            disabled={!reportData?.data || reportData.data.length === 0 || isLoading}
                            loading={exportMutation.isPending}
                        >
                            Export CSV
                        </Button>
                    </Group>
                </Group>

                {/* Tabs for different report periods */}
                <Tabs value={activeTab} onChange={(value) => setActiveTab(value || 'custom')}>
                    <Tabs.List>
                        <Tabs.Tab value="custom" leftSection={<IconCalendar size={16} />}>
                            Custom Range
                        </Tabs.Tab>
                        <Tabs.Tab value="current-month" leftSection={<IconChartBar size={16} />}>
                            Current Month
                        </Tabs.Tab>
                        <Tabs.Tab value="last-month" leftSection={<IconChartBar size={16} />}>
                            Last Month
                        </Tabs.Tab>
                    </Tabs.List>

                    <Tabs.Panel value="custom" pt="md">
                        {/* Date Range Picker - Only show for custom tab */}
                        <Card withBorder padding="md" bg="gray.0">
                            <Stack gap="sm">
                                <Text size="sm" fw={500}>Select Date Range</Text>
                                <Group grow>
                                    <DatePickerInput
                                        label="Start Date"
                                        placeholder="Select start date"
                                        value={dateRange.startDate}
                                        onChange={(value) => handleDateRangeChange('startDate', value)}
                                        leftSection={<IconCalendar size={16} />}
                                        clearable
                                        maxDate={dateRange.endDate || undefined}
                                    />
                                    <DatePickerInput
                                        label="End Date"
                                        placeholder="Select end date"
                                        value={dateRange.endDate}
                                        onChange={(value) => handleDateRangeChange('endDate', value)}
                                        leftSection={<IconCalendar size={16} />}
                                        clearable
                                        minDate={dateRange.startDate || undefined}
                                        maxDate={new Date()}
                                    />
                                </Group>
                                {!isValidDateRange(dateRange.startDate, dateRange.endDate) && (dateRange.startDate || dateRange.endDate) && (
                                    <Alert
                                        icon={<IconAlertCircle size={16} />}
                                        color="yellow"
                                        variant="light"
                                        size="sm"
                                    >
                                        Please select a valid date range (start date must be before or equal to end date)
                                    </Alert>
                                )}
                            </Stack>
                        </Card>
                    </Tabs.Panel>

                    <Tabs.Panel value="current-month" pt="md">
                        <Alert color="blue" variant="light">
                            Showing visitor data for the current month
                        </Alert>
                    </Tabs.Panel>

                    <Tabs.Panel value="last-month" pt="md">
                        <Alert color="blue" variant="light">
                            Showing visitor data for the last month
                        </Alert>
                    </Tabs.Panel>
                </Tabs>

                {/* Report Summary */}
                {reportData?.meta && (
                    <Card withBorder padding="sm" bg="blue.0">
                        <Group justify="space-between">
                            <Text size="sm">
                                <strong>Total Records:</strong> <NumberFormatter value={reportData.meta.total_records} thousandSeparator />
                            </Text>
                            <Badge variant="light" color="blue">
                                {reportData.meta.date_range.start_date} to {reportData.meta.date_range.end_date}
                            </Badge>
                        </Group>
                    </Card>
                )}

                {/* Summary Statistics (for custom range) */}
                {activeTab === 'custom' && summaryData?.summary && (
                    <Card withBorder padding="sm" bg="green.0">
                        <Group justify="space-between">
                            <Text size="sm">
                                <strong>Total Visitors:</strong> <NumberFormatter value={summaryData.summary.total_visitors} thousandSeparator />
                            </Text>
                            <Text size="xs" c="dimmed">
                                Generated: {new Date(summaryData.summary.generated_at).toLocaleString()}
                            </Text>
                        </Group>
                    </Card>
                )}

                {/* Data Table */}
                {renderTable()}
            </Stack>
        </Card>
    );
}
