<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Default Filesystem Disk
    |--------------------------------------------------------------------------
    |
    | Here you may specify the default filesystem disk that should be used
    | by the framework. The "local" disk, as well as a variety of cloud
    | based disks are available to your application. Just store away!
    |
    */

    'default' => env('FILESYSTEM_DISK', 'local'),

    /*
    |--------------------------------------------------------------------------
    | Filesystem Disks
    |--------------------------------------------------------------------------
    |
    | Here you may configure as many filesystem "disks" as you wish, and you
    | may even configure multiple disks of the same driver. Defaults have
    | been set up for each driver as an example of the required values.
    |
    | Supported Drivers: "local", "ftp", "sftp", "s3"
    |
    */

    'disks' => [

        'local' => [
            'driver' => 'local',
            'root' => storage_path('app'),
            'throw' => false,
        ],

        'public' => [
            'driver' => 'local',
            'root' => public_path(''),
            'url' => env('APP_URL').'/storage',
            'visibility' => 'public',
            'throw' => false,
        ],

        's3' => [
            'driver' => 's3',
            'key' => env('AWS_ACCESS_KEY_ID'),
            'secret' => env('AWS_SECRET_ACCESS_KEY'),
            'region' => env('AWS_DEFAULT_REGION'),
            'bucket' => env('AWS_BUCKET'),
            'url' => env('AWS_URL'),
            'endpoint' => env('AWS_ENDPOINT'),
            'use_path_style_endpoint' => env('AWS_USE_PATH_STYLE_ENDPOINT', false),
            'throw' => false,
        ],

        'ftp' => [
            'driver' => 'ftp',
            'host' => env('FTP_HOST'),
            'username' => env('FTP_USERNAME'),
            'password' => env('FTP_PASSWORD'),
            'port' => env('FTP_PORT', 21),
         
            // Optional FTP Settings...
            // 'root' => env('FTP_ROOT'),
            // 'passive' => true,
            // 'ssl' => true,
            // 'timeout' => 30,
        ],

        'sftp-hr' => [
            'driver' => 'sftp',
            'host' => env('SFTP_HOST_HRD'),
            'port' => intval(env('SFTP_PORT', 22)),
            'username' => env('SFTP_USERNAME_HRD'),
            'password' => env('SFTP_PASSWORD_HRD'),
            'privateKey' => null,
            'root' => env('SFTP_ROOT', '/hrd'),
            'timeout' => 60,
            'directoryPerm' => 0755,
            'Net_SFTP_Compatibility_Mode' => false,
            'hostFingerprint' => null,
            'useAgent' => false,
            'agent' => null,
            'disableMacCheck' => false,
            'proxy' => null,
            'visibility' => 'private', // adjust as needed
        ],

        'sftp-econ' => [
            'driver' => 'sftp',
            'host' => env('SFTP_HOST_ECON'),
            'port' => intval(env('SFTP_PORT', 22)),
            'username' => env('SFTP_USERNAME_ECON'),
            'password' => env('SFTP_PASSWORD_ECON'),
            'privateKey' => null,
            'timeout' => 60,
            'directoryPerm' => 0755,
            'Net_SFTP_Compatibility_Mode' => false,
            'hostFingerprint' => null,
            'useAgent' => false,
            'agent' => null,
            'disableMacCheck' => false,
            'proxy' => null,
            'visibility' => 'private', // adjust as needed
        ],

    ],

    /*
    |--------------------------------------------------------------------------
    | Symbolic Links
    |--------------------------------------------------------------------------
    |
    | Here you may configure the symbolic links that will be created when the
    | `storage:link` Artisan command is executed. The array keys should be
    | the locations of the links and the values should be their targets.
    |
    */

    'links' => [
        public_path('storage') => storage_path('app/public'),
    ],

];
