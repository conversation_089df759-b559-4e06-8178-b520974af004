<?php

namespace Modules\ExternalApi\app\Jobs;

use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Support\Collection;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use App\Services\ProcessDataPersonService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class ProcessMutationModule implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use Batchable;

    /**
     * Create a new job instance.
     */
    public function __construct(public array $rows)
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $Services = new ProcessDataPersonService();
        foreach ($this->rows as $key => $item) {
            $Services->processDeleteAccessLevel($item, $this->batch()->id);
        }
    }
}
