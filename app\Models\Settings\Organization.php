<?php

namespace App\Models\Settings;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\Settings\Organization
 *
 * @property int $id
 * @property string $name
 * @property int $organization_type_id
 * @property string $alias
 * @property int $status
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int|null $organization_group
 * @property-read mixed $organization_type
 * @property-read \App\Models\Settings\OrganizationType|null $organizationTypeList
 * @method static \Illuminate\Database\Eloquent\Builder|Organization newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Organization newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Organization onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Organization query()
 * @method static \Illuminate\Database\Eloquent\Builder|Organization whereAlias($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Organization whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Organization whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Organization whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Organization whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Organization whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Organization whereOrganizationGroup($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Organization whereOrganizationTypeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Organization whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Organization whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Organization whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Organization withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Organization withoutTrashed()
 * @property string|null $data_source
 * @property int|null $group_id
 * @property-read mixed $group_name
 * @property-read \App\Models\Settings\OrganizationGroup|null $organizationGroup
 * @method static \Illuminate\Database\Eloquent\Builder|Organization whereDataSource($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Organization whereGroupId($value)
 * @mixin \Eloquent
 */
class Organization extends Model
{
    use HasFactory;
    use SoftDeletes;
    protected $guarded = [];
    protected $appends = [
        'organization_type',
        'group_name'
    ];

    public function organizationTypeList()
    {
        return $this->belongsTo(OrganizationType::class, 'organization_type_id', 'id');
    }

    public function getOrganizationTypeAttribute()
    {
        return $this->organizationTypeList->name;
    }

    public function getGroupNameAttribute()
    {
        return $this->organizationGroup?->name;
    }

    public function organizationGroup(): BelongsTo
    {
        return $this->belongsTo(OrganizationGroup::class, 'group_id', 'id');
    }
}
