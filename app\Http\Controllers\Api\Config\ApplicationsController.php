<?php
namespace App\Http\Controllers\Api\Config;

use App\Models\Applications;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Str;
use Carbon\Carbon;

class ApplicationsController extends Controller
{
	public function index(){
        return response()->json([
            'status' 						=> true,
            'message' 						=> 'Success',
            'data' 							=>  Applications::all()
        ], 200);
    }

    public function store(Request $request): JsonResponse
    {
        //Insert data of Applications
    	try {
            $rules = [
                'name'                      => 'required|string',
                'link_url'                  => 'required|string',
            ];
    
            $validatedData                  = $request->validate($rules);
            DB::beginTransaction();

	        $applications 				    = Applications::create([
	            'name'     					=> $request->name,
	            'token'    					=> hash('sha256', $plainTextToken = Str::random(40)),
	            'link_url' 					=> $request->link_url,
                'post' 					    => $request->post,
                'delete' 				    => $request->delete,
                'put' 					    => $request->put,
                'index' 				    => $request->index,
	            'status' 					=> 1,
                'created_at'                => Carbon::now(),
	        ]);

            if($applications){
                DB::commit();
                return response()->json([
                    'success'               => true,
                    'message'               => 'Application '.$request->name.' created Success',
                ], 200);
            }else{
                DB::rollBack();
                return response()->json([
                    'success'               => false,
                    'message'               => "Failed to create applications '.$request->name.'"
                ], 401);
            }
	    } catch (\Throwable $th) {
            return response()->json([
                'status'  					=> false,
                'message' 					=> $th->getMessage()
            ], 500);
        }
    }

    //Update data of Applications
    public function update(Request $request, $id)
    {
        $rules = [
            'name'                          => 'required|string',
            'link_url'                      => 'required|string',
        ];

        $validatedData                      = $request->validate($rules);
        DB::beginTransaction();

        $data                               = Applications::where('id', $id)
        ->update($validatedData);

        if($data){
            DB::commit();
            return response()->json([
                'success'                   => true,
                'message'                   => 'Update Application '.$request->name.' Success',
            ], 200);
        }else{
            DB::rollBack();
            return response()->json([
                'success'                   => false,
                'message'                   => 'Update Application '.$request->name.' Failed!',
            ], 401);
        }
    }

    //Destroy data of Applications
    public function destroy(Request $request, $id)
    {
        $data                               = Applications::find($id);
        if($data->delete()){
            return response()->json([
                'success'                   => true,
                'message'                   => 'Delete Applications '.$request->name.' Success',
            ], 200);
        }else{
            return response()->json([
                'success'                   => false,
                'message'                   => 'Delete Application '.$request->name.' Failed!',
            ], 401);
        }
    }

    public function resettoken(Request $request, $id)
    {
        
        DB::beginTransaction();

        $data                               = Applications::where('id', $id)
                                            ->update(['token' => hash('sha256', $plainTextToken = Str::random(40))]);

        if($data){
            DB::commit();
            return response()->json([
                'success'                   => true,
                'message'                   => 'Reset Application '.$request->name.' Token Success',
            ], 200);
        }else{
            DB::rollBack();
            return response()->json([
                'success'                   => false,
                'message'                   => 'Reset Application '.$request->name.' Token Failed!',
            ], 401);
        }
    }

    public function changestatus(Request $request, $id)
    {
        
        DB::beginTransaction();

        $data_app                           = Applications::find($id);
        $status                             = 0;
        if($data_app->status == 0){
            $status                         = 1;
        }

        $data                               = Applications::where('id', $id)
                                            ->update(['status' => $status]);

        if($data){
            DB::commit();
            return response()->json([
                'success'                   => true,
                'message'                   => 'Change status Application '.$request->name.' Success',
            ], 200);
        }else{
            DB::rollBack();
            return response()->json([
                'success'                   => false,
                'message'                   => 'Change status Application '.$request->name.' Failed!',
            ], 401);
        }
    }

    //show
}