<?php

namespace App\Jobs\Upload;

use App\Services\Settings\Process;
use App\Services\Upload\BatchDeleteZkbioService;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Storage;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Support\Facades\Artisan;
use App\Models\User;
use App\Models\Settings\Log_transaction;
use Illuminate\Support\Facades\DB;


class ProcessDeleteZkbio implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use Batchable;

    public $row;
    public $user_id;
    /**
     * Create a new job instance.
     */
    public function __construct($row, $user_id)
    {
        $this->user_id = $user_id;
        $this->row = $row;
    }

    public function handle(): void
    {
        $Services                   = new BatchDeleteZkbioService();
        $rows                       = $this->row;
        $user_id                    = $this->user_id;
        $accessToken                = $Services->getAccessToken();
        $params_data                = array();

        Log::info('start delete person zkbio');
        $data_level = [];
        foreach ($rows as $row) {
            $pid = $row['strId'];

            $responseUser = $Services->getPerson($pid, $accessToken);
            Log::info('Data pid ' . now(), [
                'pid' => $pid,
            ]);
            
            /* Cek if Exist */
            $biometric = '';
            
            $setResponseUserCode = $responseUser->json();
            Log::info('Data prosseed ' . now(), [
                'setResponseUserCode' => $setResponseUserCode,
            ]);

            Log::info('Data prosseed ' . now(), [
                'setResponseUserCode_pin' => $setResponseUserCode['data']['pin'],
            ]);
            
            if(isset($setResponseUserCode['data']['pin'])){
                $data_level="";
                if (isset($setResponseUserCode['data']['accLevelIds'])) {
                    /* Get Access Level */
                    $data_level = $setResponseUserCode['data']['accLevelIds'];
                    
                } 
                /* Data Name */
                if (isset($setResponseUserCode['data']['name'])) {
                    $data_name = $setResponseUserCode['data']['name'];
                    $data_last_name = $setResponseUserCode['data']['lastName'];
                } else {
                    $data_name = $row['name'];
                    $data_last_name = '';
                }

                Artisan::call('cache:clear');
                Artisan::call('config:cache');            

                $data_emp = [
                    'workLocation' => $row['work_location'],
                    'company' => $row['org_alias'],
                    'dept' => $row['dept_name'],
                    'organization_group' => $row['org_group'],
                    'compType' => $row['org_type'],
                    'accessType' => 'GENERAL',
                ];

                $deptCode = app('App\Helpers\DepartmentCode')->deptCode($data_emp, 3);
                $code_depart = $deptCode['deptCode'];
                $name_depart = $deptCode['deptName'];

                $personPhoto = '';

                $params_data = [
                    'pin' => $pid,
                    'deptCode' => $code_depart,
                    'deptName' => $name_depart,
                    'name' => $data_name,
                    'lastName' => $data_last_name,
                    'birthday' => $row['bornTime'],
                    'gender' => $row['gender'],
                    'cardNo' => NULL,
                    'personPhoto' => $personPhoto,
                    'accLevelIds' => $data_level,
                    'is_leave' => $row['is_leave'],
                    'leaveDate' => $row['leaveDate'],
                    'statusEmployee' => $row['statusEmployee'],
                ];

                Log::info('Data params_data ' . now(), [
                    'params_data' => $params_data,
                ]);

                /* Call service Delete Person Data */
                Log::info('Data prosseed ' . now(), [
                    'delete person of ' => $params_data,
                ]);
                $Services->deletePerson($accessToken, $params_data, $row['company'], $data_level, $user_id);
            }else{
                /* Store data to log transaction */
                /* Return to final stage */
                $responseData = collect($setResponseUserCode);
                Log::info('There is no data '.$pid.' on Zkbio ! Time : ' . date("Y-m-d H:i:s"));
                $code_hasil = $responseData->has('code') 
                    ? $responseData['code'] 
                    : ($responseData->has('ret') ? $responseData['ret'] : -1);

                $message = $responseData->has('message') 
                    ? $responseData['message'] 
                    : ($responseData->has('msg') ? $responseData['msg'] : '-1');
                $params_log = array(
                    'credential_number'         => $pid,
                    'credential_type'           => 0,
                    'name'                      => $row['name'],
                    'company'                   => strtoupper($row['company']),
                    'type'                      => 'ZKBIO',
                    'access_level_code'         => NULL,
                    'access_level_name'         => NULL,
                    'status'                    => $row['statusEmployee'],
                    'application_id'            => null,
                    'service_code'              => $code_hasil,
                    'service_payload'           => '',
                    'service_message'           => $message,
                    'nvr_id'                    => NULL,
                    'face_list_id'              => NULL,
                    'log_type'                  => 'PERSON',
                    'method'                    => 'DELETE',
                    'created_by'                => $user_id,
                );
        
                $save_log                       = Log_transaction::create($params_log);
            }
        }

        Log::info('Delete person zkbio Done ! Time : ' . date("Y-m-d H:i:s"));
    }

    
}
