<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        $schedule->command('app:sync-company-hris')->dailyAt('23:00');
        $schedule->command('app:sync-company-contractor')->dailyAt('01:00');
        $schedule->command('app:sync-mapping-department')->dailyAt('02:00');
        $schedule->command('app:sync-mapping-dept-hris')->dailyAt('03:00');
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
