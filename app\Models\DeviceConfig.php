<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\DeviceConfig
 *
 * @property string $id
 * @property string $device_name
 * @property string $serial_number
 * @property string $area_name
 * @property int $created_by
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string $device_id
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\DeviceConfigDetail> $lineItems
 * @property-read int|null $line_items_count
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceConfig newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceConfig newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceConfig query()
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceConfig whereAreaName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceConfig whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceConfig whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceConfig whereDeviceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceConfig whereDeviceName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceConfig whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceConfig whereSerialNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceConfig whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class DeviceConfig extends Model
{
    use HasFactory;
    use HasUlids;

    protected $guarded = [];

    protected $appends = [
        "device_type_name"
    ];

    protected $casts = [
        'latitude' => 'float',
        'longitude' => 'float',
    ];

    public function getDeviceTypeNameAttribute()
    {

        switch ($this->device_type) {
            case 1:
                return 'Person';
            case 2:
                return 'Vehicle';
            default:
                return '';
        }
    }

    public function lineItems()
    {
        return $this->hasMany(DeviceConfigDetail::class);
    }
}
