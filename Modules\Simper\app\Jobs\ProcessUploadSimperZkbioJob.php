<?php

namespace Modules\Simper\app\Jobs;

use App\Models\Applications;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Support\Collection;
use Modules\Simper\app\Services\ProcessDataSimperService;

class ProcessUploadSimperZkbioJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use Batchable;

    public Collection $row;
    public string $accLevelIds;
    public $batchId;
    public $status;
    public Applications $application;

    /**
     * Create a new job instance.
     */
    public function __construct($row, $batchId, $application, $status)
    {
        $this->row = $row;
        $this->batchId = $batchId;
        $this->application = $application;
        $this->status = $status;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {

        //service process 1
        $rows = $this->row;

        $service = new ProcessDataSimperService();

        $service->uploadAccessLevelSimper($rows, $this->batchId, $this->application, $this->status);
    }
}
