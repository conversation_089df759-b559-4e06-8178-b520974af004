<?php

namespace App\Models\Mapping;

use App\Models\Settings\AccessLevel;
use App\Models\Settings\FaceList;
use App\Models\Settings\Organization;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\Mapping\MappingAccess
 *
 * @property int $id
 * @property string $name
 * @property string $mapping_code
 * @property int $face_list_id
 * @property int $access_level_id
 * @property int $mapping_access_type_id
 * @property string|null $description
 * @property int $status
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read AccessLevel|null $accessLevel
 * @property-read FaceList|null $faceList
 * @property-read \App\Models\Mapping\MappingAccessType|null $mappingAccessType
 * @property-read Organization|null $organization
 * @method static \Illuminate\Database\Eloquent\Builder|MappingAccess newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MappingAccess newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MappingAccess onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|MappingAccess query()
 * @method static \Illuminate\Database\Eloquent\Builder|MappingAccess whereAccessLevelId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MappingAccess whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MappingAccess whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MappingAccess whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MappingAccess whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MappingAccess whereFaceListId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MappingAccess whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MappingAccess whereMappingAccessTypeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MappingAccess whereMappingCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MappingAccess whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MappingAccess whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MappingAccess whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MappingAccess whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MappingAccess withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|MappingAccess withoutTrashed()
 * @mixin \Eloquent
 */
class MappingAccess extends Model
{
    use HasFactory;
    use SoftDeletes;
    protected $guarded = [];

    public function mappingAccessType()
    {
        return $this->belongsTo(MappingAccessType::class);
    }

    public function faceList()
    {
        return $this->belongsTo(FaceList::class);
    }

    public function accessLevel()
    {
        return $this->belongsTo(AccessLevel::class);
    }

    public function organization()
    {
        return $this->belongsTo(Organization::class);
    }
}
