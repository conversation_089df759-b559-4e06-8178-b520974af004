USE [AC_PRD]
GO

/****** Object:  StoredProcedure [dbo].[sp_ReportVisitorSimple]    Script Date: 02/07/2025 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[sp_ReportVisitorSimple]
    @startDate DATE,
    @endDate DATE
AS
BEGIN
    SET NOCOUNT ON;
    
    -- Simple version: Return data as rows (not pivoted)
    SELECT  
        A.company,
        CAST(A.created_at AS DATE) AS created_at,
        COUNT(*) AS Total
    FROM log_transaction A
    WHERE A.application_id = 24
    AND A.service_code = '0'
    AND A.status = 3
    AND A.type = 'Zkbio'
    AND CAST(A.created_at AS DATE) BETWEEN @startDate AND @endDate
    GROUP BY A.company, CAST(A.created_at AS DATE)
    ORDER BY A.company, CAST(A.created_at AS DATE);
    
END;
GO

-- Alternative: With summary totals
CREATE PROCEDURE [dbo].[sp_ReportVisitorWithSummary]
    @startDate DATE,
    @endDate DATE
AS
BEGIN
    SET NOCOUNT ON;
    
    -- Main data
    SELECT  
        A.company,
        CAST(A.created_at AS DATE) AS created_at,
        COUNT(*) AS Total,
        'DATA' AS row_type
    FROM log_transaction A
    WHERE A.application_id = 24
    AND A.service_code = '0'
    AND A.status = 3
    AND A.type = 'Zkbio'
    AND CAST(A.created_at AS DATE) BETWEEN @startDate AND @endDate
    GROUP BY A.company, CAST(A.created_at AS DATE)
    
    UNION ALL
    
    -- Company totals
    SELECT  
        A.company,
        NULL AS created_at,
        COUNT(*) AS Total,
        'COMPANY_TOTAL' AS row_type
    FROM log_transaction A
    WHERE A.application_id = 24
    AND A.service_code = '0'
    AND A.status = 3
    AND A.type = 'Zkbio'
    AND CAST(A.created_at AS DATE) BETWEEN @startDate AND @endDate
    GROUP BY A.company
    
    UNION ALL
    
    -- Daily totals
    SELECT  
        'DAILY_TOTAL' AS company,
        CAST(A.created_at AS DATE) AS created_at,
        COUNT(*) AS Total,
        'DAILY_TOTAL' AS row_type
    FROM log_transaction A
    WHERE A.application_id = 24
    AND A.service_code = '0'
    AND A.status = 3
    AND A.type = 'Zkbio'
    AND CAST(A.created_at AS DATE) BETWEEN @startDate AND @endDate
    GROUP BY CAST(A.created_at AS DATE)
    
    UNION ALL
    
    -- Grand total
    SELECT  
        'GRAND_TOTAL' AS company,
        NULL AS created_at,
        COUNT(*) AS Total,
        'GRAND_TOTAL' AS row_type
    FROM log_transaction A
    WHERE A.application_id = 24
    AND A.service_code = '0'
    AND A.status = 3
    AND A.type = 'Zkbio'
    AND CAST(A.created_at AS DATE) BETWEEN @startDate AND @endDate
    
    ORDER BY 
        CASE row_type 
            WHEN 'DATA' THEN 1 
            WHEN 'COMPANY_TOTAL' THEN 2 
            WHEN 'DAILY_TOTAL' THEN 3 
            WHEN 'GRAND_TOTAL' THEN 4 
        END,
        company, 
        created_at;
        
END;
GO
