import { useQuery, useMutation } from '@tanstack/react-query';
import axios from '@/lib/axios';
import { 
    VisitorReportResponse, 
    VisitorReportRequest,
    VisitorReportSummaryResponse,
    VisitorReportExportRequest,
    VisitorReportExportResponse
} from '@/types/visitorReportDto';

// API endpoints
const API_ENDPOINTS = {
    GET_REPORT: '/api/visitor-reports/get-report',
    CURRENT_MONTH: '/api/visitor-reports/current-month',
    LAST_MONTH: '/api/visitor-reports/last-month',
    EXPORT: '/api/visitor-reports/export',
    SUMMARY: '/api/visitor-reports/summary',
} as const;

// Hook for getting visitor report by date range
export const useVisitorReport = (request: VisitorReportRequest, enabled: boolean = true) => {
    return useQuery({
        queryKey: ['visitor-report', request.start_date, request.end_date],
        queryFn: async (): Promise<VisitorReportResponse> => {
            const response = await axios.post(API_ENDPOINTS.GET_REPORT, request);
            return response.data;
        },
        enabled: enabled && !!request.start_date && !!request.end_date,
        retry: 2,
        staleTime: 5 * 60 * 1000, // 5 minutes
        gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
    });
};

// Hook for getting current month visitor report
export const useCurrentMonthVisitorReport = () => {
    return useQuery({
        queryKey: ['visitor-report', 'current-month'],
        queryFn: async (): Promise<VisitorReportResponse> => {
            const response = await axios.get(API_ENDPOINTS.CURRENT_MONTH);
            return response.data;
        },
        retry: 2,
        staleTime: 5 * 60 * 1000,
        gcTime: 10 * 60 * 1000,
    });
};

// Hook for getting last month visitor report
export const useLastMonthVisitorReport = () => {
    return useQuery({
        queryKey: ['visitor-report', 'last-month'],
        queryFn: async (): Promise<VisitorReportResponse> => {
            const response = await axios.get(API_ENDPOINTS.LAST_MONTH);
            return response.data;
        },
        retry: 2,
        staleTime: 5 * 60 * 1000,
        gcTime: 10 * 60 * 1000,
    });
};

// Hook for getting visitor report summary
export const useVisitorReportSummary = (request: VisitorReportRequest, enabled: boolean = true) => {
    return useQuery({
        queryKey: ['visitor-report-summary', request.start_date, request.end_date],
        queryFn: async (): Promise<VisitorReportSummaryResponse> => {
            const response = await axios.post(API_ENDPOINTS.SUMMARY, request);
            return response.data;
        },
        enabled: enabled && !!request.start_date && !!request.end_date,
        retry: 2,
        staleTime: 5 * 60 * 1000,
        gcTime: 10 * 60 * 1000,
    });
};

// Hook for exporting visitor report
export const useExportVisitorReport = () => {
    return useMutation({
        mutationFn: async (request: VisitorReportExportRequest): Promise<VisitorReportExportResponse | Blob> => {
            const response = await axios.post(API_ENDPOINTS.EXPORT, request, {
                responseType: request.format === 'csv' ? 'blob' : 'json',
            });

            // If CSV format, return blob for download
            if (request.format === 'csv') {
                return response.data as Blob;
            }

            return response.data as VisitorReportExportResponse;
        },
        onSuccess: (data, variables) => {
            // If CSV format, trigger download
            if (variables.format === 'csv' && data instanceof Blob) {
                const url = window.URL.createObjectURL(data);
                const link = document.createElement('a');
                link.href = url;
                link.download = `visitor_report_${variables.start_date}_to_${variables.end_date}.csv`;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                window.URL.revokeObjectURL(url);
            }
        },
        onError: (error) => {
            console.error('Export failed:', error);
        },
    });
};

// Utility function to format date for API
export const formatDateForAPI = (date: Date): string => {
    return date.toISOString().split('T')[0];
};

// Utility function to get current month date range
export const getCurrentMonthDateRange = (): VisitorReportRequest => {
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    
    return {
        start_date: formatDateForAPI(startOfMonth),
        end_date: formatDateForAPI(endOfMonth),
    };
};

// Utility function to get last month date range
export const getLastMonthDateRange = (): VisitorReportRequest => {
    const now = new Date();
    const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);
    
    return {
        start_date: formatDateForAPI(startOfLastMonth),
        end_date: formatDateForAPI(endOfLastMonth),
    };
};

// Utility function to validate date range
export const isValidDateRange = (startDate: Date | null, endDate: Date | null): boolean => {
    if (!startDate || !endDate) return false;
    return startDate <= endDate;
};
