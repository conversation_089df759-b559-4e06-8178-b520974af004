<?php

namespace App\Services;

use App\Events\Common\UserMenuUpdated;
use App\Models\Settings\Permission;
use App\Models\User;
use App\Traits\RolePermission;
use Illuminate\Support\Facades\DB;

class PermissionService
{
    use RolePermission;

    /**
     * @param $form
     *
     * @return void
     */
    public function processPermission($form)
    {
        $data = $this->data($form);

        if ($form['is_crud'] == 'Y') {
            $this->generatePermission((object)$data, '-index', 'Y');
        } else {
            if ($form['index']) {
                $this->generatePermission((object)$data, '-index', 'Y');
            }

            if ($form['store']) {
                $this->generatePermission((object)$data, '-store', 'Y');
            }

            if ($form['edits']) {
                $this->generatePermission((object)$data, '-edits', 'Y');
            }

            if ($form['erase']) {
                $this->generatePermission((object)$data, '-erase', 'Y');
            }
        }
    }

    /**
     * @param $form
     *
     * @return array
     */
    protected function data($form): array
    {
        if (array_key_exists('parent_id', $form)) {
            $parent_name = (is_array($form['parent_id'])) ? $form['parent_id']['name'] : $form['parent_id'];
        } else {
            $parent_name = null;
        }
        $parent = Permission::where('id', $parent_name)->first();
        return [
            'name' => $this->getValueNull('menu_name', $form),
            'menu_name' => $this->getValueNull('menu_name', $form),
            'parent_id' => ($parent) ? $parent->id : 0,
            'icon' => $this->getValueNull('icon', $form),
            'route_name' => $this->getValueNull('route_name', $form),
            'has_child' => $this->getValueNull('has_child', $form),
            'has_route' => $this->getValueNull('has_route', $form, 'N'),
            'order_line' => $this->getValueNull('order_line', $form),
            'is_crud' => $this->getValueNull('is_crud', $form),
            'role' => $this->getValueNull('role', $form),
            'guard_name' => 'sanctum',
        ];
    }

    /**
     * @param $value
     * @param $form
     * @param $default
     *
     * @return null
     */
    public function getValueNull($value, $form, $default = null)
    {
        return (array_key_exists($value, $form)) ? $form[$value] : $default;
    }

    /**
     * Summary of removeRolePermission
     * @param mixed $permission_id
     * @return void
     */
    public function removeRolePermission($permission_id)
    {
        DB::table('role_has_permissions')
            ->where('permission_id', '=', $permission_id)
            ->delete();
    }

    /**
     * Summary of removeUserPermission
     * @param mixed $permission_id
     * @return void
     */
    public function removeUserPermission($permission_id)
    {

        $users = User::leftJoin('model_has_permissions', 'model_has_permissions.model_id', 'users.id')
            ->select('users.*')
            ->where('model_has_permissions.permission_id', '=', $permission_id)
            ->get();

        if ($users) {
            foreach ($users as $user) {
                broadcast(new UserMenuUpdated('', $user->id));
            }
        }

        DB::table('model_has_permissions')
            ->where('model_type', '=', 'App\Models\User')
            ->where('permission_id', '=', $permission_id)
            ->delete();
    }

    public function frontEndHeader()
    {
        return [
            [
                "accessorKey" => 'menu_name',
                "header" => 'Name',
            ],
            [
                "accessorKey" => 'parena_name',
                "header" => 'Parent Name',
            ],
            [
                "accessorKey" => 'route_name',
                "header" => 'Route',
            ],
            [
                "accessorKey" => 'icon',
                "header" => 'Icon',
            ],
            [
                "accessorKey" => 'index',
                "header" => 'Index',
            ],
            [
                "accessorKey" => 'store',
                "header" => 'Store',
            ],
            [
                "accessorKey" => 'edits',
                "header" => 'Edit',
            ],
            [
                "accessorKey" => 'erase',
                "header" => 'Delete',
            ],
        ];
    }
}
