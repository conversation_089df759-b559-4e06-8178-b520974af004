<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::table('organizations')
            ->where('alias', 'BDM')
            ->update(['data_source' => 'cherry']);

        DB::table('organizations')
            ->where('alias', '<>', 'BDM')
            ->update(['data_source' => 'ftp']);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::table('organizations')
            ->update(['data_source' => null]);
    }
};
