<?php

namespace App\Models\Mapping;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Mapping\HrisImage
 *
 * @method static \Illuminate\Database\Eloquent\Builder|HrisImage newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|HrisImage newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|HrisImage query()
 * @mixin \Eloquent
 */
class HrisImage extends Model
{
    use HasFactory;
    protected $guarded = [];
}
