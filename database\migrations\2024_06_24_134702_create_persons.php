<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('persons', function (Blueprint $table) {
            $table->id();
            $table->string('credential_number');
            $table->string('credential_type'); // Type Employee
            $table->string('name');
            $table->string('company');
            $table->string('department');
            $table->string('type')->nullable(); // ZKBIO or HUAWEI
            $table->text('nvr_name')->nullable();
            $table->text('nvr_code')->nullable();
            $table->text('face_list_code')->nullable();
            $table->text('access_level_code')->nullable();
            $table->text('access_level_name')->nullable();
            $table->unsignedSmallInteger('status')->default(0);
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->string('photo_name')->nullable();
            $table->softDeletes();
            $table->timestamps();
            $table->index(['credential_number']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('persons');
    }
};
