<?php

namespace Modules\ExternalApi\app\Services;

use App\Models\Applications;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;

class TransformDataApiService
{
    /**
     * @param string $service
     * @param array $form
     * @param string $accessType
     * @return array
     */
    public function transform(string $service, array $form, $application, $accessType, $isSimper = false): array
    {
        if ($service == 'Huawei') {
            return $this->transformHuawei($form, $application);
        } elseif ($service == 'Zkbio') {
            return $this->transformZkbio($form, $application, $accessType, $isSimper);
        } 
        elseif($service == 'ZkbioSync'){
            return $this->transformSyncZkbio($form, $application, $accessType, $isSimper);
        }
        elseif ($service == 'ZkbioVehicle') {
            return $this->transformZkbioVehicle($form, $application, $accessType);
        } elseif ($service == 'changePhoto') {
            return $this->transformPhoto($form, $application, $accessType);
        } else {
            return [];
        }
    }

    /**
     * @param array $form
     * @param Applications $application
     * @return array
     */
    protected function transformHuawei(array $form, Applications $application): array
    {
        $data = [];
        foreach ($form as $item) {
            $data[] = [
                'photoName' => $item['photo'],
                'name' => preg_replace('/[^A-Za-z0-9 !@#$%^&*().]/u', '', strip_tags($item['name'])),
                'credentialNumber' => $item['identifyNumber'],
                'oldCredentialNumber' => (isset($item['oldIdentifyNumber'])) ? $item['oldIdentifyNumber'] : '',
                'strId' => $item['identifyNumber'],
                'credentialType' => ($item['type'] == 'ID Card') ? 0 : 1,
                'gender' => (strtoupper($item['gender']) == 'MALE') ? 0 : 1,
                'bornTime' => date("Y-m-d", strtotime($item['birthDate'])),
                'country' => $item['nationality'],
                'department' => $item['departmentName'],
                'occupation' => $item['employeeType'],
                'description' => $item['description'],
                'company' => $item['company'],
                'base64' => $item['base64'],
                'oldCompany' => (isset($item['oldCompany'])) ? $item['oldCompany'] : '',
                'application_id' => $application->id,
                'statusEmployee' => (array_key_exists('statusEmployee', $item)) ? $item['statusEmployee'] : '3',
            ];
        }

        return $data;
    }

    /**
     * @param array $form
     * @param Applications $application
     * @param  string $accessType
     * @return array
     */
    protected function transformZkbio(array $form, Applications $application, $accessType, $isSimper = false): array
    {
        $data = [];
        $accessLevelService = new AccessLevelService();
        foreach ($form as $item) {
            
            
            if($accessType == 'CONTRACTOR'){
                $accessLevel = $accessLevelService->getAccessLevel(
                    $item['company'],
                    $item['departmentName'],
                    $accessType,
                    $item['identifyNumber'],
                    $isSimper
                );
                $data[] = [
                    'photoName' => $item['photo'],
                    'name' => preg_replace('/[^A-Za-z0-9 !@#$%^&*().]/u', '', strip_tags($item['name'])),
                    'credentialNumber' => $item['identifyNumber'],
                    'oldCredentialNumber' => (isset($item['oldIdentifyNumber'])) ? $item['oldIdentifyNumber'] : '',
                    'strId' => $item['identifyNumber'],
                    'credentialType' => ($item['type'] == 'ID Card') ? 0 : 1,
                    'gender' => (strtoupper($item['gender']) == 'MALE') ? 'M' : 'F',
                    'bornTime' => date("Y-m-d", strtotime($item['birthDate'])),
                    'country' => $item['nationality'],
                    'department' => $item['departmentName'],
                    'occupation' => $item['employeeType'],
                    'description' => $item['description'],
                    'company' => $item['company'],
                    'oldCompany' => (isset($item['oldCompany'])) ? $item['oldCompany'] : '',
                    'work_location' => $item['workLocation'],
                    'dept_name' => $item['departmentName'],
                    'application_id' => $application->id,
                    'cardNumber' => (isset($item['cardNumber'])) ? $item['cardNumber'] : '',
                    'accStartTime' => (isset($item['accStartTime'])) ? $item['accStartTime'] : '',
                    'accEndTime' => (isset($item['accEndTime'])) ? $item['accEndTime'] : '',
                    'access_level_code' => (isset($accessLevel['access_level_code'])) ? $accessLevel['access_level_code'] : '',
                    'access_level_name' => (isset($accessLevel['access_level_name'])) ? $accessLevel['access_level_name'] : '',
                    'access_type' => (isset($accessLevel['access_type'])) ? $accessLevel['access_type'] : 'CONTRACTOR',
                    'statusEmployee' => (array_key_exists('statusEmployee', $item)) ? $item['statusEmployee'] : '3',
                    'base64' => array_key_exists('base64', $item) ? $item['base64'] : null,
                    'leave_date' => array_key_exists('leaveDate', $item) ? $item['leaveDate'] : null,
                    'log_type' => 'CONTRACTOR',
                ];
            }else{
                $accessLevel = $accessLevelService->getAccessLevel(
                    $item['company'],
                    $item['departmentName'],
                    $accessType,
                    $item['identifyNumber'],
                    $isSimper
                );
                $data[] = [
                    'photoName' => $item['photo'],
                    'name' => preg_replace('/[^A-Za-z0-9 !@#$%^&*().]/u', '', strip_tags($item['name'])),
                    'credentialNumber' => $item['identifyNumber'],
                    'oldCredentialNumber' => (isset($item['oldIdentifyNumber'])) ? $item['oldIdentifyNumber'] : '',
                    'strId' => $item['identifyNumber'],
                    'credentialType' => ($item['type'] == 'ID Card') ? 0 : 1,
                    'gender' => (strtoupper($item['gender']) == 'MALE') ? 'M' : 'F',
                    'bornTime' => date("Y-m-d", strtotime($item['birthDate'])),
                    'country' => $item['nationality'],
                    'department' => $item['departmentName'],
                    'occupation' => $item['employeeType'],
                    'description' => $item['description'],
                    'company' => $item['company'],
                    'oldCompany' => (isset($item['oldCompany'])) ? $item['oldCompany'] : '',
                    'work_location' => $item['workLocation'],
                    'dept_name' => $item['departmentName'],
                    'application_id' => $application->id,
                    'cardNumber' => (isset($item['cardNumber'])) ? $item['cardNumber'] : '',
                    'accStartTime' => (isset($item['accStartTime'])) ? $item['accStartTime'] : '',
                    'accEndTime' => (isset($item['accEndTime'])) ? $item['accEndTime'] : '',
                    'access_level_code' => (isset($accessLevel['access_level_code'])) ? $accessLevel['access_level_code'] : '',
                    'access_level_name' => (isset($accessLevel['access_level_name'])) ? $accessLevel['access_level_name'] : '',
                    'access_type' => (isset($accessLevel['access_type'])) ? $accessLevel['access_type'] : 'GENERAL',
                    'statusEmployee' => (array_key_exists('statusEmployee', $item)) ? $item['statusEmployee'] : '3',
                    'base64' => array_key_exists('base64', $item) ? $item['base64'] : null,
                    'leave_date' => array_key_exists('leaveDate', $item) ? $item['leaveDate'] : null,
                ];
            }
        }
        Log::info('Data transform on transformZkbio ' . now(), [
            'data' => $data,
        ]);
        return $data;
    }

    /**
     * @param array $form
     * @param Applications $application
     * @param  string $accessType
     * @return array
     */
    protected function transformSyncZkbio(array $form, Applications $application, $accessType, $isSimper = false): array
    {
        // var_dump($form);
        $data = [];
        $accessLevelService = new AccessLevelService();
        $accessLevel = $accessLevelService->getAccessLevel(
            $form['company'],
            $form['departmentName'],
            $accessType,
            $form['identifyNumber'],
            $isSimper
        );
        $data[] = [
            'photoName' => $form['photo'],
            'name' => preg_replace('/[^A-Za-z0-9 !@#$%^&*().]/u', '', strip_tags($form['name'])),
            'credentialNumber' => $form['identifyNumber'],
            'oldCredentialNumber' => (isset($form['oldIdentifyNumber'])) ? $form['oldIdentifyNumber'] : '',
            'strId' => $form['identifyNumber'],
            'credentialType' => ($form['type'] == 'ID Card') ? 0 : 1,
            'gender' => (strtoupper($form['gender']) == 'MALE') ? 'M' : 'F',
            'bornTime' => date("Y-m-d", strtotime($form['birthDate'])),
            'country' => $form['nationality'],
            'department' => $form['departmentName'],
            'occupation' => $form['employeeType'],
            'description' => $form['description'],
            'company' => $form['company'],
            'oldCompany' => (isset($form['oldCompany'])) ? $form['oldCompany'] : '',
            'work_location' => $form['workLocation'],
            'dept_name' => $form['departmentName'],
            'application_id' => $application->id,
            'cardNumber' => (isset($form['cardNumber'])) ? $form['cardNumber'] : '',
            'accStartTime' => (isset($form['accStartTime'])) ? $form['accStartTime'] : '',
            'accEndTime' => (isset($form['accEndTime'])) ? $form['accEndTime'] : '',
            'access_level_code' => (isset($accessLevel['access_level_code'])) ? $accessLevel['access_level_code'] : '',
            'access_level_name' => (isset($accessLevel['access_level_name'])) ? $accessLevel['access_level_name'] : '',
            'access_type' => (isset($accessLevel['access_type'])) ? $accessLevel['access_type'] : 'GENERAL',
            'statusEmployee' => (array_key_exists('statusEmployee', $form)) ? $form['statusEmployee'] : '3',
            'base64' => array_key_exists('base64', $form) ? $form['base64'] : null,
            'leave_date' => array_key_exists('leaveDate', $form) ? $form['leaveDate'] : null,
        ];
        return $data;
    }


    /**
     * @param array $form
     * @param Applications $application
     * @return array
     */
    protected function transformPhoto(array $form, Applications $application, $accessType): array
    {
        $data = [];
        foreach ($form as $item) {
            $data[] = [
                'photoName' => $item['photo'],
                'name' => preg_replace('/[^A-Za-z0-9 !@#$%^&*().]/u', '', strip_tags($item['name'])),
                'credentialType' => ($item['type'] == 'ID Card') ? 0 : 1,
                'credentialNumber' => $item['identifyNumber'],
                'country' => $item['nationality'],
                'department' => $item['departmentName'],
                'company' => $item['company'],
                'dept_name' => $item['departmentName'],
                'work_location' => $item['workLocation'],
                'description' => $item['description'],
                'statusEmployee' => $item['statusEmployee'],
                'base64' => $item['base64'],
                'application_id' => $application->id,
                'access_type' => $accessType
            ];
        }

        return $data;
    }


    public function getAccessLevelSimper()
    {
        return [
            "access_level_code" => "SPR0001",
            "access_level_name" => "SIMPER",
            "access_type" => "SIMPER",
        ];
    }

    protected function getAllAccessLevel($identifyNumber, $accessLevel)
    {
        if ((int) $identifyNumber % 2 == 0 && Str::contains($accessLevel->access_level_name, 'GENAP')) {
            return [
                "access_level_code" => $accessLevel->access_level_code,
                "access_level_name" => $accessLevel->access_level_name,
                "access_type" => $accessLevel->accessType->name,
            ];
        } elseif ((int) $identifyNumber % 2 != 0 && Str::contains($accessLevel->access_level_name, 'GANJIL')) {
            return [
                "access_level_code" => $accessLevel->access_level_code,
                "access_level_name" => $accessLevel->access_level_name,
                "access_type" => $accessLevel->accessType->name,
            ];
        }
    }


}
