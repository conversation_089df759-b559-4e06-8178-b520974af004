<?php

namespace App\Models\Settings;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Settings\OrganizationGroup
 *
 * @property int $id
 * @property string $name
 * @property string|null $description
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|OrganizationGroup newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|OrganizationGroup newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|OrganizationGroup query()
 * @method static \Illuminate\Database\Eloquent\Builder|OrganizationGroup whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrganizationGroup whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrganizationGroup whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrganizationGroup whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrganizationGroup whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrganizationGroup whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrganizationGroup whereUpdatedBy($value)
 * @mixin \Eloquent
 */
class OrganizationGroup extends Model
{
    use HasFactory;

    protected $guarded = [];
}
