<?php

namespace App\Jobs\Service;

use App\Models\Applications;
use App\Models\Settings\AccessLevel;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;
use Modules\ExternalApi\app\Services\AccessLevelService;

class TransformDataMutasiService
{
    /**
     * @param string $service
     * @param array $form
     * @param string $accessType
     * @return array
     */
    public function transform(string $service, array $form, $application, $accessType, $isSimper = false): array
    {
        if ($service == 'Huawei') {
            return $this->transformHuawei($form, $application);
        } elseif ($service == 'Zkbio') {
            return $this->transformZkbio($form, $application, $accessType, $isSimper);
        } elseif ($service == 'ZkbioVehicle') {
            return $this->transformZkbioVehicle($form, $application, $accessType);
        } elseif ($service == 'changePhoto') {
            return $this->transformPhoto($form, $application, $accessType);
        } else {
            return [];
        }
    }

    protected function transformHuawei(array $item, Applications $application): array
    {
        $data = [];
        $data[] = [
            'photoName' => $item['form']['photo'],
            'name' => preg_replace('/[^A-Za-z0-9 !@#$%^&*().]/u', '', strip_tags($item['form']['name'])),
            'credentialNumber' => $item['form']['identifyNumber'],
            'oldCredentialNumber' => (isset($item['form']['oldIdentifyNumber'])) ? $item['form']['oldIdentifyNumber'] : '',
            'strId' => $item['form']['identifyNumber'],
            'credentialType' => ($item['form']['type'] == 'ID Card') ? 0 : 1,
            'gender' => (strtoupper($item['form']['gender']) == 'MALE') ? 0 : 1,
            'bornTime' => date("Y-m-d", strtotime($item['form']['birthDate'])),
            'country' => $item['form']['nationality'],
            'department' => $item['form']['departmentName'],
            'occupation' => $item['form']['employeeType'],
            'description' => $item['form']['description'],
            'company' => $item['form']['company'],
            'base64' => array_key_exists('base64', $item['form']) ? $item['form']['base64'] : null,
            'oldCompany' => (isset($item['form']['oldCompany'])) ? $item['form']['oldCompany'] : '',
            'application_id' => $application->id,
            'statusEmployee' => (array_key_exists('statusEmployee', $item)) ? $item['form']['statusEmployee'] : '3',
        ];

        return $data;
    }

    protected function transformZkbio(array $item, Applications $application, $accessType, $isSimper = false): array
    {
        $data = [];
        $accessLevelService = new AccessLevelService();
        $accessLevel = $accessLevelService->getAccessLevel(
            $item['form']['company'],
            $item['form']['departmentName'],
            $accessType,
            $item['form']['identifyNumber'],
            $isSimper
        );
        $data[] = [
            'photoName' => $item['form']['photo'],
            'name' => preg_replace('/[^A-Za-z0-9 !@#$%^&*().]/u', '', strip_tags($item['form']['name'])),
            'credentialNumber' => $item['form']['identifyNumber'],
            'oldCredentialNumber' => (isset($item['form']['oldIdentifyNumber'])) ? $item['oldIdentifyNumber'] : '',
            'strId' => $item['form']['identifyNumber'],
            'credentialType' => ($item['form']['type'] == 'ID Card') ? 0 : 1,
            'gender' => (strtoupper($item['form']['gender']) == 'MALE') ? 'M' : 'F',
            'bornTime' => date("Y-m-d", strtotime($item['form']['birthDate'])),
            'country' => $item['form']['nationality'],
            'department' => $item['form']['departmentName'],
            'occupation' => $item['form']['employeeType'],
            'description' => $item['form']['description'],
            'company' => $item['form']['company'],
            'oldCompany' => (isset($item['form']['oldCompany'])) ? $item['form']['oldCompany'] : '',
            'work_location' => $item['form']['workLocation'],
            'dept_name' => $item['form']['departmentName'],
            'application_id' => $application->id,
            'cardNumber' => (isset($item['form']['cardNumber'])) ? $item['form']['cardNumber'] : '',
            'accStartTime' => (isset($item['form']['accStartTime'])) ? $item['form']['accStartTime'] : '',
            'accEndTime' => (isset($item['form']['accEndTime'])) ? $item['form']['accEndTime'] : '',
            'access_level_code' => (isset($accessLevel['access_level_code'])) ? $accessLevel['access_level_code'] : '',
            'access_level_name' => (isset($accessLevel['access_level_name'])) ? $accessLevel['access_level_name'] : '',
            'access_type' => (isset($accessLevel['access_type'])) ? $accessLevel['access_type'] : 'GENERAL',
            'statusEmployee' => (array_key_exists('statusEmployee', $item['form'])) ? $item['form']['statusEmployee'] : '3',
            'base64' => array_key_exists('base64', $item['form']) ? $item['form']['base64'] : null,
            'leave_date' => array_key_exists('leaveDate', $item['form']) ? $item['form']['leaveDate'] : null,
        ];
        return $data;
    }
}
