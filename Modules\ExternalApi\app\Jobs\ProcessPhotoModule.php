<?php

namespace Modules\ExternalApi\app\Jobs;

use App\Models\Applications;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Bus\Batchable;
use Illuminate\Support\Collection;
use Illuminate\Queue\InteractsWithQueue;
use App\Services\ProcessDataPersonService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class ProcessPhotoModule implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use Batchable;

    public Collection $row;
    public int $nvrId;
    public $batchId;

     /**
     * Create a new job instance.
     */
    public function __construct($row, $nvrId, $batchId)
    {
        $this->row = $row;
        $this->nvrId = $nvrId;
        $this->batchId = $batchId;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $rows = $this->row;

        $service = new ProcessDataPersonService();
        $service->changePhotoFaceRecord($rows, $this->nvrId, $this->batchId);
    }
}
