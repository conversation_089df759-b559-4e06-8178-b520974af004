<?php

namespace App\Services\Settings;

use App\Models\Settings\OrganizationType;
use App\Traits\ApiResponse;
use Illuminate\Support\Arr;

class OrganizationTypeService
{
    use ApiResponse;

    /**
     * @param $request
     *
     * @return array
     */
    public function index($request): array
    {
        $options = json_decode($request->options, true);
        $pages = isset($options['page']) ? (int)($options['page'] + 1) : 1;
        $row_data = isset($options['rows']) ? (int)$options['rows'] : 20;
        $sorts = isset($options['sortField']) ? (string)$options['sortField'] : "name";
        $order = (!empty($options['sortOrder'])) ? 'desc' : 'asc';
        $search = isset($request->search) ? (string)$request->search : "";
        $filters = $options['filters'] ?? null;

        $offset = ($pages - 1) * $row_data;

        $result = array();
        $query = OrganizationType::when($filters, function ($query, $filters) {
            if (isset($filters['company']['value'])) {
                $query->where('company', 'LIKE', '%' . $filters['company']['value'] . '%');
            }
            if (isset($filters['name']['value'])) {
                $query->where('name', 'LIKE', '%' . $filters['name']['value'] . '%');
            }
        });

        $result["total"] = $query->count();

        $result['data'] = $query
            ->orderBy($sorts, $order)
            ->offset($offset)
            ->limit($row_data)
            ->get();

        $result['form'] = $this->getForm();
        $roles = OrganizationType::select('id', 'name')->get();
        $selectRoles = [];

        foreach ($roles as $role) {
            $selectRoles[] = [
                'value' => $role->id,
                'label' => $role->name,
            ];
        }
        $result['select'] = $selectRoles;
        $result['header'] = $this->frontEndHeader();

        $collect = collect($result);

        return $collect->all();
    }

    /**
     * @return array
     */
    public function getForm(): array
    {
        $form = $this->form('roles');
        $form['id'] = 0;

        return $form;
    }

    /**
     * @param $request
     * @param $type
     *
     * @return array
     */
    public function formData($request, $type): array
    {
        $data = $request->all();

        Arr::forget($data, 'id');
        Arr::forget($data, 'created_at');
        Arr::forget($data, 'updated_at');
        Arr::forget($data, 'status_name');
        Arr::forget($data, 'ACTIONS');

        if ($type == 'store') {
            $data['created_by'] = $request->user()->id;
        }

        foreach ($data as $key => $value) {
            if (strpos($key, '/api/') === 0) {
                unset($data[$key]);
            }
        }

        return $data;
    }

    public function frontEndHeader()
    {
        return [
            [
                "accessorKey" => 'name',
                "header" => 'Name',
            ],
            [
                "accessorKey" => 'description',
                "header" => 'Description',
            ],
            [
                "accessorKey" => 'status',
                "header" => 'Status',
            ],
        ];
    }
}
