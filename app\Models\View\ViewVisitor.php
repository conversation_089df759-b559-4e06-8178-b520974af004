<?php

namespace App\Models\View;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\View\DetailWithoutCompany
 *
 * @method static \Illuminate\Database\Eloquent\Builder|DetailWithoutCompany newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DetailWithoutCompany newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DetailWithoutCompany query()
 * @mixin \Eloquent
 */
class ViewVisitor extends Model
{
    use HasFactory;

    protected $connection = 'sqlsrv';
    protected $table = '';

    public static function getVisitorReport($startDate, $endDate)
    {
        $results = DB::connection('sqlsrv')
            ->select("EXEC sp_ReportVisitorPivot ?, ?", [$startDate, $endDate]);
        
        return collect($results);
    }

    /**
     * Execute stored procedure and return as array
     *
     * @param string $startDate
     * @param string $endDate
     * @return array
     */
    public static function getVisitorReportArray($startDate, $endDate)
    {
        return self::getVisitorReport($startDate, $endDate)->toArray();
    }

    /**
     * Execute stored procedure with current month date range
     *
     * @return \Illuminate\Support\Collection
     */
    public static function getCurrentMonthVisitorReport()
    {
        $startDate = now()->startOfMonth()->format('Y-m-d');
        $endDate = now()->endOfMonth()->format('Y-m-d');
        
        return self::getVisitorReport($startDate, $endDate);
    }
}
