<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Services\Dashboard\SummaryCalculation;
use Illuminate\Http\Request;

class TotalPersonController extends Controller
{
    public function index(Request $request)
    {
        $service = new SummaryCalculation();

        return response()->json($service->calculateTotalPersonHuawei());
    }

    public function getTotalPersonNvr(Request $request)
    {
        $service = new SummaryCalculation();

        return response()->json($service->getTotalPersonNvr());
    }

    public function getPersonTotalByCompany()
    {
        $service = new SummaryCalculation();

        return response()->json($service->getPersonTotalByCompany());
    }
}
