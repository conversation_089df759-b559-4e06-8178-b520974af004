<?php

namespace App\Helpers;


class GenerateInitials {

    public static function generateInitials($inputString, $length)
    {
        $cleanedString = preg_replace('/[^a-zA-Z0-9]/', ' ', $inputString);

        $words = explode(' ', $cleanedString);

        $initials = '';

        foreach ($words as $word) {
            if (!empty($word)) {
                $initials .= strtoupper(substr($word, 0, $length));
            }
        }

        // return $initials;
        return str_replace("-", "", $initials);
    }

}