<?php

namespace App\Jobs\Upload;

use App\Models\Settings\Nvr;
use App\Services\Upload\GetPhotoCherryService;
use App\Services\Upload\UploadFaceRecordService;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use App\Traits\NvrCookieHelper;
use App\Models\Settings\Persons;
use App\Models\Settings\Setting;
use App\Models\Settings\FaceList;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use App\Models\Settings\Organization;
use Intervention\Image\Facades\Image;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Storage;
use App\Models\Settings\Log_transaction;
use Illuminate\Queue\InteractsWithQueue;
use App\Services\Upload\BatchUploadService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class ProcessUploadFaceRecordUpdate implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use Batchable;
    use NvrCookieHelper;

    public $row;
    public $nvr_id;
    public int $user_id;

    /**
     * Create a new job instance.
     */
    public function __construct($row, $nvr_id, $user_id)
    {
        $this->row = $row;
        $this->nvr_id = $nvr_id;
        $this->user_id = $user_id;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $rows = $this->row;
        $user_id = $this->user_id;
        $service = new BatchUploadService();

        Log::info('start upload face record ! Time :' . date("Y-m-d H:i:s"));
        $photoService = new GetPhotoCherryService();
        $faceRecordService = new UploadFaceRecordService();
        foreach ($rows as $employee) {


            $pid = $employee['strId'];
            $org = Organization::where('name', $employee['company'])->first();
            $ftp = Storage::disk('ftp')->get('photo/' . $employee['photoName']);
            $ftpHr = Storage::disk('sftp-hr')->get($org->alias . '/' . $employee['photoName']);
            $size_validasi = Setting::getSetting('FilterSizeImage');
            $base64 = null;
            
            if ($org->data_source == 'cherry') {
                $base64 = $photoService->getUserPhoto($pid);

                if (!$base64) {
                    $faceRecordService->errorNotFound($employee);
                    continue;
                }
                $employee['log_type'] = 'PERSON';
            }
            else if($org->data_source == 'contractor'){
                $ftpEcon = Storage::disk('sftp-econ')->get($employee['photoName']);

                
                if($ftpEcon){
                    $ftp_size = Storage::disk('sftp-econ')->size($employee['photoName']);
                    $size = intval($ftp_size / 1024);

                    if($size < 100){
                        $ftpImage = Image::make($ftpEcon);
                    }else{
                        $ftpImage = Image::make($ftpEcon)->resize(null, 500, function ($constraint) {
                            $constraint->aspectRatio();
                        });
                    }

                    $base64 = preg_replace('#^data:image/\w+;base64,#i', '', $ftpImage->encode('data-url'));
                }

                if (!isset($employee['log_type'])) {
                    $employee['log_type'] = 'CONTRACTOR';
                }

            }
            elseif ($ftpHr) {
                $ftp_size = Storage::disk('sftp-hr')->size($org->alias . '/' . $employee['photoName']);
                $size = intval($ftp_size / 1024);

                if ($size < $size_validasi) {
                    $ftpImage = Image::make($ftpHr);
                } else {
                    $ftpImage = Image::make($ftpHr)->resize(null, 500, function ($constraint) {
                        $constraint->aspectRatio();
                    });
                }
                $employee['log_type'] = 'PERSON';

                $base64 = preg_replace('#^data:image/\w+;base64,#i', '', $ftpImage->encode('data-url'));

            } else {
                $ftp_size = Storage::disk('ftp')->size('photo/' . $employee['photoName']);
                $size = intval($ftp_size / 1024);

                if ($size < $size_validasi) {
                    $ftpImage = Image::make($ftp);
                } else {
                    $ftpImage = Image::make($ftp)->resize(null, 500, function ($constraint) {
                        $constraint->aspectRatio();
                    });
                }
                $employee['log_type'] = 'PERSON';
                $base64 = preg_replace('#^data:image/\w+;base64,#i', '', $ftpImage->encode('data-url'));

            }

            if ($base64) {
                // $ftpImage = Image::make($ftp)->resize(null, 500, function ($constraint) {
                //     $constraint->aspectRatio();
                // });

                // $base64 = preg_replace('#^data:image/\w+;base64,#i', '', $ftpImage->encode('data-url'));
                $company = $employee['company'];

                $paramSearch = [
                    'credential_number' => strval($employee['credentialNumber'])
                ];

                $paramPerson = [
                    'credential_type' => $employee['credentialType'],
                    'name' => preg_replace('/-+/', '', $employee['name']),
                    'company' => $employee['company'],
                    'department' => $employee['department'],
                    'photo_name' => $employee['photoName'],
                    'status' => 1,
                ];

                Persons::updateOrCreate($paramSearch, $paramPerson);


                $faceList = FaceList::where('nvr_id', $this->nvr_id)
                    ->whereHas('organizationFaceList', function ($query) use ($company) {
                        $query->whereHas('organization', function ($qr) use ($company) {
                            return $qr->where('name', 'LIKE', '%' . $company . '%');
                        });
                    })
                    ->get();

                $fullName = preg_replace('/[^A-Za-z0-9\-\s]/', '', $employee['name']);

                if ($faceList) {
                    foreach ($faceList as $index => $item) {
                        $nvr = Nvr::find($item->nvr_id);
                        $service->validateNvrCookie($nvr->id);
                        $nvrName = str_replace(' ', '_', $nvr->name);
                        $cookieNvr = $this->getConfigNvr($nvrName, 'cookie ' . $nvrName);

                        $data_upload = [
                            'index' => $index,
                            'name' => preg_replace('/-+/', '', $fullName),
                            'credentialNumber' => $employee['credentialNumber'],
                            'credentialType' => $employee['credentialType'],
                            'gender' => $employee['gender'],
                            'bornTime' => $employee['bornTime'],
                            'country' => $employee['country'],
                            'occupation' => $employee['occupation'],
                            'description' => $employee['description'],
                            'strId' => $employee['strId'],
                            'pictures' => [$base64]
                        ];

                        $data_payload = [
                            'index' => $index,
                            'name' => preg_replace('/-+/', '', $fullName),
                            'credentialNumber' => $employee['credentialNumber'],
                            'credentialType' => $employee['credentialType'],
                            'gender' => $employee['gender'],
                            'bornTime' => $employee['bornTime'],
                            'country' => $employee['country'],
                            'occupation' => $employee['occupation'],
                            'description' => $employee['description'],
                            'strId' => $employee['strId']
                        ];

                        $upload = Http::withoutVerifying()
                            ->withOptions(['verify' => false])
                            ->withHeaders([
                                'Accept' => 'application/json',
                                'Cookie' => 'JSESSIONID=' . $cookieNvr,
                            ])
                            ->post($nvr->ip . '/sdk_service/rest/facerepositories/' . $item->face_list_id . '/peoples', [
                                'peopleList' => [
                                    $data_upload
                                ]
                            ]);

                        $response_data = json_decode($upload->body(), true);
                        $jsonString = json_encode($data_payload);

                        Log_transaction::create([
                            'credential_number' => $employee['credentialNumber'],
                            'credential_type' => $employee['credentialType'],
                            'name' => $employee['name'],
                            'company' => $employee['company'],
                            'type' => 'HUAWEI',                        // HUAWEI & ZKBIO
                            'nvr_id' => $item->nvr_id,
                            'face_list_id' => $item->face_list_id,
                            'access_level_code' => null,
                            'status' => 1,
                            'application_id' => null,
                            'service_code' => $response_data['resultCode'],    // code = code service
                            'service_payload' => '',                              // payload = json param
                            'service_message' => $response_data['resultMsg'],     // reponse = json messsgae
                            'log_type' => $employee['log_type'],
                            'method' => 'POST',
                            'created_by' => $user_id,
                        ]);

                        Log::info('resultx from upload face record', [
                            'body' => $upload->body(),
                            'json' => $upload->json(),
                        ]);
                    }
                } else {
                    $faceRecordService->errorNotFound($employee, 'FL404', 'FaceList Not Found!', $user_id);
                    Log::info('FaceList Not Found!');
                }
            } else {
                $faceRecordService->errorNotFound(employee: $employee, user_id: $user_id);
            }
        }

        Log::info('Upload Done ! Time : ' . date("Y-m-d H:i:s"));
    }
}
