<?php

namespace Modules\ExternalApi\app\Http\Controllers;

use Throwable;
use App\Models\Applications;
use App\Models\Settings\Nvr;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use App\Models\Settings\Log_transaction;
use Modules\ExternalApi\app\Services\TransformDataApiService;
use Modules\ExternalApi\app\Services\ProcessDeletePersonService;
use Modules\ExternalApi\app\Http\Requests\StoreExternalApiRequest;
use Modules\ExternalApi\app\Services\ProcessMutationPersonService;
use Modules\ExternalApi\app\Http\Requests\DeleteExternalApiRequest;
use Modules\ExternalApi\app\Http\Requests\UpdateExternalApiRequest;
use Modules\ExternalApi\app\Services\ProcessDataExternalApiService;
use Modules\ExternalApi\app\Services\ProcessPhotoExternalApiService;
use Modules\ExternalApi\app\Services\ProcessUpdateExternalApiService;

class ExternalApiController extends Controller
{
    /**
     * @param StoreExternalApiRequest $request
     * @return \Illuminate\Http\JsonResponse
     * @throws Throwable
     */
    public function store(StoreExternalApiRequest $request): JsonResponse
    {
        // return response()->json($request->all());
        $application = Applications::where('token', $request->bearerToken())
            ->where('status', '1')
            ->first();

        $transform = new TransformDataApiService();
        $accLevelIds = $application->access_level_code;

        $service = new ProcessDataExternalApiService();
        if ($request->service == 'Huawei') {
            $data = $transform->transform('Huawei', $request->form, $application, $request->accessType);

            $nvr = Nvr::where("status", "=", 1)->get();
            $batch = $service->processQueueHuawei($data, $nvr, $application);
            return response()->json([
                'message' => 'Data Persons is currently being processed in the queue',
                'status' => 'processing',
                'worker' => [
                    [
                        'service' => 'huawei',
                        'device' => $batch
                    ]
                ]
            ]);
        } elseif ($request->service == 'Zkbio') {
            $data = $transform->transform('Zkbio', $request->form, $application, $request->accessType);
            // return response()->json($data);
            $batch = $service->processQueueZkbio(data: $data, application: $application);
            return response()->json([
                'message' => 'Data Face Records is currently being processed in the queue',
                'status' => 'processing',
                'worker' => [
                    [
                        'service' => 'zkbio',
                        'device' => $batch
                    ]
                ]
            ]);
        } elseif ($request->service == 'ALL') {
            $nvr = Nvr::where("status", "=", 1)->get();
            $data = $transform->transform('Huawei', $request->form, $application, $request->accessType);
            $dataZkt = $transform->transform('Zkbio', $request->form, $application, $request->accessType);
            // return response()->json($dataZkt);
            $batch1 = $service->processQueueHuawei($data, $nvr, $application);
            $batch2 = $service->processQueueZkbio(data: $dataZkt, application: $application);
            return response()->json([
                'message' => 'Data Persons is currently being processed in the queue',
                'status' => 'processing',
                'worker' => [
                    [
                        'service' => 'huawei',
                        'device' => $batch1
                    ],
                    [
                        'service' => 'zkbio',
                        'device' => $batch2
                    ]
                ]
            ]);
        } else {
            return $this->error('Service Not Found!');
        }
    }
    /**
     * update resource
     *
     * @param UpdateExternalApiRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(UpdateExternalApiRequest $request): JsonResponse
    {

        $application = Applications::where('token', $request->bearerToken())
            ->where('status', '1')
            ->first();

        $transform = new TransformDataApiService();
        $accLevelIds = $application->access_level_code;

        $service = new ProcessUpdateExternalApiService();
        if ($request->service == 'Huawei') {
            $data = $transform->transform('Huawei', $request->form, $application, $request->accessType);

            $nvr = Nvr::where("status", "=", 1)->get();
            $batch = $service->processQueueHuawei($data, $application);
            return response()->json([
                'message' => 'Data Persons is currently being processed in the queue',
                'status' => 'processing',
                'batch_id' => [
                    'huawei' => $batch
                ]
            ]);
        } elseif ($request->service == 'Zkbio') {
            $data = $transform->transform('Zkbio', $request->form, $application, $request->accessType);
            $batch = $service->processQueueZkbio(data: $data, application: $application);
            return response()->json([
                'message' => 'Data Face Records is currently being processed in the queue',
                'status' => 'processing',
                'batch_id' => [
                    'zkbio' => $batch
                ]
            ]);
        } elseif ($request->service == 'ALL') {
            $nvr = Nvr::where("status", "=", 1)->get();
            $dataHuawei = $transform->transform('Huawei', $request->form, $application, $request->accessType);
            $dataZkt = $transform->transform('Zkbio', $request->form, $application, $request->accessType);
            $batch1 = $service->processQueueHuawei($dataHuawei, $application);
            $batch2 = $service->processQueueZkbio(data: $dataZkt, application: $application);
            return response()->json([
                'message' => 'Data Persons is currently being processed in the queue',
                'status' => 'processing',
                'worker' => [
                    [
                        'service' => 'huawei',
                        'device' => $batch1
                    ],
                    [
                        'service' => 'zkbio',
                        'device' => $batch2
                    ]
                ]
            ]);
        } else {
            return $this->error('Service Not Found!');
        }
    }

    public function show(Request $request)
    {
        $batchId = $request->batchId;
        Log::info('request', [
            'request all' => $request->all(),
            'batchId' => $request->batchId
        ]);
        $params = [];
        $application = Applications::where('token', $request->bearerToken())
            ->where('status', '1')
            ->first();

        foreach ($batchId as $id) {
            $batch = Bus::findBatch($id);
            if (isset($batch)) {
                if ($batch->finished()) {
                    $logTransaction = Log_transaction::where('batch_id', $batch->id)
                        ->select(
                            "credential_number",
                            "name",
                            "company",
                            "access_level_code",
                            "access_level_name",
                            "service_code",
                            "service_message",
                            "batch_id"
                        )
                        // ->where("service_code", "=", "0")
                        ->whereApplicationId($application->id)
                        ->whereIn("type", ["HUAWEI", "ZKBIO"])
                        ->get();

                    $params[] = [
                        'message' => 'success',
                        'batch_id' => $batch->id,
                        'status' => 'completed',
                        'name' => $batch->name,
                        "data" => $logTransaction
                    ];
                } else {
                    $logTransaction = Log_transaction::where('batch_id', $batch->id)
                        ->select(
                            "credential_number",
                            "name",
                            "company",
                            "access_level_code",
                            "access_level_name",
                            "service_code",
                            "service_message",
                            "batch_id"
                        )
                        // ->where("service_code", "<>", "0")
                        ->whereApplicationId($application->id)
                        ->whereIn("type", ["HUAWEI", "ZKBIO"])
                        ->get();
                    $params[] = [
                        'message' => 'success',
                        'batch_id' => $batch->id,
                        'name' => $batch->name,
                        'status' => 'error',
                        "data" => $logTransaction
                    ];
                }
            } else {
                $params[] = [
                    'message' => 'Batch ID Not Found !',
                    'batch_id' => $request->batchId,
                    'status' => 'notfound',
                    'code' => 'BATCH404',
                    'name' => '',
                    "data" => []
                ];
            }
        }

        return response()->json($params);
    }

    /**
     * delete person
     *
     * @param DeleteExternalApiRequest $request
     * @return JsonResponse
     */
    public function destroy(DeleteExternalApiRequest $request)
    {
        $application = Applications::where('token', $request->bearerToken())
            ->where('status', '1')
            ->first();

        $transform = new TransformDataApiService();
        $accLevelIds = $application->access_level_code;

        $service = new ProcessDeletePersonService();

        if ($request->service == 'Huawei') {
            $data = $transform->transform('Huawei', $request->form, $application, $request->accessType);
            $batch = $service->processQueueHuawei($data, $application);
            return response()->json([
                'message' => 'Data Persons is currently being processed in the queue',
                'status' => 'processing',
                'worker' => [
                    [
                        'service' => 'huawei',
                        'device' => $batch
                    ],
                ]
            ]);
        } elseif ($request->service == 'Zkbio') {
            $data = $transform->transform('Zkbio', $request->form, $application, $request->accessType);
            $batch = $service->processQueueZkbio($data, $application);
            return response()->json([
                'message' => 'Data Face Records is currently being processed in the queue',
                'status' => 'processing',
                'worker' => [
                    [
                        'service' => 'zkbio',
                        'device' => $batch
                    ]
                ]
            ]);
        } elseif ($request->service == 'ALL') {
            $dataHuawei = $transform->transform('Huawei', $request->form, $application, $request->accessType);
            $dataZkt = $transform->transform('Zkbio', $request->form, $application, $request->accessType);
            $batch1 = $service->processQueueHuawei($dataHuawei, $application);
            $batch2 = $service->processQueueZkbio($dataZkt, $application);
            return response()->json([
                'message' => 'Data Persons is currently being processed in the queue',
                'status' => 'processing',
                'worker' => [
                    [
                        'service' => 'huawei',
                        'device' => $batch1
                    ],
                    [
                        'service' => 'zkbio',
                        'device' => $batch2
                    ]
                ]
            ]);
        } else {
            return $this->error('Service Not Found!');
        }
    }


    /**
     * delete person
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function mutation(Request $request)
    {
        $application = Applications::where('token', $request->bearerToken())
            ->where('status', '1')
            ->first();

        $transform = new TransformDataApiService();
        $accLevelIds = $application->access_level_code;

        $service = new ProcessMutationPersonService();

        if ($request->service == 'Huawei') {
            $data = $transform->transform('Huawei', $request->form, $application, $request->accessType);
            $batch = $service->processQueueHuawei($data, $application);
            return response()->json([
                'message' => 'Data Persons is currently being processed in the queue',
                'status' => 'processing',
                'worker' => [
                    [
                        'service' => 'huawei',
                        'device' => $batch
                    ],
                ]
            ]);
        } elseif ($request->service == 'Zkbio') {

            $data = $transform->transform('Zkbio', $request->form, $application, $request->accessType);
            $batch = $service->processQueueZkbio($data, $application);
            return response()->json([
                'message' => 'Data Face Records is currently being processed in the queue',
                'status' => 'processing',
                'worker' => [
                    [
                        'service' => 'zkbio',
                        'device' => $batch
                    ]
                ]
            ]);
        } elseif ($request->service == 'ALL') {
            $dataHuawei = $transform->transform('Huawei', $request->form, $application, $request->accessType);
            $dataZkt = $transform->transform('Zkbio', $request->form, $application, $request->accessType);
            $batch1 = $service->processQueueHuawei($dataHuawei, $application);
            $batch2 = $service->processQueueZkbio($dataZkt, $application);
            return response()->json([
                'message' => 'Data Persons is currently being processed in the queue',
                'status' => 'processing',
                'worker' => [
                    [
                        'service' => 'huawei',
                        'device' => $batch1
                    ],
                    [
                        'service' => 'zkbio',
                        'device' => $batch2
                    ]
                ]
            ]);
        } else {
            return $this->error('Service Not Found!');
        }
    }

    /**
     * delete person
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function changePhoto(Request $request)
    {
        $application = Applications::where('token', $request->bearerToken())
            ->where('status', '1')
            ->first();

        $transform = new TransformDataApiService();
        $accLevelIds = $application->access_level_code;

        $service = new ProcessPhotoExternalApiService();

        if ($request->service == 'Huawei') {
            $data = $transform->transform('changePhoto', $request->form, $application, $request->accessType);
            $batch = $service->processQueueHuawei($data, $application);
            return response()->json([
                'message' => 'Data Persons is currently being processed in the queue',
                'status' => 'processing',
                'worker' => [
                    [
                        'service' => 'huawei',
                        'device' => $batch
                    ],
                ]
            ]);
        } elseif ($request->service == 'Zkbio') {

            $data = $transform->transform('changePhoto', $request->form, $application, $request->accessType);
            $batch = $service->processQueueZkbio($data, $application);
            return response()->json([
                'message' => 'Data Face Records is currently being processed in the queue',
                'status' => 'processing',
                'worker' => [
                    [
                        'service' => 'zkbio',
                        'device' => $batch
                    ]
                ]
            ]);
        } elseif ($request->service == 'ALL') {
            $data = $transform->transform('changePhoto', $request->form, $application, $request->accessType);
            $batch1 = $service->processQueueHuawei($data, $application);
            $batch2 = $service->processQueueZkbio($data, $application);
            return response()->json([
                'message' => 'Data Persons is currently being processed in the queue',
                'status' => 'processing',
                'worker' => [
                    [
                        'service' => 'huawei',
                        'device' => $batch1
                    ],
                    [
                        'service' => 'zkbio',
                        'device' => $batch2
                    ]
                ]
            ]);
        } else {
            return $this->error('Service Not Found!');
        }
    }
}
