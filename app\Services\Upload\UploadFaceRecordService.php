<?php

namespace App\Services\Upload;

use App\Models\JobBatch;
use App\Models\Settings\Log_transaction;

class UploadFaceRecordService
{
    public function index($request)
    {
        $options = json_decode($request->options, true);
        $pages = isset($options['page']) ? (int) ($options['page'] + 1) : 1;
        $row_data = isset($options['rows']) ? (int) $options['rows'] : 10;
        $sorts = isset($options['sortField']) ? (string) $options['sortField'] : "created_at";
        $order = (!empty($options['sortOrder'])) ? 'desc' : 'desc';
        $search = isset($request->search) ? (string) $request->search : "";
        $filters = $options['filters'] ?? null;

        $offset = ($pages - 1) * $row_data;

        $result = array();
        $query = JobBatch::where('name', 'LIKE', '%upload face record%');

        $result["total"] = $query->count();

        $result['data'] = $query
            ->orderBy($sorts, $order)
            ->offset($offset)
            ->limit($row_data)
            ->get();

        $result['header'] = $this->frontEndHeader();

        $collect = collect($result);

        return $collect->all();
    }

    public function frontEndHeader()
    {
        return [
            [
                "accessorKey" => 'name',
                "header" => 'Process Name',
            ],
            [
                "accessorKey" => 'percentage',
                "header" => 'Percentage',
            ],
            [
                "accessorKey" => 'total_jobs',
                "header" => 'Total Jobs',
            ],
            [
                "accessorKey" => 'pending_jobs',
                "header" => 'Pending Jobs',
            ],
            [
                "accessorKey" => 'failed_jobs',
                "header" => 'Failed Jobs',
            ],
            [
                "accessorKey" => 'created_at',
                "header" => 'Created At',
            ],
            [
                "accessorKey" => 'finished_at',
                "header" => 'Finish At',
            ],
        ];
    }


    public function errorNotFound($employee, $service_code = "PH404", $service_message = "Photo not found!", $user_id)
    {
        Log_transaction::create([
            'credential_number' => $employee['credentialNumber'],
            'credential_type' => $employee['credentialType'],
            'name' => $employee['name'],
            'company' => $employee['company'],
            'type' => 'HUAWEI',                        // HUAWEI & ZKBIO
            'nvr_id' => null,
            'face_list_id' => null,
            'access_level_code' => null,
            'status' => 1,
            'application_id' => null,
            'service_code' => $service_code,                         // code = code service
            'service_payload' => '',                              // payload = json param
            'service_message' => $service_message,              // reponse = json messsgae
            'log_type' => 'PERSON',
            'method' => 'POST',
            'created_by' => $user_id,
        ]);
    }
}
