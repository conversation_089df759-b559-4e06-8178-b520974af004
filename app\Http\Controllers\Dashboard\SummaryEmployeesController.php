<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class SummaryEmployeesController extends Controller
{
    public function index(Request $request)
    {
        $service = new \App\Services\Dashboard\SummaryEmployeesService();
        $DataCherry = $service->getSummaryMasterCherry();
        $DataHris = $service->getSummaryMasterHris();
        $DataZkbioTenant = $service->getSummaryZkbioTenant();
        $DataZkbioImip = $service->getSummaryZkbioImip();
        $combinedDataEmployee = collect($DataCherry)->merge($DataHris)->all();
        $combinedDataZkbio = collect($DataZkbioImip)->merge($DataZkbioTenant)->all();
        return response()->json([
            "data" => [
                "employees" => $combinedDataEmployee,
                "zkbio" => $combinedDataZkbio,
            ],
        ]);
    }
}