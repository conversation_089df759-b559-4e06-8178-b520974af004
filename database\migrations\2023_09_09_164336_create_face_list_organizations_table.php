<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('face_lists', function (Blueprint $table) {
            $table->removeColumn('organization_id');
        });

        Schema::table('access_levels', function (Blueprint $table) {
            $table->removeColumn('organization_id');
        });
        Schema::create('face_list_organizations', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('face_list_id');
            $table->unsignedBigInteger('organization_id');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('face_list_organizations');
    }
};
