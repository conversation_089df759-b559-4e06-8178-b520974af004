<?php

namespace Modules\Vehicle\app\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreVehicleRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'form' => 'required',
            'form.*.name' => 'required',
            'form.*.identifyNumber' => 'required',
            'form.*.type' => 'required',
            'form.*.description' => 'required',
            'form.*.company' => 'required',
            'form.*.departmentName' => 'required'
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }
}
