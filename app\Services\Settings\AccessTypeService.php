<?php

namespace App\Services\Settings;

use App\Models\Settings\AccessType;
use App\Traits\ApiResponse;
use Illuminate\Support\Arr;

class AccessTypeService
{
    use ApiResponse;

    /**
     * @param $request
     *
     * @return array
     */
    public function index($request): array
    {
        $options = json_decode($request->options, true);
        $pages = isset($options['page']) ? (int)($options['page'] + 1) : 1;
        $row_data = isset($options['rows']) ? (int)$options['rows'] : 20;
        $sorts = isset($options['sortField']) ? (string)$options['sortField'] : "name";
        $order = (!empty($options['sortOrder'])) ? 'desc' : 'asc';
        $search = isset($request->search) ? (string)$request->search : "";
        $filters = $options['filters'] ?? null;

        $offset = ($pages - 1) * $row_data;

        $result = array();
        $query = AccessType::when($filters, function ($query, $filters) {
            if (isset($filters['company']['value'])) {
                $query->where('company', 'LIKE', '%' . $filters['company']['value'] . '%');
            }
            if (isset($filters['name']['value'])) {
                $query->where('name', 'LIKE', '%' . $filters['name']['value'] . '%');
            }
        });

        $result["total"] = $query->count();

        $result['data'] = $query
            ->orderBy($sorts, $order)
            ->offset($offset)
            ->limit($row_data)
            ->get();

        $result['form'] = $this->getForm();
        $roles = AccessType::select('id', 'name')->get();
        $selectRoles = [];

        foreach ($roles as $role) {
            $selectRoles[] = [
                'value' => $role->id,
                'label' => $role->name,
            ];  
        }
        $result['select'] = $selectRoles;
        $result['header'] = $this->frontEndHeader();

        $collect = collect($result);

        return $collect->all();
    }

    /**
     * @return array
     */
    public function getForm(): array
    {
        $form = $this->form('roles');
        $form['id'] = 0;

        return $form;
    }

    /**
     * @param $request
     * @param $type
     *
     * @return array
     */
    public function formData($request, $type): array
    {
        $data = $request->all();

        Arr::forget($data, 'id');
        Arr::forget($data, 'created_at');
        Arr::forget($data, 'updated_at');
        Arr::forget($data, 'status_name');
        Arr::forget($data, 'ACTIONS');

        if ($type == 'store') {
            $data['created_by'] = $request->user()->id;
        }
        $generate = app('App\Helpers\GenerateInitials')->generateInitials($data['name'],2);

        $data['code'] = $generate;
        
        foreach ($data as $key => $value) {
            if (strpos($key, '/api/') === 0) {
                unset($data[$key]);
            }
        }
        return $data;
    }

    

    public function frontEndHeader()
    {
        return [
            [
                "accessorKey" => 'name',
                "header" => 'Access Type Name',
            ],
            [
                "accessorKey" => 'code',
                "header" => 'Access Type Code',
            ],
            [
                "accessorKey" => 'description',
                "header" => 'Description',
            ],
            [
                "accessorKey" => 'status',
                "header" => 'Status',
            ],
        ];
    }
}
