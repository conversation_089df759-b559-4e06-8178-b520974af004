<?php

namespace App\Services\Upload;

use App\Models\JobBatch;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

use App\Models\Settings\Nvr;
use App\Models\Settings\FaceList;
use App\Models\Settings\Log_transaction;
use App\Models\Settings\Organization;
use App\Models\Settings\Setting;


class BatchUploadZkbioService
{

    public function index($request)
    {
        $options = json_decode($request->options, true);
        $pages = isset($request['start']) ? (int)($request['start'] + 1) : 1;
        $row_data = isset($request['size']) ? (int)$request['size'] : 10;
        $sorts = isset($options['sortField']) ? (string)$options['sortField'] : "created_at";
        $order = (!empty($options['sortOrder'])) ? 'desc' : 'desc';
        $search = isset($request->search) ? (string)$request->search : "";
        $filters = $options['filters'] ?? null;

        $offset = ($pages - 1) * $row_data;

        $result = array();
        $query = JobBatch::where('name', 'LIKE', '%upload zkbio%');

        $result["total"] = $query->count();

        $result['data'] = $query
            ->orderBy($sorts, $order)
            ->skip($request['start'])
            ->take($request['size'])
            ->get();

        $result['header'] = $this->frontEndHeader();
        $result['request'] = [
            'offset' => $offset,
            'row_data' => $row_data,
        ];

        $collect = collect($result);

        return $collect->all();
    }

    public function frontEndHeader()
    {
        return [
            [
                "accessorKey" => 'name',
                "header" => 'Process Name',
            ],
            [
                "accessorKey" => 'percentage',
                "header" => 'Percentage',
            ],
            [
                "accessorKey" => 'total_jobs',
                "header" => 'Total Jobs',
            ],
            [
                "accessorKey" => 'pending_jobs',
                "header" => 'Pending Jobs',
            ],
            [
                "accessorKey" => 'failed_jobs',
                "header" => 'Failed Jobs',
            ],
            [
                "accessorKey" => 'created_at',
                "header" => 'Created At',
            ],
            [
                "accessorKey" => 'finished_at',
                "header" => 'Finish At',
            ],
        ];
    }

    /**
     * @param $pid
     * @param $accessToken
     * @return \GuzzleHttp\Promise\PromiseInterface|\Illuminate\Http\Client\Response
     */

    public function getPerson($pid, $accessToken)
    {
        $urlGetPerson = Setting::getSetting('UrlGetPersonZkbio');
        return Http::withoutVerifying()
        ->withOptions(['verify' => false])
        ->withHeaders([
                'Content-Type' => 'application/json',
        ])
        ->get($urlGetPerson . $pid . '?access_token=' . $accessToken);
    }

    public function photoNotFound($accessToken, $params, $biometric, $company, $new_data_level, $accLevelNames, $user_id): void{
        $urlAddPerson = Setting::getSetting('UrlAddPersonZkbio');
        $params_data = array();
        $params_data = $params;
        unset($params_data['personPhoto']);
        $urlAddPerson = Setting::getSetting('UrlAddPersonZkbio');
        $upload                         = Http::withoutVerifying()
            ->withOptions(['verify' => false])
            ->withHeaders([
                'Content-Type' => 'application/json',
        ])
        ->post($urlAddPerson . '?access_token=' . $accessToken, $params_data);
        $code_hasil                     = ($upload->collect()->has('code'))?$upload->collect()['code']:(($upload->collect()->has('ret'))?$upload->collect()['ret']:-1);
        $message                        = ($upload->collect()->has('message'))?$upload->collect()['message']:(($upload->collect()->has('msg'))?$upload->collect()['msg']:-1);

        $log_type = 'PERSON';

        if(isset($params['log_type'])){
            if(!empty($params['log_type'])){
                $log_type = $params['log_type'];
            }
        }

        Log::info('result from save person data zkbio with creadential number '.$params['pin'].' failed. Photo is Empty or Not Found');
        $save_log                       = Log_transaction::create([
            'credential_number'         => $params['pin'],
            'credential_type'           => 0,
            'name'                      => $params['name'],
            'company'                   => strtoupper($company),
            'type'                      => 'ZKBIO',
            'access_level_code'         => $new_data_level,
            'access_level_name'         => $accLevelNames,
            'status'                    => 1,
            'application_id'            => null,
            'service_code'              => 'PH404',
            'service_payload'           => '',
            'service_message'           => 'Photo not found!',
            'nvr_id'                    => NULL,
            'face_list_id'              => NULL,
            'log_type'                  => $log_type,
            'method'                    => 'POST',
            'created_by'                => $user_id,
        ]);
        $save_log                       = Log_transaction::create([
            'credential_number'         => $params['pin'],
            'credential_type'           => 0,
            'name'                      => $params['name'],
            'company'                   => strtoupper($company),
            'type'                      => 'ZKBIO',
            'access_level_code'         => $new_data_level,
            'access_level_name'         => $accLevelNames,
            'status'                    => 1,
            'application_id'            => null,
            'service_payload'           => '',
            'nvr_id'                    => NULL,
            'face_list_id'              => NULL,
            'log_type'                  => $log_type,
            'method'                    => 'POST',
            'created_by'                => $user_id,
            'service_code'              => $code_hasil,
            'service_message'           => $message,
        ]);
    }

    public function addPerson($accessToken, $params, $biometric, $company, $new_data_level, $accLevelNames, $user_id): void
    {
        $urlAddPerson = Setting::getSetting('UrlAddPersonZkbio');
        $upload                         = Http::withoutVerifying()
            ->withOptions(['verify' => false])
            ->withHeaders([
                'Content-Type' => 'application/json',
        ])
        ->post($urlAddPerson . '?access_token=' . $accessToken, $params);

        $log_type = 'PERSON';

        if(isset($params['log_type'])){
            if(!empty($params['log_type'])){
                $log_type = $params['log_type'];
            }
        }

    
        $code_hasil                     = ($upload->collect()->has('code'))?$upload->collect()['code']:(($upload->collect()->has('ret'))?$upload->collect()['ret']:-1);
        $message                        = ($upload->collect()->has('message'))?$upload->collect()['message']:(($upload->collect()->has('msg'))?$upload->collect()['msg']:-1);
    
        if($code_hasil == '-63'){
            $params_data = array();
            $params_data = $params;
            unset($params_data['personPhoto']);
            $urlAddPerson = Setting::getSetting('UrlAddPersonZkbio');
            $upload                         = Http::withoutVerifying()
                ->withOptions(['verify' => false])
                ->withHeaders([
                    'Content-Type' => 'application/json',
            ])
            ->post($urlAddPerson . '?access_token=' . $accessToken, $params_data);
       
            $code_hasil_error               = ($upload->collect()->has('code'))?$upload->collect()['code']:(($upload->collect()->has('ret'))?$upload->collect()['ret']:-1);
            $message_error                  = ($upload->collect()->has('message'))?$upload->collect()['message']:(($upload->collect()->has('msg'))?$upload->collect()['msg']:-1);
       
            $save_log                       = Log_transaction::create([
                'credential_number'         => $params['pin'],
                'credential_type'           => 0,
                'name'                      => $params['name'],
                'company'                   => strtoupper($company),
                'type'                      => 'ZKBIO',
                'access_level_code'         => $new_data_level,
                'access_level_name'         => $accLevelNames,
                'status'                    => 1,
                'application_id'            => null,
                'service_code'              => $code_hasil_error,
                'service_payload'           => '',
                'service_message'           => $message_error.' | '.$message,
                'nvr_id'                    => NULL,
                'face_list_id'              => NULL,
                'log_type'                  => $log_type,
                'method'                    => 'POST',
                'created_by'                => $user_id,
            ]);
        
        }else{
             /* Insert Transaksi To Db */
            $save_log                       = Log_transaction::create([
                'credential_number'         => $params['pin'],
                'credential_type'           => 0,
                'name'                      => $params['name'],
                'company'                   => strtoupper($company),
                'type'                      => 'ZKBIO',
                'access_level_code'         => $new_data_level,
                'access_level_name'         => $accLevelNames,
                'status'                    => 1,
                'application_id'            => null,
                'service_code'              => $code_hasil,
                'service_payload'           => '',
                'service_message'           => $message,
                'nvr_id'                    => NULL,
                'face_list_id'              => NULL,
                'log_type'                  => $log_type,
                'method'                    => 'POST',
                'created_by'                => $user_id,
            ]);
        }

        Log::info('result from save person data zkbio', [
            'json' => $upload->json()
        ]);

        if($code_hasil != 0){
            if($biometric != ''){
                $params['personPhoto']  = $biometric;
            }
            $upload_again               = Http::withoutVerifying()
                ->withOptions(['verify' => false])
                ->withHeaders([
                    'Content-Type' => 'application/json',
            ])
            ->post($urlAddPerson . '?access_token=' . $accessToken, $params);
        }

    }

    public function getAccessToken()
    {
        $accessToken                    = Setting::getSetting('AccessTokenZkbio');
        return $accessToken;
    }
}
