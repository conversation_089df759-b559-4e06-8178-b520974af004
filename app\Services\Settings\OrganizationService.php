<?php

namespace App\Services\Settings;

use App\Models\Settings\Organization;
use App\Models\Settings\OrganizationGroup;
use App\Models\Settings\OrganizationType;
use App\Traits\ApiResponse;

class OrganizationService
{
    use ApiResponse;

    /**
     * @param $request
     *
     * @return array
     */
    public function index($request): array
    {
        $options = json_decode($request->options, true);
        $pages = isset($options['page']) ? (int) ($options['page'] + 1) : 1;
        $row_data = isset($options['rows']) ? (int) $options['rows'] : 20;
        $sorts = isset($options['sortField']) ? (string) $options['sortField'] : "alias";
        $order = (!empty($options['sortOrder'])) ? 'desc' : 'asc';
        $search = isset($request->search) ? (string) $request->search : "";
        $filters = $options['filters'] ?? null;

        $offset = ($pages - 1) * $row_data;

        $result = array();
        $query = Organization::when($filters, function ($query, $filters) {
            if (isset($filters['company']['value'])) {
                $query->where('company', 'LIKE', '%' . $filters['company']['value'] . '%');
            }
            if (isset($filters['name']['value'])) {
                $query->where('name', 'LIKE', '%' . $filters['name']['value'] . '%');
            }
        });

        $result["total"] = $query->count();

        $result['data'] = $query
            ->orderBy($sorts, $order)
            ->get()
            ->map(function ($item) {
                $item->value = (string) $item->id;
                return $item;
            });

        $result['form'] = $this->getForm();
        $roles = Organization::with('organizationTypeList')
        ->select('id', 'name', 'alias', 'data_source', 'organization_type_id')
        ->get();

        $selectRoles = [];

        foreach ($roles as $role) {
            $selectRoles[] = [
                'value' => $role->id,
                'label' => $role->name . ' - ' . $role->alias . ' - ' . $role->organization_type,
            ];
        }
        
        $result['select'] = $selectRoles;
        $result['default'] = [
            [
                'id' => null,
                'name' => null,
                'alias' => null,
                'organization_type.name' => null,
                'status' => 1
            ]
        ];
        $result['colHeaders'] = ['ID', 'Name', 'Alias', 'Organization Type', 'Group', 'Status'];
        $result['columns'] = $this->frontendColumns();

        $collect = collect($result);

        return $collect->all();
    }

    public function frontendColumns()
    {
        return [
            [
                'data' => 'id',
                'width' => 90,
                'wordWrap' => false
            ],
            [
                'data' => 'name',
                'width' => 150,
                'wordWrap' => false
            ],
            [
                'data' => 'alias',
                'width' => 150,
                'wordWrap' => false
            ],
            [
                'data' => 'organization_type',
                'type' => 'dropdown',
                'source' => OrganizationType::all()->pluck('name'),
                'width' => 100,
                'wordWrap' => false
            ],
            [
                'data' => 'group_name',
                'type' => 'dropdown',
                'source' => OrganizationGroup::all()->pluck('name'),
                'width' => 100,
                'wordWrap' => false
            ],
            [
                'data' => 'status',
                'width' => 80,
                'type' => 'checkbox',
                'checkedTemplate' => '1',
                'uncheckedTemplate' => '0'
            ]
        ];
    }

    /**
     * @return array
     */
    public function getForm(): array
    {
        $form = $this->form('roles');
        $form['id'] = 0;

        return $form;
    }

    public function create($data)
    {
        return Organization::create($data);
    }

    public function update($id, $data)
    {
        return Organization::where('id', $id)->update($data);
    }

    /**
     * @param $request
     * @param $type
     *
     * @return array
     */
    public function formData($value): array
    {
        $group = (isset($value['group_name'])) ? $value['group_name'] : 'NON GROUP';
        $data = [
            'alias' => $value['alias'],
            'name' => $value['name'],
            'status' => $value['status'],
            'organization_type_id' => OrganizationType::where('name', $value['organization_type'])->first()->id,
            'group_id' => OrganizationGroup::where('name', $group)->first()->id,
            'created_by' => auth()->user()->id,
        ];
        return $data;
    }
}
