<?php

namespace Modules\Vehicle\app\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Modules\Vehicle\Database\factories\VehicleDetailFactory;

class VehicleDetail extends Model
{
    use HasFactory;

    protected $connection = 'sqlsrvsimper';

    protected $table = 'sft_vehicle'; 

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [];


    public function perusahaan()
    {
        return $this->belongsTo(Perusahaan::class, 'vhc_perusahaan', 'id_perusahaan')
            ->whereRaw('ISNUMERIC(id_perusahaan) = 1'); // <PERSON><PERSON> nilai numerik yang direlasikan
    }

    public function merk()
    {
        return $this->belongsTo(Merk::class, 'merk_id', 'id_merk');
    }
    
    protected static function newFactory(): VehicleDetailFactory
    {
        //return VehicleDetailFactory::new();
    }
}
