<?php

namespace Modules\Vehicle\app\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Modules\Vehicle\Database\factories\MerkFactory;

class Merk extends Model
{
    use HasFactory;

    protected $connection = 'sqlsrvsimper';
    protected $table = 'sft_merk';
    protected $primaryKey = 'id_merk'; 

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [];
    
    protected static function newFactory(): MerkFactory
    {
        //return MerkFactory::new();
    }
}
