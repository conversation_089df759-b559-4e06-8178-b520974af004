<?php

namespace Modules\Visitor\App\Http\Controllers;

use App\Models\Applications;
use Illuminate\Http\Request;
use App\Models\Settings\Nvr;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use App\Models\Settings\Log_transaction;
use Modules\Visitor\App\Services\TransformDataApiService;
use Modules\Visitor\App\Http\Requests\StoreVisitorRequest;
use Modules\Visitor\App\Http\Requests\DeleteVisitorRequest;
use Modules\Visitor\App\Services\ProcessDataVisitorApiService;
use Modules\Visitor\App\Services\ProcessDeleteVisitorApiService;

class VisitorController extends Controller
{
    /**
     * Display a listing of the resource.
     */

    /**
     * @param StoreVisitorRequest $request
     * @return \Illuminate\Http\JsonResponse
     * @throws Throwable
     */
    public function store(StoreVisitorRequest $request): \Illuminate\Http\JsonResponse
    {

        $application = Applications::where('token', $request->bearerToken())
            ->where('status', '1')
            ->first();

        // if (!$application) {
        //     return response()->json(['message' => 'Application not found'], 404);
        // }

        $transform = new TransformDataApiService();
        $accLevelIds = $application->access_level_code;
        $service = new ProcessDataVisitorApiService();
        if ($request->service == 'Huawei') {
            $data = $transform->transform('Huawei', $request->form, $application, $request->accessType);
            $nvr = Nvr::where("status", "=", 1)->get();
            $batch = $service->processQueueHuawei($data, $nvr, $application);
            return response()->json([
                'message' => 'Data Persons is currently being processed in the queue',
                'status' => 'processing',
                'worker' => [
                    [
                        'service' => 'huawei',
                        'device' => $batch
                    ]
                ]
            ]);
        } elseif ($request->service == 'Zkbio') {
            $data = $transform->transform('Zkbio', $request->form, $application, $request->accessType);
            $batch = $service->processQueueZkbio(data: $data, application: $application);
            return response()->json([
                'message' => 'Data Face Records is currently being processed in the queue',
                'status' => 'processing',
                'worker' => [
                    [
                        'service' => 'zkbio',
                        'device' => $batch
                    ]
                ]
            ]);
        } elseif ($request->service == 'ALL') {

            $nvr = Nvr::where("status", "=", 1)->get();
            $dataHuawei = $transform->transform('Huawei', $request->form, $application, $request->accessType);
            $dataZkt = $transform->transform('Zkbio', $request->form, $application, $request->accessType);

            $batch1 = $service->processQueueHuawei($dataHuawei, $nvr, $application);
            $batch2 = $service->processQueueZkbio(data: $dataZkt, application: $application);
            return response()->json([
                'message' => 'Data Persons is currently being processed in the queue',
                'status' => 'processing',
                'worker' => [
                    [
                        'service' => 'huawei',
                        'device' => $batch1
                    ],
                    [
                        'service' => 'zkbio',
                        'device' => $batch2
                    ]
                ]
            ]);

            // $data = $transform->transform('Zkbio', $request->form, $application, $request->accessType);
            // $batch = $service->processQueueZkbio(data: $data, application: $application);
            // return response()->json([
            //     'message' => 'Data Face Records is currently being processed in the queue',
            //     'status' => 'processing',
            //     'worker' => [
            //         [
            //             'service' => 'zkbio',
            //             'device' => $batch
            //         ]
            //     ]
            // ]);
        } else {
            return $this->error('Service Not Found!');
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(DeleteVisitorRequest $request): \Illuminate\Http\JsonResponse
    {
        Log::info('request', [
            'request all delete visitor' => $request->all()
        ]);
        $application = Applications::where('token', $request->bearerToken())
            ->where('status', '1')
            ->first();

        $transform = new TransformDataApiService();
        $accLevelIds = $application->access_level_code;
        $nvr = Nvr::where("status", "=", 1)->get();
        $service = new ProcessDeleteVisitorApiService();
        if ($request->service == 'Huawei') {
            $data = $transform->transform('HuaweiDelete', $request->form, $application, $request->accessType);
           
            $batch = $service->processQueueHuawei($data, $nvr, $application);
            return response()->json([
                'message' => 'Deleting Data visitor Records is currently being processed in the queue',
                'status' => 'processing',
                'worker' => [
                    [
                        'service' => 'huawei',
                        'device' => $batch
                    ]
                ]
            ]);
        } elseif ($request->service == 'Zkbio') {

            $data = $transform->transform('ZkbioDelete', $request->form, $application, $request->accessType);
            $batch = $service->processQueueZkbio(data: $data, application: $application);
            return response()->json([
                'message' => 'Data visitor Records is currently being processed in the queue',
                'status' => 'processing',
                'worker' => [
                    [
                        'service' => 'zkbio',
                        'device' => $batch
                    ]
                ]
            ]);
        } elseif ($request->service == 'ALL') {
            $dataZkt = $transform->transform('ZkbioDelete', $request->form, $application, $request->accessType);
            $batch1  = $service->processQueueZkbio(data: $dataZkt, application: $application);

            $dataHuawei = $transform->transform('HuaweiDelete', $request->form, $application, $request->accessType);
            $batch2     = $service->processQueueHuawei($dataHuawei, $nvr, $application);

            return response()->json([
                'message' => 'Data visitor is currently being processed in the queue',
                'status' => 'Processing',
                'worker' => [
                    [
                        'service' => 'zkbio',
                        'device' => $batch1
                    ],
                    [
                        'service' => 'huawei',
                        'device' => $batch2
                    ]
                ]
            ]);
        } else {
            return $this->error('Service Not Found!');
        }
    }

    public function show(Request $request)
    {
        $batchId = $request->batchId;
      
        $params = [];
        $application = Applications::where('token', $request->bearerToken())
            ->where('status', '1')
            ->first();


        foreach ($batchId as $id) {
            $batch = Bus::findBatch($id);
            if (isset($batch)) {
                if ($batch->finished()) {
                    $logTransaction = Log_transaction::where('batch_id', $batch->id)
                        ->select(
                            "credential_number",
                            "name",
                            "company",
                            "access_level_code",
                            "access_level_name",
                            "service_code",
                            "service_message",
                            "batch_id"
                        )
                        // ->where("service_code", "=", "0")
                        ->whereApplicationId($application->id)
                        ->whereIn("type", ["HUAWEI", "ZKBIO"])
                        ->get();

                    $params[] = [
                        'message' => 'success',
                        'batch_id' => $batch->id,
                        'status' => 'completed',
                        'name' => $batch->name,
                        "data" => $logTransaction
                    ];
                } else {
                    $logTransaction = Log_transaction::where('batch_id', $batch->id)
                        ->select(
                            "credential_number",
                            "name",
                            "company",
                            "access_level_code",
                            "access_level_name",
                            "service_code",
                            "service_message",
                            "batch_id"
                        )
                        ->where("service_code", "<>", "0")
                        ->whereApplicationId($application->id)
                        ->whereIn("type", ["HUAWEI", "ZKBIO"])
                        ->get();
                    $params[] = [
                        'message' => 'success',
                        'batch_id' => $batch->id,
                        'name' => $batch->name,
                        'status' => 'error',
                        "data" => $logTransaction
                    ];
                }
            } else {
                $params[] = [
                    'message' => 'Batch ID Not Found !',
                    'batch_id' => $request->batchId,
                    'status' => 'notfound',
                    'code' => 'BATCH404',
                    'name' => '',
                    "data" => []
                ];
            }

        }
        return response()->json($params);
    }
}
