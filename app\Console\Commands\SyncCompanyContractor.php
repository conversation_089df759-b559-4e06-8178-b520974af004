<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\Settings\Organization;

class SyncCompanyContractor extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-company-contractor';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {

        Log::info('Start Sync Company : '.date('Y-m-d H:i:s'));

        $organizationAliases = Organization::where('organization_type_id', 2)->pluck('alias')->toArray();

        $responseContractor = DB::connection('sqlsrvcontractor')
            ->table('vw_master_company')
            ->select('Fullname', 'Company')
            ->where('Status', 1)
            ->whereNotIn('Company', $organizationAliases)
            ->orderBy('Company', 'ASC')
            ->get();

        foreach($responseContractor as $res){
            $request = array(
                'alias'                => $res->Company,
                'name'                 => $res->Fullname,
                'status'               => 1,
                'organization_type_id' => 2,
                'created_by'           => null,
                'organization_group'   => 2,
                'group_id'             => 7,
                'data_source'          => 'contractor',
            );
            
            Organization::create($request);
        }

        Log::info('Done Sync Company : '.date('Y-m-d H:i:s'));
    }
}
