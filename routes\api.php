<?php

use App\Http\Controllers\Dashboard\ReportController;
use App\Http\Controllers\Dashboard\ReportZkbioContractorController;
use App\Http\Controllers\Dashboard\ReportVisitorController;
use App\Http\Controllers\Dashboard\SummaryContractorController;
use App\Http\Controllers\Dashboard\SummaryEmployeesController;
use App\Http\Controllers\Dashboard\SummaryController;
use App\Http\Controllers\Dashboard\SummaryEmployeeController;
use App\Http\Controllers\Dashboard\TotalPersonController;
use App\Http\Controllers\Settings\OrganizationGroupController;
use App\Http\Controllers\Settings\SettingController;
use App\Mail\OrderShipped;
use App\Traits\ApiResponse;
use Illuminate\Http\Request;
use Intervention\Image\Image;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Storage;
use App\Http\Controllers\TestingController;
use App\Http\Controllers\Api\LoginController;
use App\Http\Controllers\Settings\RoleController;
use App\Http\Controllers\Api\Config\NvrController;
use App\Http\Controllers\Api\Config\UserController;
use App\Http\Controllers\Mapping\MappingController;
use App\Http\Controllers\Settings\PersonsController;
use App\Http\Controllers\Api\Config\AccLevelContoller;
use App\Http\Controllers\Api\Config\FaceListContoller;
use App\Http\Controllers\Api\Service\PersonController;
use App\Http\Controllers\Mapping\MappingTypeController;
use App\Http\Controllers\Settings\AccessTypeController;
use App\Http\Controllers\Settings\PermissionController;
use App\Http\Controllers\Api\Config\DepartmentContoller;
use App\Http\Controllers\Api\AppauthenticationController;
use App\Http\Controllers\Settings\OrganizationController;
use App\Http\Controllers\Api\Config\ApplicationsController;
use App\Http\Controllers\Api\Config\TransactionsController;
use App\Http\Controllers\Settings\ConfigFaceListController;
use App\Http\Controllers\Upload\BatchUploadImageController;
use App\Http\Controllers\Upload\BatchUploadZkbioController;
use App\Http\Controllers\Upload\BatchDeleteZkbioController;
use App\Http\Controllers\Upload\UploadFaceRecordController;
use App\Http\Controllers\Settings\ConfigDepartmentController;
use App\Http\Controllers\Settings\OrganizationTypeController;
use App\Http\Controllers\Settings\ConfigAccessLevelController;
use App\Http\Controllers\Api\Config\MappingDepartmentContoller;
use App\Http\Controllers\Settings\DeviceConfigController;
use Modules\Visitor\App\Http\Controllers\VisitorController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::post('/login', [LoginController::class, 'loginUser']);
Route::post('/register', [LoginController::class, 'createUser']);

Route::get('/ftp', [TestingController::class, 'index']);

Route::get('/generate', function () {

    $inputString = 'INTERNAL JAKARTA IT - APP DEV';
    $length = '3';
    $cleanedString = preg_replace('/[^a-zA-Z0-9]/', ' ', $inputString);

    $words = explode(' ', $cleanedString);

    $initials = '';

    foreach ($words as $word) {
        if (!empty($word)) {
            $initials .= strtoupper(substr($word, 0, $length));
        }
    }

    return substr($inputString, 0, 1);
});

Route::get('/deptCode', function () {
    $data = array(
        'workLocation' => 'IMIP MOROWALI',
        'company' => 'PT IMIP',
        'dept' => 'LAND PLANNING / INFRA',
        'organization_group' => '1',
        'compType' => 'INTERNAL',
        'accessType' => 'GENERAL',
    );

    $generate = app('App\Helpers\DepartmentCode')->deptCode($data, 3);
    return $generate;
});

// Route::get('upload-zkbio', [BatchUploadZkbioController::class, 'index']);
// Route::post('upload-zkbio', [BatchUploadZkbioController::class, 'store']);

Route::match(['get', 'post'], '/', function () {
    return 'Hello World';
});

// Route::group(['middleware' => ['external.app']], function () {
//     Route::get('test-api', function () {
//         return response()->json([
//             'message' => 'TEST API'
//         ]);
//     });
// });

Route::group(['middleware' => ['auth:sanctum']], function () {
    Route::group(['prefix' => 'dashboard'], function () {
        Route::get('summary', [SummaryController::class, 'index']);
        Route::get('total-person', [TotalPersonController::class, 'index']);
        Route::get('total-person-nvr', [TotalPersonController::class, 'getTotalPersonNvr']);
        Route::get('total-by-company', [TotalPersonController::class, 'getPersonTotalByCompany']);

        Route::get('total-employee', [SummaryEmployeeController::class, 'getTotal']);
        Route::get('total-employee-per-company', [SummaryEmployeeController::class, 'index']);
        Route::get('detail-employee-per-company/{id}', [SummaryEmployeeController::class, 'show']);

        Route::get('total-contractor-zkbio-per-company', [SummaryContractorController::class, 'index']);
        Route::get('total-employees-zkbio-per-company', [SummaryEmployeesController::class, 'index']);

        Route::post('export-report', [ReportController::class, 'store']);

        //ReportZkbioContractorController
    });

    Route::prefix('visitor-reports')->group(function () {
        Route::post('/get-report', [ReportVisitorController::class, 'getReport']);
        Route::get('/current-month', [ReportVisitorController::class, 'getCurrentMonth']);
        Route::get('/last-month', [ReportVisitorController::class, 'getLastMonth']);
        Route::post('/export', [ReportVisitorController::class, 'export']);
        Route::post('/summary', [ReportVisitorController::class, 'getSummary']);
    });

    

    Route::post('/logout', [LoginController::class, 'logout']);
    Route::get('/user', function (Request $request) {
        return $request->user();
    });

    Route::get('upload-zkbio', [BatchUploadZkbioController::class, 'index']);
    Route::post('upload-zkbio', [BatchUploadZkbioController::class, 'store']);

    Route::get('delete-zkbio', [BatchDeleteZkbioController::class, 'index']);
    Route::post('delete-zkbio', [BatchDeleteZkbioController::class, 'store']);


    Route::apiResources([
        'userData' => UserController::class,
        'permissions' => PermissionController::class,
        'roles' => RoleController::class,
        'departmentCherry' => DepartmentContoller::class,
        'mappingDepartment' => MappingDepartmentContoller::class,
        'faceList' => FaceListContoller::class,
        'accLevel' => AccLevelContoller::class,
        'nvr' => NvrController::class,
        'applications' => ApplicationsController::class,
        'transactions' => TransactionsController::class,
        'mapping-access-type' => MappingTypeController::class,
        'mapping-access' => MappingController::class,
        'device-config' => DeviceConfigController::class,
    ]);

    Route::get('upload-images', [BatchUploadImageController::class, 'index']);
    Route::post('upload-images', [BatchUploadImageController::class, 'store']);

    Route::get('upload-face-record', [UploadFaceRecordController::class, 'index']);
    Route::post('upload-face-record', [UploadFaceRecordController::class, 'store']);

    Route::post('resettoken/{id}', [ApplicationsController::class, 'resettoken']);
    Route::post('changestatus/{id}', [ApplicationsController::class, 'changestatus']);

    Route::post('sync-data-dept', [ConfigDepartmentController::class, 'syncDept']);
    Route::post('sync-persons', [PersonsController::class, 'sync']);

    Route::group(['prefix' => 'settings'], function () {
        Route::apiResources([
            'face-list' => ConfigFaceListController::class,
            'department' => ConfigDepartmentController::class,
            'access-level' => ConfigAccessLevelController::class,
            'organization' => OrganizationController::class,
            'organization-group' => OrganizationGroupController::class,
            'setting' => SettingController::class,
            'organization-type' => OrganizationTypeController::class,
            'access-type' => AccessTypeController::class,
            'persons' => PersonsController::class,
        ]);
    
    });


    Route::group(['middleware' => ['nvr.verify']], function () {

        // Service
        Route::apiResource('/facelist', FaceListContoller::class);
    });

    Route::group(['middleware' => ['zkteco.verify']], function () {

        // Service

    });
});

Route::post('upload-visitor', [VisitorController::class, 'store']);

Route::group(['middleware' => ['authapp']], function () {
    Route::post('verify', [AppauthenticationController::class, 'verify']);

    Route::apiResource('personx', PersonController::class);
    Route::post('person/disabled', [PersonController::class, 'disabled']);
    Route::post('person/active', [PersonController::class, 'active']);
    Route::post('access-mapping', [AccLevelContoller::class, 'accessMapping']);
});


Route::get('email', function () {
    Mail::to([
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
    ])->send(new OrderShipped());
    return response()->json('email send!');
});
