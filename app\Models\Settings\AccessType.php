<?php

namespace App\Models\Settings;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\Settings\AccessType
 *
 * @property int $id
 * @property string $name
 * @property string $code
 * @property string|null $description
 * @property int $status
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read mixed $status_name
 * @method static \Illuminate\Database\Eloquent\Builder|AccessType newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AccessType newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|AccessType onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|AccessType query()
 * @method static \Illuminate\Database\Eloquent\Builder|AccessType whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccessType whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccessType whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccessType whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccessType whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccessType whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccessType whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccessType whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccessType whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccessType whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|AccessType withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|AccessType withoutTrashed()
 * @mixin \Eloquent
 */
class AccessType extends Model
{
    use HasFactory;
    use SoftDeletes;
    protected $guarded = [];

    protected $appends = ['status_name'];

    public function getStatusNameAttribute()
    {
        return ($this->status == 1) ? 'Active' : 'Not Active';
    }
}
