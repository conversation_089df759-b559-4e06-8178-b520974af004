<?php

namespace App\Models\Mapping;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\Mapping\MappingAccessType
 *
 * @property int $id
 * @property string $name
 * @property string|null $description
 * @property int $status
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|MappingAccessType newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MappingAccessType newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MappingAccessType onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|MappingAccessType query()
 * @method static \Illuminate\Database\Eloquent\Builder|MappingAccessType whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MappingAccessType whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MappingAccessType whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MappingAccessType whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MappingAccessType whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MappingAccessType whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MappingAccessType whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MappingAccessType whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MappingAccessType whereUpdatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MappingAccessType withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|MappingAccessType withoutTrashed()
 * @mixin \Eloquent
 */
class MappingAccessType extends Model
{
    use HasFactory;
    use SoftDeletes;
    protected $guarded = [];
}
