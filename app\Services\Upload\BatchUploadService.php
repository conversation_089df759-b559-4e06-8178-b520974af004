<?php

namespace App\Services\Upload;

use App\Models\JobBatch;
use App\Models\Settings\Nvr;
use App\Traits\NvrCookieHelper;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class BatchUploadService
{
    use NvrCookieHelper;

    public function index($request)
    {
        $options = json_decode($request->options, true);
        $pages = isset($request['start']) ? (int)($request['start'] + 1) : 1;
        $row_data = isset($request['size']) ? (int)$request['size'] : 10;
        $sorts = isset($options['sortField']) ? (string)$options['sortField'] : "created_at";
        $order = (!empty($options['sortOrder'])) ? 'desc' : 'desc';
        $search = isset($request->search) ? (string)$request->search : "";
        $filters = $options['filters'] ?? null;

        $offset = ($pages - 1) * $row_data;

        $result = array();
        $query = JobBatch::where('name', 'LIKE', '%upload image%');

        $result["total"] = $query->count();

        $result['data'] = $query
            ->orderBy($sorts, $order)
            ->skip($request['start'])
            ->take($request['size'])
            // ->offset($offset)
            // ->limit($row_data)
            ->get();

        $result['header'] = $this->frontEndHeader();
        $result['request'] = [
            'offset' => $offset,
            'row_data' => $row_data,
        ];

        $collect = collect($result);

        return $collect->all();
    }

    public function validateNvrCookie($nvr_id)
    {
        
        // get detail nvr
        $getNvr = Nvr::select('ip', 'username', 'password', 'name')
            ->where('id', $nvr_id)->first();

        $nvrName = str_replace(' ', '_', $getNvr->name);
        $cookieNvr = $this->getConfigNvr($nvrName, 'cookie ' . $nvrName);
        // check login user
        $responseUser = Http::withoutVerifying()
            ->withOptions(['verify' => false])
            ->withHeaders([
                'Content-Type' => 'application/json',
                'Cookie' => 'JSESSIONID=' . $cookieNvr,
            ])
            ->get($getNvr->ip . '/users/userid');
        $setResponseUserCode = $responseUser->json();

        Log::info('Response check user in NVR: ' . $nvrName, [
            'response' => $setResponseUserCode
        ]);

        // set login
        if ($setResponseUserCode['resultCode'] != 0) {
            $response = Http::withoutVerifying()
                ->withOptions(['verify' => false])
                ->post($getNvr->ip . '/loginInfo/login/v1.0', [
                    'userName' => $getNvr->username,
                    'password' => $getNvr->password,
                ]);
            $setResponseCode = $response->json();

            Log::info('Response login user NVR: ' . $nvrName, [
                'response' => $setResponseCode
            ]);

            if ($setResponseCode['resultCode'] == 0) {
                $setCookiesNvr  = $response->cookies()->getCookieByName('JSESSIONID')->getValue();
                $this->storeConfigNvr($nvrName, $setCookiesNvr);
            }
        }
    }

    public function frontEndHeader()
    {
        return [
            [
                "accessorKey" => 'name',
                "header" => 'Process Name',
            ],
            [
                "accessorKey" => 'percentage',
                "header" => 'Percentage',
            ],
            [
                "accessorKey" => 'total_jobs',
                "header" => 'Total Jobs',
            ],
            [
                "accessorKey" => 'pending_jobs',
                "header" => 'Pending Jobs',
            ],
            [
                "accessorKey" => 'failed_jobs',
                "header" => 'Failed Jobs',
            ],
            [
                "accessorKey" => 'created_at',
                "header" => 'Created At',
            ],
            [
                "accessorKey" => 'finished_at',
                "header" => 'Finish At',
            ],
        ];
    }
}
