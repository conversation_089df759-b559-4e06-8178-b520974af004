<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\TempConfig
 *
 * @property int $id
 * @property string $config
 * @property string $value
 * @property string $type
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|TempConfig newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TempConfig newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|TempConfig query()
 * @method static \Illuminate\Database\Eloquent\Builder|TempConfig whereConfig($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TempConfig whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TempConfig whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TempConfig whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TempConfig whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|TempConfig whereValue($value)
 * @mixin \Eloquent
 */
class TempConfig extends Model
{
    use HasFactory;
    protected $guarded = [];
}
