<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('log_transaction', function (Blueprint $table) {
            $table->id();
            $table->string('credential_number');
            $table->string('credential_type');
            $table->string('name');
            $table->string('company');
            $table->string('type')->nullable();
            $table->unsignedBigInteger('nvr_id')->nullable();
            $table->unsignedBigInteger('face_list_id')->nullable();
            $table->unsignedBigInteger('access_level_code')->nullable();
            $table->unsignedSmallInteger('status')->default(0);
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->softDeletes();
            $table->timestamps();
            $table->index(['credential_number']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('log_transaction');
    }
};
