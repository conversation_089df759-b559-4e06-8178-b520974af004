<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('face_lists', function (Blueprint $table) {
            $table->id();
            $table->string('nvr_name', 200);
            $table->string('nvr_ip', 20)->nullable();
            $table->string('face_list_name');
            $table->string('face_list_code', 20);
            // $table->unsignedBigInteger('organization_id');
            $table->unsignedSmallInteger('status')->default(0);
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->softDeletes();
            $table->timestamps();


            $table->index(['face_list_code']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('face_lists');
    }
};
