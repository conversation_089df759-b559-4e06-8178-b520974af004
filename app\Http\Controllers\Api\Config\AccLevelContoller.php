<?php

namespace App\Http\Controllers\Api\Config;

use Illuminate\Http\Request;
use App\Models\Settings\Setting;
use App\Http\Controllers\Controller;
use App\Models\Settings\AccessLevel;
use Illuminate\Support\Facades\Http;

class AccLevelContoller extends Controller
{
    public function index(Request $request){

        $access_token       = Setting::getSetting('AccessTokenZkbio');
        if($request->access_id == 6){
            $urlAccLevel = Setting::getSetting('UrlAccLevelZkbioVisitor');
        }else{
            $urlAccLevel  = Setting::getSetting('UrlAccLevelZkbio');
        }
        $response           = Http::get($urlAccLevel.'?pageNo='.$request->pageNo.'&pageSize='.$request->pageSize.'&access_token='.$access_token);
        $jsonData           = $response->json();
        $data               = array();

        foreach($jsonData['data'] as $dt){
            $data[] = array(
                'value' => $dt['id'],
                'label' => $dt['name'],
            );
        }
        if($jsonData['data']){
            try{
                return response()->json([
                    'status'   => true,
                    'message'  => 'Success',
                    'data'     => $data
                ], 200);
            } catch (\Throwable $th) {
                return response()->json([
                    'status'  => false,
                    'message' => $th->getMessage(),
                    'data'    => []
                ], 500);
            }
        }else{
            return response()->json([
                'success' => false,
                'message' => 'Data Not Found !'
            ], 401);
        }
    }

    public function accessMapping(Request $request){
       
        $filters = $request->all();

        $query = AccessLevel::when($filters, function ($query, $filters) {
            if (isset($filters['company'])) {
                $query->where('company', 'LIKE', '%' . $filters['company'] . '%');
            }
            if (isset($filters['accessType'])) {
                $query->where('access_type_id', '=', $filters['accessType']);
            }
        });

        $data = $query
            ->get();

        if (isset($data)) {
            foreach ($data as $dt) {
                $dataAccess[] = array(
                    'access_name'     => $dt->access_name,
                    'access_level_id' => $dt->access_level_id
                );
            }
            return response()->json([
                'status'     => true,
                'message'    => 'Success',
                'data'       => $dataAccess
            ], 200);
        } else {
            return response()->json([
                'status'     => false,
                'message'    => 'Access Not Found.',
                'data'       => null
            ], 422);
        }
    }
}
