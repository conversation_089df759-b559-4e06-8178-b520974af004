<?php

namespace Modules\Vehicle\app\Jobs;

use App\Models\Applications;
use Illuminate\Bus\Queueable;
use Illuminate\Bus\Batchable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use App\Services\ProcessDataVehicleService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class ProcessUploadVehicleZkbioModule implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use Batchable;

    public Collection $row;
    public string $accLevelIds;
    public $batchId;
    public Applications $application;

    /**
     * Create a new job instance.
     */
    public function __construct($row, $batchId, $application)
    {
        $this->row = $row;
        $this->batchId = $batchId;
        $this->application = $application;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $rows = $this->row;

        $service = new ProcessDataVehicleService();

        $service->insertDataVehicle($rows, batch_id: $this->batchId, applications: $this->application);
    }
}
