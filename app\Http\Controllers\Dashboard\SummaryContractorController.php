<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class SummaryContractorController extends Controller
{
    public function index(Request $request)
    {
        $service = new \App\Services\Dashboard\SummaryContractorService();
        return response()->json([
            "data" => [
                "contractor" => $service->getSummaryMaster(),
                "zkbio" => $service->getSummaryZkbio(),
            ],
        ]);
    }
}