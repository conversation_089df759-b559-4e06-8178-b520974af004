<?php
namespace App\Http\Controllers\Api\Config;

use App\Models\Transactions;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Str;
use Carbon\Carbon;

class TransactionsController extends Controller
{
	public function index(){

        $transactions                       = DB::table('transactions')
            ->leftJoin('m_mapping_dept', 'transactions.mapping_id', '=', 'm_mapping_dept.id')
            ->leftJoin('applications', 'transactions.applications_id', '=', 'applications.id')
            ->select('transactions.*', 'm_mapping_dept.name AS dept_name', 'applications.name AS app_name')
            ->get();
        return response()->json([
            'status' 						=> true,
            'message' 						=> 'Success',
            'data' 							=>  $transactions
        ], 200);
    }
}