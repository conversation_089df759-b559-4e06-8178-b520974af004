<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('mapping_accesses', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('mapping_code');
            $table->unsignedBigInteger('face_list_id');
            $table->unsignedBigInteger('access_level_id');
            $table->unsignedBigInteger('mapping_access_type_id');
            $table->string('description')->nullable();
            $table->unsignedSmallInteger('status')->default(0);
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->softDeletes();
            $table->timestamps();


            $table->index(['mapping_code']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('mapping_accesses');
    }
};
