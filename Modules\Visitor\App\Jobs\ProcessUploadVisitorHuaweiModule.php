<?php

namespace Modules\Visitor\App\Jobs;

use App\Models\Settings\Nvr;
use App\Services\ProcessDataVisitorService;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Collection;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ProcessUploadVisitorHuaweiModule implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use Batchable;

    public Collection $row;
    public int $nvrId;
    public $batchId;

    /**
     * Create a new job instance.
     */
    public function __construct($row, $nvrId, $batchId)
    {
        $this->row = $row;
        $this->nvrId = $nvrId;
        $this->batchId = $batchId;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $rows = $this->row;

        $service = new ProcessDataVisitorService();
        $nvr = Nvr::all();
        $service->uploadFaceRecord($rows, $this->nvrId, $this->batchId);
    }
}
