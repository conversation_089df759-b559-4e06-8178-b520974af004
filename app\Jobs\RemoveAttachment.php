<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class RemoveAttachment implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $files;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($files)
    {
        $this->files = $files;
        $this->onQueue('transactionDocument');
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        sleep(3);
        if ($this->files) {
            if (is_array($this->files) == 1) {
                foreach ($this->files as $file) {
                    // iterate files
                    if (is_file($file)) {
                        unlink($file);
                    } //end if
                }
            } else {
                if (is_file($this->files)) {
                    unlink($this->files);
                } //end if
            }
        }
    }
}
