<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\DeviceConfigDetail
 *
 * @property string $id
 * @property string $device_config_id
 * @property string $header
 * @property string $sub_menu
 * @property string $field
 * @property string $value
 * @property string $notes
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceConfigDetail newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceConfigDetail newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceConfigDetail query()
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceConfigDetail whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceConfigDetail whereDeviceConfigId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceConfigDetail whereField($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceConfigDetail whereHeader($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceConfigDetail whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceConfigDetail whereNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceConfigDetail whereSubMenu($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceConfigDetail whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceConfigDetail whereValue($value)
 * @mixin \Eloquent
 */
class DeviceConfigDetail extends Model
{
    use HasFactory;

    use HasUlids;

    protected $guarded = [];
}
