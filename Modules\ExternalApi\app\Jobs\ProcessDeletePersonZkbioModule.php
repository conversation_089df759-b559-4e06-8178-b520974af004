<?php

namespace Modules\ExternalApi\app\Jobs;

use App\Services\ProcessDataPersonService;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessDeletePersonZkbioModule implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use Batchable;

    /**
     * Create a new job instance.
     */
    public function __construct(public array $rows)
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $Services = new ProcessDataPersonService();
        foreach ($this->rows as $key => $item) {
            if ($item["statusEmployee"] == "0") {
                $Services->processDeletePersonZkbio($item, $this->batch()->id);
            } else {
                $row = array_merge($item, ['is_leave' => true]);
                Log::info('rows', [
                    'rows type' => gettype($row),
                    'rows' => $row,
                ]);
                $Services->processUpdateAccessLevel($row, $this->batch()->id);
            }
        }
    }
}
