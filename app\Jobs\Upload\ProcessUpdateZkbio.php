<?php

namespace App\Jobs\Upload;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Bus\Batchable;
use Illuminate\Queue\InteractsWithQueue;
use App\Services\ProcessDataPersonService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Contracts\Queue\ShouldBeUnique;

class ProcessUpdateZkbio implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use Batchable;

    public $row;
    public $batch_id;
    /**
     * Create a new job instance.
     */
    public function __construct($row,$batch_id)
    {
        $this->row           = $row;
        $this->batch_id      = $batch_id;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $Services = new ProcessDataPersonService();
        $rows     = $this->row;
        $Services->processUpdateAccessLevel($rows, $this->batch_id);
    }
}
