#!/bin/bash

if [ ! -f "vendor/autoload.php" ]; then
    composer install --no-progress --no-interaction
else
    composer install --no-progress --no-interaction
fi

if [ ! -f ".env" ]; then
    echo "Creating env file for env $APP_ENV"
    cp .env.prd .env
else
    echo "env file exists."
fi

echo "start artisan config cache."
php artisan config:cache
echo "start artisan migrate."
php artisan migrate
echo "start artisan optimize."
php artisan optimize
echo "start artisan clear cache."
php artisan view:cache

echo "end artisan command."

echo "start php fpm"
# Start php-fpm in the background
php-fpm -D

# echo "start nginx"
# # Start nginx in the background
# nginx -g "daemon off;"

echo "start supervisord"
# Start supervisor in the background
exec /usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.conf

# Optionally, add a message indicating that the services are running
echo "All services are running."

# Wait for all background processes to finish
wait
