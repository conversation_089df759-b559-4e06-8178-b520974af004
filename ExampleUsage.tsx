import React from 'react';
import { MantineProvider } from '@mantine/core';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { DatesProvider } from '@mantine/dates';
import VisitorReport from './VisitorReport';

// Create a query client
const queryClient = new QueryClient({
    defaultOptions: {
        queries: {
            retry: 2,
            staleTime: 5 * 60 * 1000, // 5 minutes
        },
    },
});

// Example usage of the VisitorReport component
export default function ExampleUsage() {
    return (
        <QueryClientProvider client={queryClient}>
            <MantineProvider>
                <DatesProvider settings={{ locale: 'id' }}>
                    <div style={{ padding: '20px' }}>
                        <h1>Visitor Report Dashboard</h1>
                        <VisitorReport />
                    </div>
                </DatesProvider>
            </MantineProvider>
        </QueryClientProvider>
    );
}

// Alternative: If you want to use it in an existing app with providers already set up
export function VisitorReportPage() {
    return (
        <div style={{ padding: '20px', maxWidth: '1200px', margin: '0 auto' }}>
            <VisitorReport />
        </div>
    );
}
