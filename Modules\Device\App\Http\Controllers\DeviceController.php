<?php

namespace Modules\Device\App\Http\Controllers;

use App\Models\DeviceConfig;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Models\Settings\Setting;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Http;
use Illuminate\Http\RedirectResponse;
use Modules\Device\App\Http\Requests\GetDeviceRequest;
use Modules\Device\App\Http\Requests\OpenDeviceRequest;
use Modules\Device\App\Http\Requests\CloseDeviceRequest;

class DeviceController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('device::index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('device::create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function openGate(OpenDeviceRequest $request)
    {
        $accessToken = Setting::getSetting('AccessTokenZkbio');

        $doorName = $request->doorName;
        $interval = $request->interval;
        $urlOpenGate = Setting::getSetting('UrlOpenGateZkbio');
        $upload = Http::withoutVerifying()
            ->withOptions(['verify' => false])
            ->withHeaders([
                'Content-Type' => 'application/json',
            ])
            ->post($urlOpenGate . '?doorName='.$doorName.'&interval='.$interval.'&access_token=' . $accessToken);

            return response()->json([
                'status'   => true,
                'message'  => 'Success'
            ], 200);
    }

    public function closeGate(CloseDeviceRequest $request)
    {
        $accessToken = Setting::getSetting('AccessTokenZkbio');

        $doorName = $request->doorName;
        $interval = $request->interval;
        $urlOpenGate = Setting::getSetting('UrlCloseGateZkbio');
        $upload = Http::withoutVerifying()
            ->withOptions(['verify' => false])
            ->withHeaders([
                'Content-Type' => 'application/json',
            ])
            ->post($urlOpenGate . '?doorName='.$doorName.'&access_token=' . $accessToken);

            return response()->json([
                'status'   => true,
                'message'  => 'Success'
            ], 200);
    }

    /**
     * Show the specified resource.
     */
    public function show(GetDeviceRequest $request)
    {

        $pageNo   = $request->pageNo;
        $pageSize = $request->pageSize;

        if(isset($request->area)){
            $area      = $request->area;
            $areaArray = explode(",", str_replace("'", "", $area));
        }else{
            $area      = Setting::getSetting('AreaName');
            $areaArray = explode(",", str_replace("'", "", $area));
        }
        
        $config = DeviceConfig::select('device_location','serial_number','device_name', 'area_name', DB::raw("CASE 
                WHEN device_type = 1 THEN 'Person' 
                WHEN device_type = 2 THEN 'Vehicle' 
                ELSE 'Unknown' 
            END as device_type_label"),
            'latitude',
            'longitude'
            )
            ->whereIn('area_name', $areaArray)
            ->skip(($pageNo - 1) * $pageSize)
            ->when($request->deviceType, function ($query, $deviceType) {
                return $query->where('device_type', $deviceType);
            })
            ->take($pageSize)
            ->get();
        
        return response()->json([
            'status'   => true,
            'message'  => 'Success',
            'data'     => $config
        ], 200);
    }

     /**
     * Show the specified resource.
     */
    public function getAllPos(GetDeviceRequest $request)
    {

        $pageNo   = $request->pageNo;
        $pageSize = $request->pageSize;

        if(isset($request->area)){
            $area      = $request->area;
            $areaArray = explode(",", str_replace("'", "", $area));
        }else{
            $area      = Setting::getSetting('AreaName');
            $areaArray = explode(",", str_replace("'", "", $area));
        }
        
     
        $config = DeviceConfig::select(
            DB::raw("REPLACE(device_location, ' ', '-') as device_location"),
            DB::raw("STRING_AGG(serial_number, ',') as serial_numbers"), 
            DB::raw("STRING_AGG(device_name, ',') as device_names"),
            'area_name',
            DB::raw("CASE 
                WHEN device_type = 1 THEN 'Person' 
                WHEN device_type = 2 THEN 'Vehicle' 
                ELSE 'Unknown' 
            END as device_type_label"),
            'latitude',
            'longitude'
        )
        ->whereIn('area_name', $areaArray)
        ->groupBy(
            'device_location', // Kelompokkan berdasarkan device_location langsung
            'area_name',
            'device_type',
            'latitude',
            'longitude'
        )
        ->skip(($pageNo - 1) * $pageSize)
        ->take($pageSize)
        ->get();
    
        
        return response()->json([
            'status'   => true,
            'message'  => 'Success',
            'data'     => $config, 
        ], 200);
    }

   public function getAllArea(Request $request)
   {

       $pageNo   = $request->pageNo;
       $pageSize = $request->pageSize;

       $config = DeviceConfig::select(
           'area_name as areaName',
       )
       ->groupBy(
           'area_name'
       )
       ->skip(($pageNo - 1) * $pageSize)
       ->take($pageSize)
       ->get()
       ->makeHidden(['device_type_name']);
   
       
       return response()->json([
           'status'  => true,
           'message' => 'Success',
           'data'    => $config,
       ], 200);

   }

   public function getDeviceType(Request $request)
   {

       $pageNo   = $request->pageNo;
       $pageSize = $request->pageSize;
    
       $config = DeviceConfig::select(
        'device_type as idDeviceType',
            DB::raw("CASE 
            WHEN device_type = 1 THEN 'Person' 
            WHEN device_type = 2 THEN 'Vehicle' 
            ELSE 'Unknown' 
        END as deviceTypeLabel")
       )
       ->groupBy(
           'device_type'
       )
       ->skip(($pageNo - 1) * $pageSize)
       ->take($pageSize)
       ->get()
       ->makeHidden(['device_type_name']);
   
       
       return response()->json([
           'status'   => true,
           'message'  => 'Success',
           'data'     => $config, 
       ], 200);
   }


    /**
     * Show the form for editing the specified resource.
     */
    public function edit($id)
    {
        return view('device::edit');
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id): RedirectResponse
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        //
    }
}
