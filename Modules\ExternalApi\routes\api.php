<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Modules\ExternalApi\app\Http\Controllers\ExternalApiController;

Route::group(['middleware' => ['external.app'], 'prefix' => 'v1'], function () {
    Route::get('personnel', [ExternalApiController::class, 'show']);
    Route::post('personnel', [ExternalApiController::class, 'store']);
    Route::post('personnel/mutation', [ExternalApiController::class, 'mutation']);
    Route::post('personnel/changePhoto', [ExternalApiController::class, 'changePhoto']);
    Route::put('personnel', [ExternalApiController::class, 'update']);
    Route::delete('personnel', [ExternalApiController::class, 'destroy']);
});
