<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Validator;

class LoginController extends Controller
{
    /**
     * Creates a new user with the given data.
     *
     * @param Request $request contains the user data
     * @throws \Throwable if an error occurs during the process
     * @return JsonResponse with the response data
     */
    public function createUser(Request $request): JsonResponse
    {
        try {
            //Validated
            $validateUser = Validator::make(
                $request->all(),
                [
                    'name'     => 'required',
                    'email'    => 'required|email|unique:users,email',
                    'password' => 'required',
                    'username' => 'required|unique:users,username',
                ]
            );

            if ($validateUser->fails()) {
                return response()->json([
                    'status'  => false,
                    'message' => 'validation error',
                    'errors'  => $validateUser->errors()
                ], 401);
            }

            $user = User::create([
                'name'     => $request->name,
                'email'    => $request->email,
                'username' => $request->username,
                'password' => Hash::make($request->password),
                'token_cherry' => ''
            ]);

            return response()->json([
                'status'  => true,
                'message' => 'User Created Success',
                'token'   => $user->createToken("API TOKEN")->plainTextToken
            ], 200);
        } catch (\Throwable $th) {
            return response()->json([
                'status'  => false,
                'message' => $th->getMessage()
            ], 500);
        }
    }

    /**
     * Login The User
     * @param Request $request
     */
    public function loginUser(Request $request)
    {
        try {

            $credentials = $request->validate([
                'username' => 'required',
                'password' => 'required',
            ]);

            $user = User::where('username', $request->username)->first();

            if (!$user) {
                return response()->json([
                    'status'  => false,
                    'message' => 'Username is not registered !',
                    'data'    => []
                ], 401);
            }

            //login cherry check
            $response = Http::post(config('app.token_cherry'), [
                'CommandName'   => 'RequestToken',
                'ModelCode'     => 'AppUserAccount',
                'UserName'      => $request->username,
                'Password'      => $request->password,
                'ParameterData' => [],
            ]);

            if (isset($response['Token'])) {
                $this->insertUser($response, $request->password);
            } else {
                return response()->json([
                    'status' => false,
                    'message' => $response['Message'],
                    'data'    => []
                ], 401);
            }

            if (!Auth::attempt($credentials)) {
                return response()->json([
                    'status'  => false,
                    'message' => 'Username & Password does not match with our record.',
                    'data'    => []
                ], 401);
            }

            return response()->json([
                'status'  => true,
                'message' => 'Success',
                'data'    => [
                    'token' => $user->createToken("API TOKEN")->plainTextToken
                ]
            ], 200);
        } catch (\Throwable $th) {
            return response()->json([
                'status'  => false,
                'message' => $th->getMessage(),
                'data'    => []
            ], 500);
        }
    }

    public function logout(Request $request)
    {
        try {
            // Auth::logout();
            $request->user()->tokens()->delete();

            return response()->json([
                'status'  => true,
                'message' => 'Success',
                'data'    => [],
                'token' => $request->user()->tokens()
            ]);
        } catch (\Throwable $th) {
            return response()->json([
                'status'  => false,
                'message' => $th->getMessage(),
                'data'    => []
            ], 500);
        }
    }

    protected function insertUser($response, $password)
    {
        $data = [
            'name'         => $response['Data']['Name'],
            'token_cherry' => $response['Token'],
            'username'     => $response['UserName'],
            'password'     => bcrypt($password),
            'company'      => $response['Data']['Company'],
            'department'   => $response['Data']['Organization'],
            'email'        => $response['Data']['Email'],
            'last_login'   => date('Y-m-d H:i:s')
        ];

        $user = User::where('username', '=', $response['UserName'])
            ->update($data);
        return $user;
    }

    public function userData()
    {
        return response()->json([
            'status' => true,
            'message' => 'Success',
            'data' =>  User::all()
        ], 200);
    }
}
