<?php

namespace App\Http\Controllers\Dashboard;

use App\Exports\DashboardReportExport;
use App\Http\Controllers\Controller;
use App\Services\Dashboard\SummaryContractorService;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

class ReportZkbioContractorController extends Controller
{
    public function index(Request $request)
    {
        $service = new SummaryContractorService();
        $contractor = collect($service->getSummaryMaster());
        $zkbio = collect($service->getSummaryZkbio());

        return $contractor;
    }
}