<?php

namespace Modules\Simper\app\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Modules\Simper\Database\factories\SimperDetailFactory;

class SimperDetail extends Model
{
    use HasFactory;

    protected $connection = 'sqlsrvsimper';

    protected $table = 'sft_simper'; 

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [];

    
    protected static function newFactory(): SimperDetailFactory
    {
        //return SimperDetailFactory::new();
    }
}
