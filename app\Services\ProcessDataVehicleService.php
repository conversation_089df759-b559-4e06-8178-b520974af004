<?php

namespace App\Services;

use Carbon\Carbon;
use App\Models\Applications;
use App\Helpers\DepartmentCode;
use App\Models\Settings\Setting;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use App\Models\Settings\AccessLevel;
use Illuminate\Support\Facades\Http;
use App\Models\Settings\Organization;
use App\Models\Settings\Log_transaction;

class ProcessDataVehicleService
{
      /**
     * @param Collection $rows
     * @param string $batch_id
     * @param Applications $applications
     * @return void
     */
    public function uploadAccessLevel(Collection $rows, string $batch_id, Applications $applications): void
    {
        $start = Carbon::now()->format('Y-m-d H:i:s');

        Log::info('start upload Vehicle ZKBIO, time: ' . $start);

        $access = AccessLevel::where('access_type_id', $row['accessMapping'])->first();

        Log::info('check access'.$access);

        $accessLevelCode = $access->access_level_code;
        $accessLevelName = $access->access_level_name;

        $data_level = [];
        foreach ($rows as $row) {

            $accessToken    = Setting::getSetting('AccessTokenZkbio');
            $pid            = $row['credentialNumber'];
            $cardNumber     = $row['cardNumber'];
            $data_name      = $row['name'];
            $data_last_name = '';
            $acc_level_name = $accessLevelCode;
            $acc_level_code = $accessLevelName;
            $accStartTime   = $row['accStartTime'];
            $accEndTime     = $row['accEndTime'];

            $code_depart = 'VHC001';
            $name_depart = 'VEHICLE';

            Log::info('Department Name ' . $name_depart . ' (' . $code_depart . ') to ZKBIO ');

            $this->processUploadVehicle($accessToken,$pid, $cardNumber, $code_depart, $name_depart, $data_name, $data_last_name, $accStartTime, $accEndTime, $row, $batch_id, $acc_level_name,$acc_level_code);
        }

        Log::info('Upload Vehicle ZKBIO Done ! Time : ' . Carbon::now()->diffForHumans($start));
    }

      /**
     * @param Collection $rows
     * @param string $batch_id
     * @param Applications $applications
     * @return void
     */
    public function insertDataVehicle(Collection $rows, string $batch_id, Applications $applications): void
    {
        $start = Carbon::now()->format('Y-m-d H:i:s');


        Log::info('start upload Vehicle ZKBIO, time: ' . $start);

        $data_level = [];
        foreach ($rows as $row) {

            $access = AccessLevel::where('access_level_id', $row['accessMapping'])->first();
            $accessLevelCode = $access->access_level_code;
            $accessLevelName = $access->access_level_name;

            Log::info('Check Access ' . $accessLevelCode.' - '.$accessLevelName);

            $accessToken    = Setting::getSetting('AccessTokenZkbio');
            $pid            = $row['credentialNumber'];
            $cardNumber     = $row['cardNumber'];
            $data_name      = $row['name'];
            $data_last_name = '';
            $acc_level_name = $accessLevelName;
            $acc_level_code = $accessLevelCode;
            $accStartTime   = $row['accStartTime'];
            $accEndTime     = $row['accEndTime'];

            /* Emp Data */
            // $deptCode = $this->getDepartmentCode((array) $row, $row['access_type']);
            $code_depart = 'VHC001';
            $name_depart = 'VEHICLE';
            Log::info('Department Name ' . $name_depart . ' (' . $code_depart . ') to ZKBIO ');

            $this->processUploadVehicle($accessToken,$pid, $cardNumber, $code_depart, $name_depart, $data_name, $data_last_name, $accStartTime, $accEndTime, $row, $batch_id,$acc_level_name,$acc_level_code);
        }

        Log::info('Upload Vehicle ZKBIO Done ! Time : ' . Carbon::now()->diffForHumans($start));
    }


    /**
     * @param Collection $rows
     * @param string $batch_id
     * @param Applications $applications
     * @return void
     */
    public function updateDataVehicle(Collection $rows, string $batch_id, Applications $applications): void
    {
        $start = Carbon::now()->format('Y-m-d H:i:s');

        Log::info('start upload Vehicle ZKBIO, time: ' . $start);

        $data_level = [];
        foreach ($rows as $row) {

            $access = AccessLevel::where('access_level_id', $row['accessMapping'])->first();
            $accessLevelCode = $access->access_level_code;
            $accessLevelName = $access->access_level_name;

            $accessToken    = Setting::getSetting('AccessTokenZkbio');
            $pid            = $row['credentialNumber'];
            $cardNumber     = $row['cardNumber'];
            $data_name      = $row['name'];
            $data_last_name = '';
            $acc_level_name = $accessLevelName;
            $acc_level_code = $accessLevelCode;
            $accStartTime   = $row['accStartTime'];
            $accEndTime     = $row['accEndTime'];

            /* Emp Data */
            // $deptCode = $this->getDepartmentCode((array) $row, $row['access_type']);
            $code_depart = 'VHC001';
            $name_depart = 'VEHICLE';

            Log::info('Department Name ' . $name_depart . ' (' . $code_depart . ') to ZKBIO ');

            $this->processUploadVehicle($accessToken,$pid, $cardNumber, $code_depart, $name_depart, $data_name, $data_last_name, $accStartTime, $accEndTime, $row, $batch_id,$acc_level_name,$acc_level_code);
        }

        Log::info('Upload Vehicle ZKBIO Done ! Time : ' . Carbon::now()->diffForHumans($start));
    }

     /**
     * @param Collection $rows
     * @param string $batch_id
     * @param Applications $applications
     * @return void
     */
    public function deleteDataVehicle(Collection $rows, string $batch_id, Applications $applications): void
    {
        $start = Carbon::now()->format('Y-m-d H:i:s');

        Log::info('start upload Vehicle ZKBIO, time: ' . $start);

        foreach ($rows as $row) {

            $accessToken    = Setting::getSetting('AccessTokenZkbio');
            $pid          = $row['credentialNumber'];
            $isDisabled   = $row['isDisabled'];
            $accStartTime = $row['accStartTime'];
            $accEndTime   = $row['accEndTime'];

            $code_depart = 'VHC001';
            $name_depart = 'VEHICLE';
            Log::info('Department Name ' . $name_depart . ' (' . $code_depart . ') to ZKBIO ');

            $this->processDeleteVehicle($accessToken,$pid, $isDisabled, $accStartTime, $accEndTime, $row, $batch_id);
        }

        Log::info('Upload Vehicle ZKBIO Done ! Time : ' . Carbon::now()->diffForHumans($start));
    }

     /**
     * @param array $row
     * @param string $accessType
     * @return array
     */
    public function getDepartmentCode(array $row, string $accessType = 'GENERAL'): array
    {
        $org = Organization::where('name', $row['company'])->first();
        $org_group = $org->organization_group;
        $org_type = $org->organization_type;

        $data_emp = array(
            'workLocation' => 'MOROWALI',
            'company' => $org->alias,
            'dept' => $row['dept_name'],
            'organization_group' => $org_group,
            'compType' => $org_type,
            'accessType' => $accessType,
        );

        $departmentCode = new DepartmentCode();
        $deptCode = $departmentCode->deptCodeVehicle($data_emp, 3);
        return [
            'deptCode' => $deptCode['deptCode'],
            'deptName' => $deptCode['deptName'],
        ];
    }

      /**
     * @param string $accessToken
     * @param string $pid
     * @param string $code_depart
     * @param string $name_depart
     * @param string $data_name
     * @param $data_last_name
     * @param array $row
     * @param string $new_data_level
     * @param string $batch_id
     * @return void
     */
    protected function processUploadVehicle(string $accessToken, string $pid, string $cardNumber, string $code_depart, string $name_depart, string $data_name, $data_last_name, string $accStartTime, string $accEndTime, array $row, string $batch_id, string $acc_level_name, string $acc_level_code): void
    {
        $params = [
            'pin'          => $pid,
            'deptCode'     => $code_depart,
            'deptName'     => $name_depart,
            'name'         => $data_name,
            'cardNo'       => $cardNumber,
            'lastName'     => $data_last_name,
            'accLevelIds'  => $acc_level_code,
            'accStartTime' => $accStartTime,
            'accEndTime'   => $accEndTime
        ];
        $urlAddPerson = Setting::getSetting('UrlAddPersonZkbio');
        $upload = Http::withoutVerifying()
            ->withOptions(['verify' => false])
            ->withHeaders([
                'Content-Type' => 'application/json',
            ])
            ->post($urlAddPerson . '?access_token=' . $accessToken, $params);

        $urlAddPerson = Setting::getSetting('UrlAddPersonZkbio');
        $upload = Http::withoutVerifying()
            ->withOptions(['verify' => false])
            ->withHeaders([
                'Content-Type' => 'application/json',
            ])
            ->post($urlAddPerson . '?access_token=' . $accessToken, $params);

        Log::info('Result from upload access level zkbio', [
            'json' => $upload->json(),
            'url' => $urlAddPerson
        ]);

        $acc_level_name = $acc_level_name;
        $acc_level_code = $acc_level_code;

        $this->createLogAccessLevel(employee: $row, upload: $upload, params: $params, batch_id: $batch_id, submitted: true, message: '', code : '', access_level_name : $acc_level_name, access_level_code : $acc_level_code);
    }


     /**
     * @param string $accessToken
     * @param string $pid
     * @param string $isDisabled
     * @param string $accStartTime
     * @param string $accStartTime
     * @param array $row
     * @param string $batch_id
     * @return void
     */
    protected function processDeleteVehicle(string $accessToken, string $pid,string $isDisabled,string $accStartTime, string $accEndTime, array $row, string $batch_id): void
    {
        $params = [
            'pin'          => $pid,
            'isDisabled'   => $isDisabled,
            'accStartTime' => $accStartTime,
            'accEndTime'   => $accEndTime
        ];
        $urlAddPerson = Setting::getSetting('UrlAddPersonZkbio');
        $upload = Http::withoutVerifying()
            ->withOptions(['verify' => false])
            ->withHeaders([
                'Content-Type' => 'application/json',
            ])
            ->post($urlAddPerson . '?access_token=' . $accessToken, $params);

        $urlAddPerson = Setting::getSetting('UrlAddPersonZkbio');
        $upload = Http::withoutVerifying()
            ->withOptions(['verify' => false])
            ->withHeaders([
                'Content-Type' => 'application/json',
            ])
            ->post($urlAddPerson . '?access_token=' . $accessToken, $params);

        Log::info('Result from upload access level zkbio', [
            'json' => $upload->json(),
            'url' => $urlAddPerson,
            'log' => $params
        ]);
        $this->createLogAccessLevel(employee: $row, upload: $upload, params: $params, batch_id: $batch_id, submitted: true, message: '', code : '');
    }

      /**
     * @param array $employee
     * @param $new_data_level
     * @param $upload
     * @param $params
     * @param bool $submitted
     * @param string $message
     * @param $batch_id
     * @return void
     */
    protected function createLogAccessLevel(array $employee, $upload, $params, bool $submitted = true, string $message = '', $batch_id, $code = '', $access_level_name = null, $access_level_code = null): void
    {
        if (!$submitted) {
            $code = $code;
        } else {
            $code = ($upload->collect()->has('code')) ? $upload->collect()['code'] : (
                ($upload->collect()->has('ret')) ? $upload->collect()['ret'] : -1
            );
            $message = $upload->collect()['message'];
        }
        $log = Log_transaction::create([
            'credential_number' => $employee['credentialNumber'],
            'credential_type' => '0',
            'name' => $employee['name'],
            'company' => $employee['company'],
            'type' => 'ZKBIO', // HUAWEI & ZKBIO
            'nvr_id' => null,
            'face_list_id' => null,
            'status' => $employee['statusEmployee'],
            'application_id' => $employee['application_id'],
            'service_code' => $code,
            'service_message' => $message,
            'service_payload' => '',
            'batch_id' => $batch_id,
            'access_level_name' => $access_level_name
        ]);

        Log::info('Create Log Zkbio for vehicle ' . $employee['credentialNumber'] . ' with id ' . $log->id);
    }
}
