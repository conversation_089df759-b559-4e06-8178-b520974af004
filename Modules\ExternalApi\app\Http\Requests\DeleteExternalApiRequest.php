<?php

namespace Modules\ExternalApi\app\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class DeleteExternalApiRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'form.*.photo' => 'required|string',
            'form.*.name' => 'required|string',
            'form.*.identifyNumber' => 'required|string',
            'form.*.type' => 'required|string',
            'form.*.gender' => 'required|in:MALE,FEMALE',
            'form.*.birthDate' => 'required|date_format:Y-m-d',
            'form.*.nationality' => 'required|string',
            'form.*.employeeType' => 'required|string',
            'form.*.description' => 'nullable|string',
            'form.*.company' => 'required|string',
            'form.*.workLocation' => 'required|string',
            'form.*.departmentName' => 'required|string',
            'form.*.leaveDate' => 'required|date',
            'form.*.statusEmployee' => 'required|string|in:2,0',
            'accessType' => 'required|string|in:GENERAL,KI,CONTRACTOR',
            'service' => 'required|string|in:ALL,Huawei,Zkbio',
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }
}
