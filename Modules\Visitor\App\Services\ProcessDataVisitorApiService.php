<?php

namespace Modules\Visitor\App\Services;

use Throwable;
use Illuminate\Bus\Batch;
use App\Models\Applications;
use App\Helpers\DepartmentCode;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use App\Models\Settings\Organization;
use App\Models\Settings\Log_transaction;
use App\Services\ProcessDataPersonService;
use Modules\Visitor\App\Jobs\ProcessUploadVisitorZkbioModule;
use Modules\Visitor\App\Jobs\ProcessUploadVisitorHuaweiModule;

class ProcessDataVisitorApiService
{
      /**
     * @param $data
     * @param Applications $application
     * @return array
     * @throws Throwable
     */

    public function getDepartmentCode(array $row, string $accessType = 'GENERAL'): array
     {
         $org = Organization::where('name', $row['company'])->first();
         $org_group = $org->organization_group;
         $org_type = $org->organization_type;
 
         $data_emp = array(
             'workLocation' => 'MOROWALI',
             'company' => $org->alias,
             'dept' => $row['dept_name'],
             'organization_group' => $org_group,
             'compType' => $org_type,
             'accessType' => $accessType,
         );
 
         $departmentCode = new DepartmentCode();
         $deptCode = $departmentCode->deptCodeVehicle($data_emp, 3);
         return [
             'deptCode' => $deptCode['deptCode'],
             'deptName' => $deptCode['deptName'],
         ];
     }
 

     public function processQueueZkbio($data, Applications $application): array
     {
 
         $collection = collect($data);
         $chunks = $collection->chunk(20);
         $batchQueue = [];
         $batch = Bus::batch([])
             ->catch(function (Batch $batch, Throwable $e) use ($application) {
                 Log::info('error upload batch ' . now(), [
                     'error' => $e->getTraceAsString(),
                 ]);
                 $logTransaction = Log_transaction::where('batch_id', $batch->id)
                     ->select(
                         "credential_number",
                         "name",
                         "company",
                         "nvr_id",
                         "face_list_id",
                         "service_code",
                         "service_message",
                         "batch_id"
                     )
                     ->where("service_code", "<>", "0")
                     ->where('application_id', $application->id)
                     ->where('type', "ZKBIO")
                     ->get();
 
                 $params = [
                     'message' => $e->getMessage(),
                     'batch_id' => $batch->id,
                     'status' => 'error',
                     'name' => $batch->name,
                     'data' => $logTransaction,
                     // "application" => $application->id,
                 ];
 
                 Http::post($application->link_url, $params);
 
                 activity()
                     ->performedOn($application)
                     ->withProperties($params)
                     ->log('Error process face record to application ' . $application->name);
             })
             ->then(function (Batch $batch) use ($application) {
                 Log::info('Batch ID ZKBIO ' . $batch->id);
                 $logTransaction = Log_transaction::where('batch_id', $batch->id)
                     ->select(
                         "credential_number",
                         "name",
                         "company",
                         "nvr_id",
                         "face_list_id",
                         "service_code",
                         "service_message",
                         "batch_id"
                     )
                     ->where('application_id', $application->id)
                     ->where('type', "ZKBIO")
                     ->get();
                 $params = [
                     'message' => 'success',
                     'batch_id' => $batch->id,
                     'status' => 'completed',
                     'name' => $batch->name,
                     "data" => $logTransaction,
                 ];
                 Http::post($application->link_url, $params);
 
                 activity()
                     ->performedOn($application)
                     ->withProperties($params)
                     ->log('Success process face record to application ' . $application->name);
             })
             ->name('Upload Zkbio Visitor' . date('Y-m-d H:i:s'))
             ->onQueue('ZkbioVisitorApiCheckInFix')
             ->allowFailures()
             ->dispatch();
 
         foreach ($chunks as $key => $chunk) {
             $batch->add(new ProcessUploadVisitorZkbioModule(row: $chunk, batchId: $batch->id, application: $application));
         }
 
         return [
             [
                 'label' => 'ZKBIO',
                 'batch_id' => $batch->id
             ]
         ];
     }

    /* Zkbio Services */
    // public function processQueueZkbio($data, Applications $application): array
    // {

    //     $collection = collect($data);
    //     $chunks = $collection->chunk(20);
    //     $batchQueue = [];
    //     $batch = Bus::batch([])
    //         ->catch(function (Batch $batch, Throwable $e) use ($application) {
    //             Log::info('error upload batch ' . now(), [
    //                 'error' => $e->getTraceAsString(),
    //             ]);
    //             // sleep(5);
    //             $logTransaction = Log_transaction::where('batch_id', $batch->id)
    //                 ->select(
    //                     "credential_number",
    //                     "name",
    //                     "company",
    //                     "nvr_id",
    //                     "face_list_id",
    //                     "service_code",
    //                     "service_message",
    //                     "batch_id"
    //                 )
    //                 ->where("service_code", "<>", "0")
    //                 ->where('application_id', $application->id)
    //                 ->where('type', "ZKBIO")
    //                 ->get();

    //             $params = [
    //                 'message' => $e->getMessage(),
    //                 'batch_id' => $batch->id,
    //                 'status' => 'error',
    //                 'name' => $batch->name,
    //                 'data' => $logTransaction,
    //                 // "application" => $application->id,
    //             ];

    //             Http::post($application->link_url, $params);

    //             activity()
    //                 ->performedOn($application)
    //                 ->withProperties($params)
    //                 ->log('Error process face record to application ' . $application->name);
    //         })
    //         ->then(function (Batch $batch) use ($application) {
    //             Log::info('Batch ID ZKBIO ' . $batch->id);
                
    //             $logTransaction = Log_transaction::where('batch_id', $batch->id)
    //                 ->select(
    //                     "credential_number",
    //                     "name",
    //                     "company",
    //                     "nvr_id",
    //                     "face_list_id",
    //                     "service_code",
    //                     "service_message",
    //                     "batch_id"
    //                 )
    //                 ->where('application_id',$application->id)
    //                 ->where('type', "ZKBIO")
    //                 ->get();
    //             $params = [
    //                 'message' => 'success',
    //                 'batch_id' => $batch->id,
    //                 'status' => 'completed',
    //                 'name' => $batch->name,
    //                 "data" => $logTransaction,
    //                 // "application" => $application->id,
    //             ];
    //             Http::post($application->link_url, $params);

    //             activity()
    //                 ->performedOn($application)
    //                 ->withProperties($params)
    //                 ->log('Success process face record to application ' . $application->name);
    //         })
    //         ->name('Upload Zkbio Visitor' . date('Y-m-d H:i:s'))
    //         ->onQueue('ZkbioVisitorApiCheckInFix')
    //         ->allowFailures()
    //         ->dispatch();

    //     foreach ($chunks as $key => $chunk) {
    //         $batch->add(new ProcessUploadVisitorZkbioModule(row: $chunk, batchId: $batch->id, application: $application));
    //     }

    //     return [
    //         [
    //             'label' => 'ZKBIO',
    //             'batch_id' => $batch->id
    //         ]
    //     ];
    // }

    /* Huawei Services */
    public function processQueueHuawei($data, Collection $nvr, Applications $application): array
    {
        $batchId = [];
        foreach ($nvr as $item) {
            $nvrName = str_replace(' ', '', $item->name);
            $collection = collect($data);
            $chunks = $collection->chunk(100);
            $batchQueue = [];
            $batch = Bus::batch([])
                ->catch(function (Batch $batch, Throwable $e) use ($application, $nvrName) {
                    Log::info('Error upload to huawei ' . $nvrName . ' time: ' . now(), [
                        'error' => $e->getTraceAsString(),
                    ]);

                    
                    $logTransaction = Log_transaction::where('batch_id', $batch->id)
                        ->select(
                            "credential_number",
                            "name",
                            "company",
                            "access_level_code",
                            "access_level_name",
                            "service_code",
                            "service_message",
                            "batch_id"
                        )
                        ->where("service_code", "<>", "0")
                        ->where('application_id', $application->id)
                        ->where('type', "HUAWEI")
                        ->get();

                    $params = [
                        'message' => $e->getMessage(),
                        'batch_id' => $batch->id,
                        'status' => 'error',
                        'name' => $batch->name,
                        'data' => $logTransaction,
                    ];
                    Http::post($application->link_url, $params);

                    activity()
                        ->performedOn($application)
                        ->withProperties($params)
                        ->log('Error process person to application ' . $application->name);
                })
                ->then(function (Batch $batch) use ($application) {
                    Log::info('Batch ID Huawei ' . $batch->id);
                    $logTransaction = Log_transaction::where('batch_id', $batch->id)
                        ->select(
                            "credential_number",
                            "name",
                            "company",
                            "access_level_code",
                            "access_level_name",
                            "service_code",
                            "service_message",
                            "batch_id"
                        )
                        ->where('application_id', $application->id)
                        ->where('type', "HUAWEI")
                        ->get();

                    $params = [
                        'message' => 'success',
                        'batch_id' => $batch->id,
                        'status' => 'completed',
                        'name' => $batch->name,
                        "data" => $logTransaction,
                    ];
                    Http::post($application->link_url, $params);

                    activity()
                        ->performedOn($application)
                        ->withProperties($params)
                        ->log('Success process person to application ' . $application->name);
                })
                ->name('Upload Face Record ' . $nvrName . date('Y-m-d H:i:s'))
                ->onQueue(queue: $nvrName.'API-VISITOR')
                ->allowFailures()
                ->dispatch();

            foreach ($chunks as $key => $chunk) {
                $batch->add(new ProcessUploadVisitorHuaweiModule($chunk, $item->id, $batch->id));
            }

            $batchId[] = [
                'label' => $nvrName,
                'batch_id' => $batch->id
            ];
        }
        return $batchId;
    }

}