<?php

namespace App\Http\Middleware;

use App\Models\Applications;
use App\Traits\ApiResponse;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ExternalApplicationMiddleware
{
    use ApiResponse;
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $application = Applications::where('token', $request->bearerToken())
            ->where('status', '1')
            ->first();

        if (!$application) {
            return $this->error('Token credentials not match!', [], '422');
        }
        return $next($request);
    }
}
