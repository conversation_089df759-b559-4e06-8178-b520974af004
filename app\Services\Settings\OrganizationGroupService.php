<?php

namespace App\Services\Settings;

use App\Models\Settings\OrganizationGroup;
use App\Traits\ApiResponse;

class OrganizationGroupService
{
    use ApiResponse;

    /**
     * @param $request
     *
     * @return array
     */
    public function index($request): array
    {
        $options = json_decode($request->options, true);
        $pages = isset($options['page']) ? (int) ($options['page'] + 1) : 1;
        $row_data = isset($options['rows']) ? (int) $options['rows'] : 20;
        $sorts = isset($options['sortField']) ? (string) $options['sortField'] : "name";
        $order = (!empty($options['sortOrder'])) ? 'desc' : 'asc';
        $search = isset($request->search) ? (string) $request->search : "";
        $filters = $options['filters'] ?? null;

        $offset = ($pages - 1) * $row_data;

        $result = array();
        $query = OrganizationGroup::when($filters, function ($query, $filters) {
            if (isset($filters['company']['value'])) {
                $query->where('company', 'LIKE', '%' . $filters['company']['value'] . '%');
            }
            if (isset($filters['name']['value'])) {
                $query->where('name', 'LIKE', '%' . $filters['name']['value'] . '%');
            }
        });

        $result["total"] = $query->count();

        $result['data'] = $query
            ->orderBy($sorts, $order)
            ->get()
            ->map(function ($item) {
                $item->value = (string) $item->id;
                return $item;
            });

        $result['form'] = $this->getForm();
        $result['default'] = [
            [
                'id' => null,
                'name' => null,
                'description' => null,
            ]
        ];
        $result['colHeaders'] = ['ID', 'Name', 'Description'];
        $result['columns'] = $this->frontendColumns();

        $collect = collect($result);

        return $collect->all();
    }

    public function frontendColumns()
    {
        return [
            [
                'data' => 'id',
                'width' => 90,
                'wordWrap' => false
            ],
            [
                'data' => 'name',
                'width' => 150,
                'wordWrap' => false
            ],
            [
                'data' => 'description',
                'width' => 150,
                'wordWrap' => false
            ],
        ];
    }

    /**
     * @return array
     */
    public function getForm(): array
    {
        $form = $this->form('roles');
        $form['id'] = 0;

        return $form;
    }

    public function create($data)
    {
        return OrganizationGroup::create($data);
    }

    public function update($id, $data)
    {
        return OrganizationGroup::where('id', $id)->update($data);
    }

    /**
     * @param $request
     * @param $type
     *
     * @return array
     */
    public function formData($value): array
    {
        $data = [
            'name' => $value['name'],
            'description' => $value['description'],
            'created_by' => auth()->user()->id,
        ];
        return $data;
    }
}
