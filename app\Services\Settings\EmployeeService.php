<?php

namespace App\Services\Settings;

use Illuminate\Support\Facades\DB;
use App\Models\Settings\Organization;


class EmployeeService
{
    /**
     * @param $request
     *
     * @return array
     */
    public function employee($request): array
    {

        $checkComp = Organization::where('name', $request->company)->first();
        if (!$checkComp) {
            return [
                'status' => false,
                'message' => 'Company not found'
            ];
        }else{
            if($checkComp->group_id == 3){ // IMIP
                $dataCherry = $this->cherryView($request);
                return $dataCherry;
            }
            elseif($checkComp->group_id != 8){ // != CONTRACTOR
                $dataHris = $this->hrisView($request);
                return $dataHris;
            }
            else{
                return NULL;
            }
        }
    }

    public function cherryView($request): array
    {
        $responseCherry = DB::connection('sqlsrvcherry')->select("
                SELECT
                    CONCAT(A.nik, '.jpg') AS photo,
                    A.Name AS 'name',
                    CAST(<PERSON>.<PERSON> AS NVARCHAR) AS 'identifyNumber',
                    --
                    'ID Card' AS 'type',
                    CASE 
                        WHEN A.Gender = 'Laki-Laki' THEN 'MALE'
                        ELSE 'FEMALE' 
                    END AS 'gender',
                    A.BirthDate AS 'birthDate',
                    'Indonesia' AS 'nationality',
                    'Karyawan' AS 'employeeType',
                    SUBSTRING(A.Company, 4, LEN(A.Company) - 3) +' - ' + A.Department AS description,
                    CASE 
                        WHEN A.Company = 'PT IMIP' THEN 'PT. Indonesia Morowali Industrial Park' 
                        WHEN A.Company = 'PT BDT' THEN 'PT. Bintang Delapan Terminal' 
                        WHEN A.Company = 'PT MMM' THEN 'PT. MMM' 
                        WHEN A.Company = 'PT KBS' THEN 'PT. KENCANA BUMI SAKTI' 
                    WHEN A.Company = 'PT BDM' THEN 'PT. BINTANG DELAPAN MINERAL'
                    WHEN A.Company = 'PT MPSK' THEN 'PT. META PILAR SURYA KONSULTINDO'
                        ELSE 'PT. Indonesia Morowali Industrial Park' 
                    END AS company,
                    CASE 
                        WHEN A.WorkLocation LIKE '%JAKARTA%' THEN 'JAKARTA'
                        ELSE 'MOROWALI'
                    END AS 'workLocation',
                    A.Department AS 'departmentName',
                    CASE 
                        WHEN A.IsActive = 'TRUE' THEN 3
                        ELSE 2
                    END AS statusEmployee
                    
                FROM
                    vw_employee_masterdata A
                WHERE
                    A.IsActive = 'TRUE'
                    AND A.Nik = '$request->credential_number'
        ");

        return $responseCherry ? json_decode(json_encode($responseCherry[0]), true) : [];
    }


    public function hrisView($request): array
    {
        $responseHris = DB::connection('mysqlhris')
            ->table('vw_access_control')
            ->selectRaw("
                photoName as photo,
                Name AS name,
                idNumber AS identifyNumber,
                idCardType AS type,
                gender AS gender,
                dateOfBirth AS 'birthDate',
                nationality AS nationality,
                occupation AS employeeType,
                description AS description,
                company AS company,
                worklocation AS 'workLocation',
                departementName AS departmentName,
                CASE 
                        WHEN sts_karyawan = 1 THEN 3
                        ELSE 2
                    END AS statusEmployee

            ")
            ->where('idNumber', $request->credential_number)
            ->first();
        
            return $responseHris ? json_decode(json_encode($responseHris), true) : [];
    }
}