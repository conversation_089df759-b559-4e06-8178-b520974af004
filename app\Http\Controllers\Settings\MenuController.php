<?php

namespace App\Http\Controllers\Settings;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class MenuController extends Controller
{
    public function index(Request $request)
    {
        $permissions = $request->user()
            ->getAllPermissions()
            ->where('parent_id', '=', '0')
            ->whereIn('app_name', [$request->appName, 'All']);

        $array = [];
        foreach ($permissions as $permission) {
            $children = $request->user()
                ->getAllPermissions()
                ->where('parent_id', '=', $permission->id)
                ->whereIn('app_name', [$request->appName, 'All']);

            $array_child = [];
            $prev_name = '';
            foreach ($children as $child) {
                if ($prev_name != $child->menu_name) {
                    if (Str::contains($child->name, 'index')) {
                        $array_child[] = [
                            'menu' => $child->menu_name,
                            'icon' => $child->icon,
                            'route_name' => $child->route_name,
                        ];

                        $prev_name = $child->menu_name;
                    }
                }
            }

            $array[] = [
                'menu' => $permission->menu_name,
                'icon' => $permission->icon,
                'route_name' => $permission->route_name,
                'children' => $array_child
            ];
        }
        return $this->success('', [
            'menus' => $array,
            'app_version' => app()->version()
        ]);
    }
}
