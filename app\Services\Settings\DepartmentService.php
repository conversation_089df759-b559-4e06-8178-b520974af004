<?php

namespace App\Services\Settings;

use App\Traits\ApiResponse;
use Illuminate\Support\Arr;
use App\Models\Settings\Setting;
use App\Models\Settings\AccessType;
use App\Models\Settings\Department;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use App\Models\Settings\Organization;
use App\Models\Settings\OrganizationType;
use Illuminate\Support\Facades\Validator;
use App\Helpers\GenerateInitials;

class DepartmentService
{
    use ApiResponse;

    /**
     * @param $request
     *
     * @return array
     */
    public function index($request): array
    {
        $options = json_decode($request->options, true);
        $pages = isset($options['page']) ? (int)($options['page'] + 1) : 1;
        $row_data = isset($options['rows']) ? (int)$options['rows'] : 20;
        $sorts = isset($options['sortField']) ? (string)$options['sortField'] : "dpt_name";
        $order = (!empty($options['sortOrder'])) ? 'desc' : 'asc';
        $search = isset($request->search) ? (string)$request->search : "";
        $filters = $options['filters'] ?? null;
        $filter = json_decode($request->get('filters'));
        $offset = ($pages - 1) * $row_data;

        $result = array();
        $query = Department::where(function ($q) use ($filter) {    
            if ($filter != '') {
                foreach ($filter as $tags) {
                    if ($tags != null) {   
                        $q->where( $tags->id, 'LIKE', '%' . $tags->value . '%');
                    }
                }
            }
        })->with(['organization']);
    

        $result["total"] = $query->count();

        $result['data'] = $query
            ->orderBy($sorts, $order)
            ->skip($request->get('start'))
            ->take($request->get('size'))
            ->limit(20)
            ->get();

        $result['form'] = $this->getForm();

        $result['header'] = $this->frontEndHeader();

        $collect = collect($result);

        return $collect->all();
    }

     /**
     * @return array
     */
    public function getForm(): array
    {
        $form = $this->form('roles');
        $form['id'] = 0;

        return $form;
    }

      /**
     * @param $request
     * @param $type
     *
     * @return array
     */
    public function formData($request, $type): array
    {
        $data     = $request->all();
        $data_req = $request->all();
        $org_id   = $data['organization_id'];
        $work     = $data['dpt_worklocation'];
        $dept     = $data['dpt_name'];
        $acc_type = $data['dpt_type'];
        
        Arr::forget($data, 'id');
        Arr::forget($data, 'dpt_name');
        Arr::forget($data, 'created_at');
        Arr::forget($data, 'updated_at');
        Arr::forget($data, 'ACTIONS');

        $org      = Organization::where('id', $org_id)->first();
        $acc      = AccessType::where('id', $acc_type)->first();
        $org_type = OrganizationType::where('id', $org->organization_type_id)->first();

        $level1Name = $work;
        $level1Code = GenerateInitials::generateInitials($work,4);

        if($org->organization_group == 1){
            $level2Name = $org->alias;
            $level2Code = GenerateInitials::generateInitials($org_type->name.' '.$work.' '.$org->alias,2);
           
            $level3Name = $dept;
            $level3Code = GenerateInitials::generateInitials($org_type->name.' '.$work.' '.$org->alias.' '.$dept,2).crc32($dept);
        }else{
            $level2Name = $org_type->name;
            $level2Code = GenerateInitials::generateInitials($org_type->name.' '.$work.' '.$acc->name,2);
           
            $level3Name = substr($org_type->name, 0, 1).'-'.$org->alias;
            $level3Code = GenerateInitials::generateInitials($org_type->name.' '.$work.' '.$acc->name.' '.$org->alias,2).crc32($org->alias);
        }

        $data['created_by']      = $request->user()->id;
        $data['dpt_name']        = $level3Name;
        $data['dpt_code']        = $level3Code;
        $data['dpt_parent_code'] = $level2Code;

        foreach ($data as $key => $value) {
            if (strpos($key, '/api/') === 0) {
                unset($data[$key]);
            }
        }

        return $data;
    }

    public function processCreateDept($request)
    {
        $data = $request->all();
        $org_id   = $data['organization_id'];
        $work     = $data['dpt_worklocation'];
        $dept     = str_replace(array( '\'', '"','&','/',',' , ';', '<', '>' ), '', $data['dpt_name']);
        $acc_type = $data['dpt_type'];

        $org      = Organization::where('id', $org_id)->first();
        $acc      = AccessType::where('id', $acc_type)->first();
        $org_type = OrganizationType::where('id', $org->organization_type_id)->first();
       
        $level1Name = $work;
        $level1Code = GenerateInitials::generateInitials($work,4).crc32($work);

        if($org->organization_group == 1){
            $level2Name = $org->alias;
            $level2Code = GenerateInitials::generateInitials($org_type->name.' '.$work.' '.$org->alias,2).crc32($org->alias);
           
            $level3Name = $dept;
            $level3Code = GenerateInitials::generateInitials($org_type->name.' '.$work.' '.$org->alias.' '.$dept,2).crc32($dept);
        }else{
            $level2Name = $acc->name;
            $level2Code = GenerateInitials::generateInitials($work.' '.$acc->name,2).crc32($acc->name);
           
            $level3Name = substr($org_type->name, 0, 1).'-'.$org->alias;
            $level3Code = GenerateInitials::generateInitials($work.' '.$acc->name.' '.$org->alias,2).crc32($org->alias);
        }

        $urlAddDepartment = Setting::getSetting('UrlAddDepartmentZkbio');
        $accessToken      = Setting::getSetting('AccessTokenZkbio');
     
        Http::post($urlAddDepartment.'?access_token='.$accessToken, [
            'code'       => $level1Code,
            'name'       => $level1Name,
            'parentCode' => 0,
            'sortNo'     => 999,
        ]);
        Http::post($urlAddDepartment.'?access_token='.$accessToken, [
            'code'       => $level2Code,
            'name'       => $level2Name,
            'parentCode' => $level1Code,
            'sortNo'     => 999,
        ]);
        Http::post($urlAddDepartment.'?access_token='.$accessToken, [
            'code'       => $level3Code,
            'name'       => $level3Name,
            'parentCode' => $level2Code,
            'sortNo'     => 999,
        ]);
    }

    public function processSyncDept($request){

        $work               = $request['workLocation'];
        $company            = $request['company'];
        $dept               = str_replace(array( '\'', '"','&','/',',' , ';', '<', '>' ), '', $request['dept']);
        $organization_group = $request['organization_group']; // 1
        $compType           = $request['compType']; // Internal
        $accessType         = $request['accessType']; // General

        $level1Name = $work;
        $level1Code = GenerateInitials::generateInitials($work,4).crc32($work);

        if($organization_group == 1){
            $level2Name = $company;
            $level2Code = GenerateInitials::generateInitials($compType .' '.$work.' '.$company,2).crc32($company);
           
            $level3Name = $dept;
            $level3Code = GenerateInitials::generateInitials($compType .' '.$work.' '.$company.' '.$dept,2).crc32($dept);
        }else{
            $level2Name = $accessType;
            $level2Code = GenerateInitials::generateInitials($work.' '.$accessType,2).crc32($accessType);
           
            $level3Name = substr($compType , 0, 1).'-'.$company;
            $level3Code = GenerateInitials::generateInitials($work.' '.$accessType.' '.$company,2).crc32($company);
        }

        // Log::info("Check Kode :".$company.' - '.$level3Code.' - '.$level3Name);

        $org      = Organization::where('alias', $company)->first();
        $acc      = AccessType::where('name', $accessType)->first();
        $org_type = OrganizationType::where('name', $compType)->first();
        $Department = Department::where('dpt_code', $level3Code)->first();

        $urlAddDepartment = Setting::getSetting('UrlAddDepartmentZkbio');
        $accessToken      = Setting::getSetting('AccessTokenZkbio');

        if(!isset($Department)){
            Department::create([
                'dpt_name'         => $level3Name,
                'dpt_code'         => $level3Code,
                'dpt_parent_code'  => $level2Code,
                'dpt_sort_no'      => 999,
                'dpt_type'         => $org_type->id,
                'dpt_worklocation' => $request['workLocation'],
                'organization_id'  => (isset($org->id)) ? $org->id : '',
                'created_by'       => Auth::user()->id,
                'status'           => 1,
            ]);
        }
     
        Http::post($urlAddDepartment.'?access_token='.$accessToken, [
            'code'       => $level1Code,
            'name'       => $level1Name,
            'parentCode' => 0,
            'sortNo'     => 999,
        ]);
        Http::post($urlAddDepartment.'?access_token='.$accessToken, [
            'code'       => $level2Code,
            'name'       => $level2Name,
            'parentCode' => $level1Code,
            'sortNo'     => 999,
        ]);
        Http::post($urlAddDepartment.'?access_token='.$accessToken, [
            'code'       => $level3Code,
            'name'       => $level3Name,
            'parentCode' => $level2Code,
            'sortNo'     => 999,
        ]);
    }

    public function frontEndHeader()
    {
        return [
            [
                "accessorKey" => 'dpt_name',
                "header" => 'Department Name',
            ],
            [
                "accessorKey" => 'dpt_code',
                "header" => 'Department Code',
            ],
            [
                "accessorKey" => 'dpt_parent_code',
                "header" => 'Parent Code',
            ],
            [
                "accessorKey" => 'dpt_worklocation',
                "header" => 'Work Location',
            ],
            [
                "accessorKey" => 'organization.alias',
                "header" => 'Organization',
            ],
            [
                "accessorKey" => 'status',
                "header" => 'Status',
            ],
        ];
    }
}