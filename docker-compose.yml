version: "3"
services:
    # PHP
    app:
        build:
            context: .
            dockerfile: Dockerfile
        container_name: access-control-api
        restart: always
        tty: true
        ports:
            - 4004:4004
        environment:
            PHP_OPCACHE_ENABLE: 1
            PRODUCTION: "1"
            NGINX_PORT: 4004
        volumes:
            - ./:/opt/laravel
            - vendor-data:/opt/laravel/vendor
            - storage-data:/opt/laravel/storage
        networks:
            - imip-app
        extra_hosts:
            - "auth-dev.corp.imip.co.id:${DOMAIN_HOST:-127.0.0.1}"
            - "auth.imip.co.id:${DOMAIN_HOST:-**********}"
            - "websocket.imip.co.id:${DOMAIN_HOST:-**********}"

# Networks
networks:
    imip-app:
        driver: bridge

# Volumes
volumes:
    vendor-data:
    storage-data:
