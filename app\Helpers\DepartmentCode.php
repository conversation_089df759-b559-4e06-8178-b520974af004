<?php

namespace App\Helpers;

use App\Helpers\GenerateInitials;

class DepartmentCode
{

    public static function deptCode($data, $level)
    {
        $work = $data['workLocation'];
        $company = $data['company'];
        $dept = str_replace(array('\'', '"', '&', '/', ',', ';', '<', '>'), '', $data['dept']);
        $organization_group = $data['organization_group']; // 1
        $compType = $data['compType']; // Internal
        $accessType = $data['accessType']; // General

        $level1Name = $work;
        $level1Code = GenerateInitials::generateInitials($work, 4).crc32($work);

        if ($organization_group == 1) {
            $level2Name = $company;
            $level2Code = GenerateInitials::generateInitials($compType . ' ' . $work . ' ' . $company, 2).crc32($company);

            $level3Name = $dept;
            $level3Code = GenerateInitials::generateInitials($compType . ' ' . $work . ' ' . $company . ' ' . $dept, 2).crc32($dept);
        } else {
            $level2Name = $accessType;
            $level2Code = GenerateInitials::generateInitials($work . ' ' . $accessType, 2).crc32($accessType);

            $level3Name = substr($compType, 0, 1) . '-' . $company;
            $level3Code = GenerateInitials::generateInitials($work . ' ' . $accessType . ' ' . $company, 2).crc32($company);
        }

        if ($level == 1) {
            $data = array(
                'deptCode' => $level1Code,
                'deptName' => $level1Name
            );
        } elseif ($level == 2) {
            $data = array(
                'deptCode' => $level2Code,
                'deptName' => $level2Name
            );
        } elseif ($level == 3) {
            $data = array(
                'deptCode' => $level3Code,
                'deptName' => $level3Name
            );
        } else {
            $data = array(
                'deptCode' => 'uknown',
                'deptName' => 'uknown',
            );
        }
        return $data;
    }


    public static function deptCodeVehicle($data, $level)
    {
        $work = $data['workLocation'];
        $company = $data['company'];
        $dept = str_replace(array('\'', '"', '&', '/', ',', ';', '<', '>'), '', $data['dept']);
        $organization_group = $data['organization_group']; // 1
        $compType = $data['compType']; // Internal
        $accessType = $data['accessType']; // General

        $level1Name = $work;
        $level1Code = GenerateInitials::generateInitials($work, 4).crc32($work);

        if ($organization_group == 1) {
            $level2Name = $company;
            $level2Code = GenerateInitials::generateInitials($compType . ' ' . $work . ' ' . $company, 2).crc32($company);

            $level3Name = $dept;
            $level3Code = GenerateInitials::generateInitials($compType . ' ' . $work . ' ' . $company . ' ' . $dept, 2).crc32($dept);
        } else {
            $level2Name = $accessType;
            $level2Code = GenerateInitials::generateInitials($work . ' ' . $accessType, 2).crc32($accessType);

            $level3Name = substr($compType, 0, 1) . '-' . $company;
            $level3Code = GenerateInitials::generateInitials($work . ' ' . $accessType . ' ' . $company, 2).crc32($company);
        }

        if ($level == 1) {
            $data = array(
                'deptCode' => $level1Code,
                'deptName' => $level1Name
            );
        } elseif ($level == 2) {
            $data = array(
                'deptCode' => $level2Code,
                'deptName' => $level2Name
            );
        } elseif ($level == 3) {
            $data = array(
                'deptCode' => $level3Code,
                'deptName' => $level3Name
            );
        } else {
            $data = array(
                'deptCode' => 'uknown',
                'deptName' => 'uknown',
            );
        }
        return $data;
    }
}
