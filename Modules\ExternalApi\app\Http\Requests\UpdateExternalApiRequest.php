<?php

namespace Modules\ExternalApi\app\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateExternalApiRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'form' => 'required',
            'form.*.photo' => 'required',
            'form.*.name' => 'required',
            'form.*.identifyNumber' => 'required',
            'form.*.type' => 'required',
            'form.*.gender' => 'required',
            'form.*.birthDate' => 'required',
            'form.*.nationality' => 'required',
            'form.*.employeeType' => 'required',
            'form.*.description' => 'required',
            'form.*.company' => 'required',
            'form.*.departmentName' => 'required',
            'form.*.workLocation' => 'required',
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }
}
