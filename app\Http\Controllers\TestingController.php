<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Jobs\RemoveAttachment;
use Illuminate\Routing\Controller;
use Intervention\Image\Facades\Image;
use Illuminate\Support\Facades\Storage;

class TestingController extends Controller
{
    public function index(){
        $data = Storage::disk('ftp')->get('photo/********.jpg');
        // $base = base64_decode($data);
        $image_ftp = Image::make($data);

        // Storage::disk('public')->put('********.jpg', Storage::disk('ftp')->get('photo/********.jpg'));
        // // RemoveAttachment::dispatch(public_path('********.jpg'));
        // // unlink(public_path().'/********.jpg');
        // // return "<img width='100' src=".url('/********.jpg').">";
        // $image = Image::make(public_path().'/********.jpg')->resize(null, 500, function ($constraint) {
        //     $constraint->aspectRatio();
        // });

        return $image_ftp->response();
        // if($data){
        //     echo 'success';
        // }else{
        //     echo 'gagal';
        // }
        
        // Storage::put('/public/file.jpg', $data);
      
        // $image_ftp->save(public_path().'/********.jpg');
        // return $image_ftp->response();

        // $img = Image::make(public_path().'/********.jpg');
        // return "<img src=".$image->encode('data-url').">";
        
        // dd(preg_replace('#^data:image/\w+;base64,#i', '', $image->encode('data-url')));
        // var_dump($image->encode('data-url'));
        // return "<img src=".$image->encode('data-url').">";

        // dd(getimagesize(public_path().'/testing.jpg'));
        // dd($this->size_as_kb($img->filesize()));
       
    }

    public function size_as_kb($yoursize) {
        if($yoursize < 1024) {
          return "{$yoursize} bytes";
        } elseif($yoursize < 1048576) {
          $size_kb = round($yoursize/1024);
          return "{$size_kb} KB";
        } else {
          $size_mb = round($yoursize/1048576, 1);
          return "{$size_mb} MB";
        }
      }
}
