<?php

namespace App\Http\Middleware;

use App\Models\Settings\Nvr;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Symfony\Component\HttpFoundation\Response;

class AuthNvrApiMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // get detail nvr
        $getNvr = Nvr::select('ip', 'username', 'password', 'name')
            ->where('id', $request->nvr_id)->first();

        $nvrName = str_replace(' ', '_', $getNvr->name);
        $request->attributes->add(['DETAIL_NVR' => [
            'ip'   => $getNvr->ip,
            'name' => $nvrName
        ]]);

        // check login user
        $responseUser = Http::withoutVerifying()
            ->withOptions(['verify' => false])
            ->withHeaders([
                'Content-Type' => 'application/json',
                'Cookie' => 'JSESSIONID=' . cookie($nvrName),
            ])
            ->get($getNvr->ip . '/users/userid');
        $setResponseUserCode = $responseUser->json();

        // set login
        if ($setResponseUserCode['resultCode'] != 0) {
            $response = Http::withoutVerifying()
                ->withOptions(['verify' => false])
                ->post($getNvr->ip . '/loginInfo/login/v1.0', [
                    'userName' => $getNvr->username,
                    'password' => $getNvr->password,
                ]);
            $setResponseCode = $response->json();

            if ($setResponseCode['resultCode'] == 0) {
                $setCookiesNvr  = $response->cookies()->getCookieByName('JSESSIONID')->getValue();
                cookie($nvrName, $setCookiesNvr);
            }
        }

        return $next($request);
    }
}
