<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('log_transaction', function (Blueprint $table) {
            $table->string('service_code', 30)->nullable();
            $table->json('service_payload')->nullable();
            $table->json('service_message')->nullable();

            $table->index(['credential_type', 'type']);
            $table->index(['nvr_id', 'face_list_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('log_transaction', function (Blueprint $table) {
            //
        });
    }
};
