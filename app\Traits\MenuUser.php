<?php

namespace App\Traits;

use Illuminate\Support\Str;

trait MenuUser
{
    /**
     * @param $appName
     * @param $user
     *
     * @return array
     */
    public function menuUser($appName, $user): array
    {
        $permissions = $user
            ->getAllPermissions()
            ->where('parent_id', '=', '0')
            ->whereIn('app_name', [$appName, 'All'])
            ->sortBy('order_line');

        // $array = [];
        $array = [];
        foreach ($permissions as $permission) {
            $children = $user
                ->getAllPermissions()
                ->where('parent_id', '=', $permission->id)
                ->whereIn('app_name', [$appName, 'All'])
                ->sortBy('order_line');

            $array_child = [];
            $prev_name = '';
            foreach ($children as $child) {
                if ($prev_name != $child->menu_name) {
                    if (Str::contains($child->name, 'index')) {
                        $array_child[] = [
                            'label' => $child->menu_name,
                            'icon' => $child->icon,
                            'to' => $child->route_name,
                        ];

                        $prev_name = $child->menu_name;
                    }
                }
            }

            $array[] = [
                'label' => $permission->menu_name,
                'icon' => $permission->icon,
                'to' => $permission->route_name,
                'items' => $array_child
            ];
        }

        return $array;
    }
}
