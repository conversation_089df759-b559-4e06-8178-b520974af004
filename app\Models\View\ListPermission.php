<?php

namespace App\Models\View;

use App\Models\Settings\Permission;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

/**
 * App\Models\View\ListPermission
 *
 * @property-read mixed $id
 * @property-read mixed $role
 * @method static \Illuminate\Database\Eloquent\Builder|ListPermission newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ListPermission newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ListPermission query()
 * @mixin \Eloquent
 */
class ListPermission extends Model
{
    use HasFactory;

    protected $connection = 'sqlsrv';
    protected $table = 'list_permissions';

    protected $appends = [
        'role',
        'id'
    ];

    protected $casts = [
        'parent_id' => 'integer'
    ];

    public function getIdAttribute()
    {
        return Str::random(20);
    }

    public function getRoleAttribute()
    {
        $permissions = Permission::leftJoin('role_has_permissions', 'role_has_permissions.permission_id', 'permissions.id')
            ->leftJoin('roles', 'roles.id', 'role_has_permissions.role_id')
            ->where('permissions.menu_name', $this->menu_name)
            ->distinct()
            ->select(DB::raw('role_has_permissions.role_id AS role'), 'roles.name AS role_name')
            ->get();
        $role = [];
        foreach ($permissions as $permission) {
            $role[] = (int)$permission->role;
        }
        return $role;
    }
}
