[supervisord]
nodaemon=true
user=root
pidfile=/var/run/supervisor.pid
logfile = /var/log/supervisord.log

[program:nginx]
command=/usr/sbin/nginx -g "daemon off;"
autostart=true
autorestart=true
stderr_logfile=/dev/stderr
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes = 0
stderr_logfile_maxbytes = 0

[program:php-fpm]
command=/usr/local/sbin/php-fpm
autostart=true
autorestart=true
stderr_logfile=/dev/stderr
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes = 0
stderr_logfile_maxbytes = 0


[program:app-queue1]
command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan queue:work --tries=3 --max-jobs=2 --queue=High,Medium,Low,document,default --timeout=0
environment=LARAVEL_SAIL="1"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autostart=true
autorestart=true
user=root


[program:nvr1-queue]
command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan queue:work --tries=3 --max-jobs=2 --queue=NVR1DEV --timeout=0
environment=LARAVEL_SAIL="1"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autostart=true
autorestart=true
user=root
numprocs=25
process_name=%(program_name)s_%(process_num)s

[program:nvr2-queue]
command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan queue:work --tries=3 --max-jobs=2 --queue=NVR2 --timeout=0
environment=LARAVEL_SAIL="1"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autostart=true
autorestart=true
user=root
numprocs=25
process_name=%(program_name)s_%(process_num)s

[program:nvr3-queue]
command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan queue:work --tries=3 --max-jobs=2 --queue=NVR3 --timeout=0
environment=LARAVEL_SAIL="1"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autostart=true
autorestart=true
user=root
numprocs=25
process_name=%(program_name)s_%(process_num)s

[program:nvr4-queue]
command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan queue:work --tries=3 --max-jobs=2 --queue=NVR4 --timeout=0
environment=LARAVEL_SAIL="1"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autostart=true
autorestart=true
user=root
numprocs=25
process_name=%(program_name)s_%(process_num)s



[program:zkbiodev-queue26]
command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan queue:work --tries=3 --max-jobs=2 --queue=Zkbio --timeout=0
environment=LARAVEL_SAIL="1"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autostart=true
autorestart=true
user=root


[program:externalapi-zkbio]
command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan queue:work --tries=3 --max-jobs=2 --queue=ZkbioDevApi --timeout=0
environment=LARAVEL_SAIL="1"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autostart=true
autorestart=true
user=root

[program:externalapi-huawei1]
command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan queue:work --tries=3 --max-jobs=2 --queue=NVR1API --timeout=0
environment=LARAVEL_SAIL="1"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autostart=true
autorestart=true
user=root

[program:externalapi-huawei2]
command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan queue:work --tries=3 --max-jobs=2 --queue=NVR2API --timeout=0
environment=LARAVEL_SAIL="1"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autostart=true
autorestart=true
user=root

[program:externalapi-huawei3]
command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan queue:work --tries=3 --max-jobs=2 --queue=NVR3API --timeout=0
environment=LARAVEL_SAIL="1"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autostart=true
autorestart=true
user=root

[program:externalapi-huawei4]
command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan queue:work --tries=3 --max-jobs=2 --queue=NVR4API --timeout=0
environment=LARAVEL_SAIL="1"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autostart=true
autorestart=true
user=root

[program:zkbio-person-update]
command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan queue:work --tries=3 --max-jobs=2 --queue=ZkbioUpdatePerson --timeout=0
environment=LARAVEL_SAIL="1"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autostart=true
autorestart=true
user=root


[program:zkbio-person-updateapi-1]
command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan queue:work --tries=3 --max-jobs=2 --queue=NVR1APIDEV --timeout=0
environment=LARAVEL_SAIL="1"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autostart=true
autorestart=true
user=root


[program:zkbio-person-updateapi-2]
command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan queue:work --tries=3 --max-jobs=2 --queue=NVR2APIDEV --timeout=0
environment=LARAVEL_SAIL="1"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autostart=true
autorestart=true
user=root

[program:zkbio-person-updateapi-3]
command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan queue:work --tries=3 --max-jobs=2 --queue=NVR3APIDEV --timeout=0
environment=LARAVEL_SAIL="1"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autostart=true
autorestart=true
user=root

[program:zkbio-person-updateapi-4]
command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan queue:work --tries=3 --max-jobs=2 --queue=NVR4APIDEV --timeout=0
environment=LARAVEL_SAIL="1"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autostart=true
autorestart=true
user=root

[program:zkbio-person-updateapi-5]
command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan queue:work --tries=3 --max-jobs=2 --queue=ZkbioDevApiDev --timeout=0
environment=LARAVEL_SAIL="1"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autostart=true
autorestart=true
user=root

[program:zkbio-simper-store]
command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan queue:work --tries=3 --max-jobs=2 --queue=ZkbioSimperDev --timeout=0
environment=LARAVEL_SAIL="1"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autostart=true
autorestart=true
numprocs=2
process_name=%(program_name)s_%(process_num)s
user=root

[program:zkbio-simper-delete]
command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan queue:work --tries=3 --max-jobs=2 --queue=ZkbioSimperDevDelete --timeout=0
environment=LARAVEL_SAIL="1"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autostart=true
autorestart=true
numprocs=2
process_name=%(program_name)s_%(process_num)s
user=root

[program:zkbio-person-updateapi-6]
command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan queue:work --tries=3 --max-jobs=2 --queue=ZkbioApiMutation --timeout=0
environment=LARAVEL_SAIL="1"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autostart=true
autorestart=true
user=root

[program:externalapi-huawei1]
command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan queue:work --tries=3 --max-jobs=2 --queue=NVR1APItio --timeout=0
environment=LARAVEL_SAIL="1"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autostart=true
autorestart=true
user=root

[program:externalapi-huawei1]
command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan queue:work --tries=3 --max-jobs=2 --queue=NVR2APItio --timeout=0
environment=LARAVEL_SAIL="1"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autostart=true
autorestart=true
user=root

[program:externalapi-huawei1]
command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan queue:work --tries=3 --max-jobs=2 --queue=NVR3APItio --timeout=0
environment=LARAVEL_SAIL="1"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autostart=true
autorestart=true
user=root


[program:nvr1-queue-delete-1]
command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan queue:work --tries=1 --max-jobs=2 --queue=NVR1API-DELETE --timeout=0
environment=LARAVEL_SAIL="1"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autostart=true
autorestart=true
numprocs=10
process_name=%(program_name)s_%(process_num)s
user=root


[program:nvr1-queue-mutation]
command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan queue:work --tries=3 --max-jobs=2 --queue=NVR1API-MUTATION --timeout=0
environment=LARAVEL_SAIL="1"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autostart=true
autorestart=true
numprocs=10
process_name=%(program_name)s_%(process_num)s
user=root



[program:nvr2-queue-delete-1]
command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan queue:work --tries=1 --max-jobs=2 --queue=NVR2API-DELETE --timeout=0
environment=LARAVEL_SAIL="1"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autostart=true
autorestart=true
numprocs=10
process_name=%(program_name)s_%(process_num)s
user=root

[program:nvr2-queue-mutation]
command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan queue:work --tries=3 --max-jobs=2 --queue=NVR2API-MUTATION --timeout=0
environment=LARAVEL_SAIL="1"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autostart=true
autorestart=true
numprocs=10
process_name=%(program_name)s_%(process_num)s
user=root


[program:nvr3-queue-delete-1]
command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan queue:work --tries=1 --max-jobs=2 --queue=NVR3API-DELETE --timeout=0
environment=LARAVEL_SAIL="1"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autostart=true
autorestart=true
numprocs=10
process_name=%(program_name)s_%(process_num)s
user=root

[program:nvr3-queue-mutation]
command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan queue:work --tries=3 --max-jobs=2 --queue=NVR3API-MUTATION --timeout=0
environment=LARAVEL_SAIL="1"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autostart=true
numprocs=10
process_name=%(program_name)s_%(process_num)s
autorestart=true
user=root


[program:nvr4-queue-delete-1]
command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan queue:work --tries=1 --max-jobs=2 --queue=NVR4API-DELETE --timeout=0
environment=LARAVEL_SAIL="1"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autostart=true
autorestart=true
numprocs=10
process_name=%(program_name)s_%(process_num)s
user=root

[program:nvr4-queue-mutation]
command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan queue:work --tries=3 --max-jobs=2 --queue=NVR4API-MUTATION --timeout=0
environment=LARAVEL_SAIL="1"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autostart=true
autorestart=true
numprocs=10
process_name=%(program_name)s_%(process_num)s
user=root


[program:zkbio-queue-delete-1]
command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan queue:work --tries=3 --max-jobs=2 --queue=ZkbioDevApiDevDelete --timeout=0
environment=LARAVEL_SAIL="1"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autostart=true
autorestart=true
user=root

[program:zkbio-queue-delete-2]
command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan queue:work --tries=3 --max-jobs=2 --queue=ZkbioDevApiDevDelete --timeout=0
environment=LARAVEL_SAIL="1"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autostart=true
autorestart=true
user=root

[program:zkbio-queue-vehicle-1]
command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan queue:work --tries=3 --max-jobs=2 --queue=ZkbioVehicleApiDev --timeout=0
environment=LARAVEL_SAIL="1"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autostart=true
autorestart=true
user=root

[program:zkbio-queue-zkbiodelete-1]
command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan queue:work --tries=3 --max-jobs=2 --queue=ZkbioDeleteApiPrd --timeout=0
environment=LARAVEL_SAIL="1"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autostart=true
autorestart=true
user=root

[program:zkbio-queue-zkbioprdmutasi-1]
command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan queue:work --tries=3 --max-jobs=2 --queue=ZkbioPrdApiMutasi --timeout=0
environment=LARAVEL_SAIL="1"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autostart=true
autorestart=true
user=root

[program:zkbio-queue-zkbioupload-1]
command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan queue:work --tries=3 --max-jobs=2 --queue=ZkbioUploadApiPrd --timeout=0
environment=LARAVEL_SAIL="1"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autostart=true
autorestart=true
user=root

[program:zkbio-queue-simper-1]
command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan queue:work --tries=3 --max-jobs=2 --queue=ZkbioSimperDev --timeout=0
environment=LARAVEL_SAIL="1"
max-time=3600
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=root
numprocs=3
redirect_stderr=true
stdout_logfile=/dev/stdout
process_name=%(program_name)s_%(process_num)02d
stdout_logfile_maxbytes=0
stopwaitsecs=3600

# [program:zkbio-queue-visitor-1]
# command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan queue:work --tries=3 --max-jobs=2 --queue=ZkbioVisitorApiDev --timeout=0
# environment=LARAVEL_SAIL="1"
# stdout_logfile=/dev/stdout
# stdout_logfile_maxbytes=0
# stderr_logfile=/dev/stderr
# stderr_logfile_maxbytes=0
# autostart=true
# autorestart=true
# user=root

[program:zkbio-queue-delete-visitor-1]
command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan queue:work --tries=3 --max-jobs=2 --queue=ZkbioVisitorApiDelete --timeout=0
environment=LARAVEL_SAIL="1"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autostart=true
autorestart=true
user=root

[program:zkbio-queue-visitor-1]
command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan queue:work --tries=3 --max-jobs=2 --queue=ZkbioVisitorApiCheckInFix --timeout=0
environment=LARAVEL_SAIL="1"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autostart=true
autorestart=true
user=root

[program:zkbio-queue-visitor-2]
command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan queue:work --tries=3 --max-jobs=2 --queue=NVR1API-VISITOR --timeout=0
environment=LARAVEL_SAIL="1"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autostart=true
autorestart=true
user=root

[program:zkbio-queue-visitor-3]
command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan queue:work --tries=3 --max-jobs=2 --queue=NVR2API-VISITOR --timeout=0
environment=LARAVEL_SAIL="1"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autostart=true
autorestart=true
user=root

[program:zkbio-queue-visitor-4]
command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan queue:work --tries=3 --max-jobs=2 --queue=NVR3API-VISITOR --timeout=0
environment=LARAVEL_SAIL="1"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autostart=true
autorestart=true
user=root

[program:zkbio-queue-visitor-5]
command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan queue:work --tries=3 --max-jobs=2 --queue=NVR4API-VISITOR --timeout=0
environment=LARAVEL_SAIL="1"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autostart=true
autorestart=true
user=root


[program:zkbio-queue-mutation-1]
command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan queue:work --tries=3 --max-jobs=2 --queue=ZkbioDevApiDevMutation --timeout=0
environment=LARAVEL_SAIL="1"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autostart=true
autorestart=true
user=root

[program:zkbio-queue-change-1]
command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan queue:work --tries=3 --max-jobs=2 --queue=ZkbioChangePhoto --timeout=0
environment=LARAVEL_SAIL="1"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autostart=true
autorestart=true
user=root

[program:zkbio-queue-change-2]
command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan queue:work --tries=3 --max-jobs=2 --queue=NVR1CHANGEPHOTO --timeout=0
environment=LARAVEL_SAIL="1"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autostart=true
autorestart=true
user=root

[program:zkbio-queue-change-3]
command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan queue:work --tries=3 --max-jobs=2 --queue=NVR2CHANGEPHOTO --timeout=0
environment=LARAVEL_SAIL="1"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autostart=true
autorestart=true
user=root

[program:zkbio-queue-change-4]
command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan queue:work --tries=3 --max-jobs=2 --queue=NVR3CHANGEPHOTO --timeout=0
environment=LARAVEL_SAIL="1"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autostart=true
autorestart=true
user=root

[program:zkbio-queue-change-5]
command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan queue:work --tries=3 --max-jobs=2 --queue=NVR4CHANGEPHOTO --timeout=0
environment=LARAVEL_SAIL="1"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autostart=true
autorestart=true
user=root

# [program:laravel-schedule]
# command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan schedule:run --no-interaction
# environment=LARAVEL_SAIL="1"
# stdout_logfile=/dev/stdout
# stdout_logfile_maxbytes=0
# stderr_logfile=/dev/stderr
# stderr_logfile_maxbytes=0
# autostart=true
# autorestart=true
# user=root

[program:laravel-schedule]
command=sh -c "while true; do /usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan schedule:run --no-interaction; sleep 60; done"
environment=LARAVEL_SAIL="1"
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autostart=true
autorestart=true
user=root


# [program:zkbio-queue-mutation-1]
# command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan queue:work --tries=3 --max-jobs=2 --queue=NVR1APIDEVTEST --timeout=0
# environment=LARAVEL_SAIL="1"
# stdout_logfile=/dev/stdout
# stdout_logfile_maxbytes=0
# stderr_logfile=/dev/stderr
# stderr_logfile_maxbytes=0
# autostart=true
# autorestart=true
# user=root


# [program:zkbio-person-updateapi-5]
# command=/usr/local/bin/php -d variables_order=EGPCS /opt/laravel/artisan queue:work --tries=3 --max-jobs=2 --queue=ZkbioDevApiDevTest --timeout=0
# environment=LARAVEL_SAIL="1"
# stdout_logfile=/dev/stdout
# stdout_logfile_maxbytes=0
# stderr_logfile=/dev/stderr
# stderr_logfile_maxbytes=0
# autostart=true
# autorestart=true
# user=root
