<?php
namespace App\Http\Controllers\Upload;

use App\Http\Controllers\Controller;
use App\Jobs\RemoveAttachment;
use App\Jobs\Upload\ProcessUploadZkbio;
use App\Models\Settings\Nvr;
use App\Services\Upload\BatchUploadZkbioService;
use Maatwebsite\Excel\Facades\Excel;
use App\Imports\FaceRecordImport;
use Illuminate\Bus\Batch;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use App\Models\View\ViewAccessControl;
use Illuminate\Support\Facades\Http;
use Illuminate\Database\Eloquent\Collection;
use App\Models\Settings\Organization;
use Throwable;
use ZipArchive;
use DB;


use Intervention\Image\Facades\Image;

class BatchUploadZkbioController extends Controller
{
    public function __construct()
    {
    }

    public function index(Request $request)
    {
        $service                            = new BatchUploadZkbioService();
        try {
            return $this->success('', $service->index($request));
        } catch (\Exception $exception) {
            throw new \Exception($exception->getMessage(), 1);
        }
    }

    public function store(Request $request)
    {
        $files = $request->file("files");
        $accLevelIds = $request->accLevelIds;
        $accLevelNames = $request->accLevelNames;
        $collection = Excel::toArray(new FaceRecordImport, $files);
        $lvl = explode(",",$accLevelIds);
        // $user_id        = $request->user()->id;
        $user_id = auth()->user()->id;

        try{
            foreach ($collection[0] as $index => $row) {
                if ($index != 0) {
                    if($row[5] == '0000-00-00' || $row[5] == '00/00/0000' || $row[5] == '0000/00/00'){
                        $row[5] = '1970-01-01';
                    }
                    /* Emp Data */
                    $org = Organization::where('name', $row[9])->first();
                    if($org != NULL){
                        $org_group = $org->organization_group;
                        $org_type = $org->organization_type;
                        $org_alias = $org->alias;
                    }
                    
                    $data[] = [
                        'photoName' => $row[0],
                        'name' => preg_replace('/[^A-Za-z0-9 !@#$%^&*().]/u','', strip_tags($row[1])),
                        'credentialNumber' => $row[2],
                        'strId' => $row[2],
                        'credentialType' => ($row[3] == 'ID Card') ? 0 : 1,
                        'gender' => ($row[4] == 'MALE') ? 'M' : 'F',
                        'bornTime' => date("Y-m-d", strtotime($row[5])),
                        'country' => $row[6],
                        'occupation' => $row[7],
                        'description' => $row[8],
                        'company' => $row[9],
                        'work_location' => $row[10],
                        'dept_name' => $row[11],
                        'log_type' => $row[12],
                        'org_group' => $org_group,
                        'org_type' => $org_type,
                        'org_alias' => $org_alias,
                    ];
                }
            }
            
            if (count($data) <= 0) {
                return $this->error('data must greater than 0');
            }

            Log::info('Data Array Is', [
                'data' => $data
            ]);

            $collection = collect($data);
            $chunks = $collection->chunk(100);
            $batchQueue = [];
            $batch = Bus::batch([])
                ->catch(function (Batch $batch, Throwable $e) {
                    Log::info('error upload batch ' . now(), [
                        'error'             => $e->getTraceAsString(),
                    ]);
                })
                ->name('Upload Zkbio '. date('Y-m-d H:i:s'))
                ->onQueue('ZkbioUploadApiPrd')
                // ->onQueue('ZkbioDevApiPatar')
                ->allowFailures()
                ->dispatch();

            foreach ($chunks as $key => $chunk) {
                $batch->add(new ProcessUploadZkbio($chunk, $accLevelIds, $accLevelNames, $user_id));
            }
            return response()->json([
                'status'                    => true,
                'message'                   => 'Success',
            ], 200);
        
        }catch(\Exception $e){
            return $this->success('Upload failed', [
                'batch'                     => $e->getMessage()
            ]);
        }
    }

    public function destroy(Request $request)
    {
        $files          = $request->file("files");
        $accLevelIds    = $request->accLevelIds;
        $accLevelNames  = $request->accLevelNames;
        $collection     = Excel::toArray(new FaceRecordImport, $files);
        $lvl            = explode(",",$accLevelIds);
        $user_id = auth()->user()->id;

        try{
            foreach ($collection[0] as $index => $row) {
                if ($index != 0) {
                    if($row[5] == '0000-00-00' || $row[5] == '00/00/0000' || $row[5] == '0000/00/00'){
                        $row[5]             = '1970-01-01';
                    }
                    /* Emp Data */
                    $org                    = Organization::where('name', $row[9])->first();
                    if($org != NULL){
                        $org_group          = $org->organization_group;
                        $org_type           = $org->organization_type;
                        $org_alias          = $org->alias;
                    }
                    
                    $data[]                 = [
                        'photoName'         => $row[0],
                        'name'              => preg_replace('/[^A-Za-z0-9 !@#$%^&*().]/u','', strip_tags($row[1])),
                        'credentialNumber'  => $row[2],
                        'strId'             => $row[2],
                        'credentialType'    => ($row[3] == 'ID Card') ? 0 : 1,
                        'gender'            => ($row[4] == 'MALE') ? 'M' : 'F',
                        'bornTime'          => date("Y-m-d", strtotime($row[5])),
                        'country'           => $row[6],
                        'occupation'        => $row[7],
                        'description'       => $row[8],
                        'company'           => $row[9],
                        'work_location'     => $row[10],
                        'dept_name'         => $row[11],
                        'org_group'         => $org_group,
                        'org_type'          => $org_type,
                        'org_alias'         => $org_alias,
                        'log_type'          => $row[12],
                    ];
                }
            }
            
            if (count($data) <= 0) {
                return $this->error('data must greater than 0');
            }

            Log::info('Data Array Is', [
                'data' => $data
            ]);

            $collection                     = collect($data);
            $chunks                         = $collection->chunk(100);
            $batchQueue                     = [];
            $batch                          = Bus::batch([])
                ->catch(function (Batch $batch, Throwable $e) {
                    Log::info('error upload batch ' . now(), [
                        'error'             => $e->getTraceAsString(),
                    ]);
                })
                ->name('Upload Zkbio '. date('Y-m-d H:i:s'))
                ->onQueue('ZkbioUploadApiPrd')
                // ->onQueue('ZkbioDevApiPatar')
                ->allowFailures()
                ->dispatch();

            foreach ($chunks as $key => $chunk) {
                $batch->add(new ProcessUploadZkbio($chunk, $accLevelIds, $accLevelNames, $user_id));
            }
            return response()->json([
                'status'                    => true,
                'message'                   => 'Success',
            ], 200);
        
        }catch(\Exception $e){
            return $this->success('Upload failed', [
                'batch'                     => $e->getMessage()
            ]);
        }
    }
}
