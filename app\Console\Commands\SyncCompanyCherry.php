<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class SyncCompanyCherry extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-company-cherry';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        Log::info('Start Sync Company : '.date('Y-m-d H:i:s'));

        $organizationAliases = Organization::where('organization_type_id', 1)->pluck('alias')->toArray();

        $excludedAliases = array_merge(['IMIP', 'MSS', 'BDT', 'MMM', 'BMS'], $organizationAliases);
        
        $placeholders = implode(',', array_fill(0, count($excludedAliases), '?'));
        
        $responseCherry = DB::connection('sqlsrvcherry')->select("
            SELECT 
                'GENERAL' as accType,
                'INTERNAL' as compType,
                RIGHT(<PERSON><PERSON>WorkLocation, LEN(A.WorkLocation) - CHARINDEX(' ', A.WorkLocation)) as Work,
                A.WorkLocation,
                RIGHT(A.Company, LEN(A.Company) - 3) as Company,
                `group`,
                '' AS Fullname
            FROM vw_employee_masterdata A
            WHERE RIGHT(A.Company, LEN(A.Company) - 3) NOT IN ($placeholders)
            GROUP BY
                A.WorkLocation,
                A.Company
            ORDER BY A.Company, A.Department ASC
        ", $excludedAliases);
       
        foreach($responseCherry as $res){

            $request = array(
                'alias'                => $res->Company,
                'name'                 => $res->Fullname,
                'status'               => 1,
                'organization_type_id' => 1,
                'created_by'           => null,
                'organization_group'   => 2,
                'group_id'             => 3,
                'data_source'          => 'ftp',
            );
            
            Organization::create($request);
        }

        Log::info('Done Sync Company : '.date('Y-m-d H:i:s'));
    }
}
