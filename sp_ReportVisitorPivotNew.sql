USE [AC_PRD]
GO

/****** Object:  StoredProcedure [dbo].[sp_ReportVisitorPivotNew]    Script Date: 02/07/2025 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[sp_ReportVisitorPivotNew]
    @startDate DATE,
    @endDate DATE
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @cols NVARCHAR(MAX), @colsWithIsNull NVARCHAR(MAX), @colsSum NVARCHAR(MAX), @sql NVARCHAR(MAX);

    -- Generate dynamic columns based on date range from your specific query
    SELECT @cols = STRING_AGG(QUOTENAME(CONVERT(VARCHAR(10), created_at, 120)), ',') WITHIN GROUP (ORDER BY created_at),
           @colsWithIsNull = STRING_AGG('COALESCE(' + QUOTENAME(CONVERT(VARCHAR(10), created_at, 120)) + ', 0) AS ' + QUOTENAME(CONVERT(VARCHAR(10), created_at, 120)), ','),
           @colsSum = STRING_AGG('COALESCE(' + QUOTENAME(CONVERT(VARCHAR(10), created_at, 120)) + ', 0)', '+')
    FROM (
        SELECT DISTINCT CAST(A.created_at AS DATE) AS created_at
        FROM log_transaction A
        WHERE A.application_id = 24
        AND A.service_code = '0'
        AND A.status = 3
        AND A.type = 'Zkbio'
        AND CAST(A.created_at AS DATE) BETWEEN @startDate AND @endDate
    ) AS DateSequence;

    -- Build dynamic SQL for pivot table
    SET @sql = '
        WITH AllData AS (
            SELECT  
                A.company,
                CAST(A.created_at AS DATE) AS created_at,
                COUNT(*) AS Total
            FROM log_transaction A
            WHERE A.application_id = 24
            AND A.service_code = ''0''
            AND A.status = 3
            AND A.type = ''Zkbio''
            AND CAST(A.created_at AS DATE) BETWEEN ''' + CONVERT(VARCHAR(10), @startDate, 120) + ''' AND ''' + CONVERT(VARCHAR(10), @endDate, 120) + '''
            GROUP BY A.company, CAST(A.created_at AS DATE)
        ),
        PivotData AS (
            SELECT 
                company, 
                ' + @colsWithIsNull + ',
                TotalPerBulan = ' + @colsSum + '
            FROM 
                (SELECT company, created_at, Total FROM AllData) AS SourceTable
            PIVOT 
                (SUM(Total) FOR created_at IN (' + @cols + ')) AS PivotTable
        ),
        TotalData AS (
            SELECT 
                ' + @colsWithIsNull + ',
                TotalPerHari = ' + @colsSum + '
            FROM (
                SELECT 
                    CAST(A.created_at AS DATE) AS created_at,
                    COUNT(*) AS Total
                FROM log_transaction A
                WHERE A.application_id = 24
                AND A.service_code = ''0''
                AND A.status = 3
                AND A.type = ''Zkbio''
                AND CAST(A.created_at AS DATE) BETWEEN ''' + CONVERT(VARCHAR(10), @startDate, 120) + ''' AND ''' + CONVERT(VARCHAR(10), @endDate, 120) + '''
                GROUP BY CAST(A.created_at AS DATE)
            ) AS SourceTable
            PIVOT 
                (SUM(Total) FOR created_at IN (' + @cols + ')) AS PivotTable
        )
        SELECT 
            company,
            ' + @colsWithIsNull + ',
            TotalPerBulan
        FROM PivotData
        UNION ALL
        SELECT 
            ''TotalPerHari'' AS company,
            ' + @colsWithIsNull + ',
            TotalPerHari AS TotalPerBulan
        FROM TotalData
        ORDER BY 
            CASE WHEN company = ''TotalPerHari'' THEN 1 ELSE 0 END,
            company;';

    EXEC sp_executesql @sql;
END;
GO
