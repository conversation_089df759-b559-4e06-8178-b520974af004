<?php

namespace App\Services\Upload;

use App\Models\JobBatch;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

use App\Models\Settings\Nvr;
use App\Models\Settings\FaceList;
use App\Models\Settings\Log_transaction;
use Illuminate\Http\JsonResponse;
use App\Models\Settings\Organization;
use App\Models\Settings\Setting;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use App\Models\Settings\AccessLevel;


class BatchDeleteZkbioService
{

    public function index($request)
    {
        $options = json_decode($request->options, true);
        $pages = isset($request['start']) ? (int)($request['start'] + 1) : 1;
        $row_data = isset($request['size']) ? (int)$request['size'] : 10;
        $sorts = isset($options['sortField']) ? (string)$options['sortField'] : "created_at";
        $order = (!empty($options['sortOrder'])) ? 'desc' : 'desc';
        $search = isset($request->search) ? (string)$request->search : "";
        $filters = $options['filters'] ?? null;

        $offset = ($pages - 1) * $row_data;

        $result = array();
        $query = JobBatch::where('name', 'LIKE', '%upload zkbio%');

        $result["total"] = $query->count();

        $result['data'] = $query
            ->orderBy($sorts, $order)
            ->skip($request['start'])
            ->take($request['size'])
            ->get();

        $result['header'] = $this->frontEndHeader();
        $result['request'] = [
            'offset' => $offset,
            'row_data' => $row_data,
        ];

        $collect = collect($result);

        return $collect->all();
    }

    public function frontEndHeader()
    {
        return [
            [
                "accessorKey" => 'name',
                "header" => 'Process Name',
            ],
            [
                "accessorKey" => 'percentage',
                "header" => 'Percentage',
            ],
            [
                "accessorKey" => 'total_jobs',
                "header" => 'Total Jobs',
            ],
            [
                "accessorKey" => 'pending_jobs',
                "header" => 'Pending Jobs',
            ],
            [
                "accessorKey" => 'failed_jobs',
                "header" => 'Failed Jobs',
            ],
            [
                "accessorKey" => 'created_at',
                "header" => 'Created At',
            ],
            [
                "accessorKey" => 'finished_at',
                "header" => 'Finish At',
            ],
        ];
    }

    public function deletePerson($accessToken, $params, $company, $new_data_level, $user_id): void
    {
        Log::info('Data params deletePerson ' . now(), [
            'json' => $params,
        ]);
        /* getAccessLvlName */
        $accLevelNames                      = '';
        if(!empty($new_data_level)){
            $accLevelNames                  = $this->getAccessLvlName($new_data_level);
        }
        Log::info('Data level ' .$params['pin'], [
            'new_data_level' => $new_data_level,
            'accLevelNames' => $accLevelNames,
        ]);
        if ($params['is_leave'] == true) {
            $params['leaveType']                 = 2;
            $leaveDate                      = date("Y-m-d", strtotime($params['leaveDate']));
            $params['leaveDate']            = $leaveDate;
            unset($params['is_leave']);

            Log::info('Data params deletePerson2 ' . now(), [
                'json' => $params,
            ]);

            $urlLeavePerson                 = Setting::getSetting('UrlLeavePersonZkbio');
            $upload                         = Http::withoutVerifying()
                ->withOptions(['verify' => false])
                ->withHeaders([
                    'Content-Type' => 'application/json',
                ])
                ->post($urlLeavePerson . '?access_token=' . $accessToken, $params);
            $code_hasil                     = ($upload->collect()->has('code'))?$upload->collect()['code']:(($upload->collect()->has('ret'))?$upload->collect()['ret']:-1);
            $message                        = ($upload->collect()->has('message'))?$upload->collect()['message']:(($upload->collect()->has('msg'))?$upload->collect()['msg']:-1);

            Log::info('Result from update person zkbio', [
                'json' => $upload->json(),
                'url' => $urlLeavePerson
            ]);
        }else{
            unset($params['is_leave']);
            unset($params['leaveDate']);
            $urlDeletePerson = Setting::getSetting('UrlDeletePersonZkbio');
            $urlDeletePerson = $urlDeletePerson.'/'.$params['pin'].'';
            $code_hasil                     = '';
            $message                        = '';
            $upload                         = Http::withoutVerifying()
                ->withOptions(['verify' => false])
                ->withHeaders([
                    'Content-Type' => 'application/json',
            ])
            ->post($urlDeletePerson . '?access_token=' . $accessToken, $params);
            $code_hasil                     = ($upload->collect()->has('code'))?$upload->collect()['code']:(($upload->collect()->has('ret'))?$upload->collect()['ret']:-1);
            $message                        = ($upload->collect()->has('message'))?$upload->collect()['message']:(($upload->collect()->has('msg'))?$upload->collect()['msg']:-1);

            Log::info('result from delete person data zkbio', [
                'json' => $upload->json()
            ]);

            if($code_hasil != 0){
                $upload_again               = Http::withoutVerifying()
                    ->withOptions(['verify' => false])
                    ->withHeaders([
                        'Content-Type'      => 'application/json',
                ])
                ->post($urlDeletePerson . '?access_token=' . $accessToken, $params);
            }
        }
        /* insert to Log Transaction*/
        $params_log                         = array(
            'credential_number'             => $params['pin'],
            'credential_type'               => 0,
            'name'                          => $params['name'],
            'company'                       => strtoupper($company),
            'type'                          => 'ZKBIO',
            'access_level_code'             => $new_data_level,
            'access_level_name'             => $accLevelNames,
            'status'                        => $params['statusEmployee'],
            'application_id'                => null,
            'service_code'                  => $code_hasil,
            'service_payload'               => '',
            'service_message'               => $message,
            'nvr_id'                        => NULL,
            'face_list_id'                  => NULL,
            'log_type'                      => 'PERSON',
            'method'                        => 'DELETE',
            'created_by'                    => $user_id,
        );

        $save_log                           = Log_transaction::create($params_log);

    }

    public function getPerson($pid, $accessToken)
    {
        $urlGetPerson                       = Setting::getSetting('UrlGetPersonZkbio');
        return Http::withoutVerifying()
        ->withOptions(['verify' => false])
        ->withHeaders([
                'Content-Type' => 'application/json',
        ])
        ->get($urlGetPerson . $pid . '?access_token=' . $accessToken);
    }

    public function getAccessLvlName($data_level){
        $accLevel                       = array();
        $array = explode(",", $data_level);
        if(count($array) >= 1){
            foreach($array as $key => $value){
                $access = AccessLevel::where('access_level_code', $value)->first();
                if($access){
                    $accLevel[]         = $access->access_level_name;
                }
            }
        }
        return implode(",", $accLevel);
    }

    public function getAccessToken()
    {
        $accessToken                    = Setting::getSetting('AccessTokenZkbio');
        return $accessToken;
    }
    
}
