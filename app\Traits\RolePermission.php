<?php

namespace App\Traits;

use App\Events\Common\UserMenuUpdated;
use App\Models\Settings\Permission;
use App\Models\Settings\Role;
use App\Models\User;

trait RolePermission
{
    /**
     * @param $request
     * @param string $suffix
     * @param string $insert_role
     */
    protected function generatePermission($request, string $suffix = '-index', string $insert_role = 'N')
    {
        $data = [
            'name' => $request->name,
            'menu_name' => $request->menu_name,
            'parent_id' => $request->parent_id,
            'icon' => $request->icon,
            'route_name' => $request->route_name,
            'has_child' => $request->has_child,
            'has_route' => ($request->route_name != '') ? 'Y' : 'N',
            'order_line' => $request->order_line,
            'is_crud' => $request->is_crud,
            'guard_name' => $request->guard_name
        ];

        if ($request->is_crud == 'Y') {
            $suffix = ['index', 'store', 'edits', 'erase'];

            foreach ($suffix as $value) {
                $data['name'] = $request->name . '-' . $value;

                $check = Permission::where('name', '=', $request->menu_name . '-' . $value)->first();
                if ($check) {
                    $permission = Permission::where('id', '=', $check->id)->update($data);
                    $permission = Permission::find($check->id);
                } else {
                    $permission = Permission::create($data);
                }

                $this->assignPermissionToRole($permission, $insert_role, $request);
            }
        } else {
            $data['name'] = $request->name . $suffix;
            $check = Permission::where('name', '=', $request->menu_name . $suffix)->first();
            if ($check) {
                $permission = Permission::where('id', '=', $check->id)->update($data);
                $permission = Permission::find($check->id);
            } else {
                $permission = Permission::create($data);
            }

            $this->assignPermissionToRole($permission, $insert_role, $request);
        }
    }

    /**
     * @param $permission
     * @param $insert_role
     * @param $request
     */
    protected function assignPermissionToRole($permission, $insert_role, $request)
    {
        if ($insert_role == 'Y') {
            foreach ($request->role as $item) {
                $role = Role::where('id', '=', $item)->first();
                $permissions = Permission::where('id', '=', $permission->id)->first();

                $permissions->assignRole($role->name);

                $this->assignPermissionToUser($permission, $role);
            }
        }
    }

    /**
     * @param $permission
     * @param $role
     */
    protected function assignPermissionToUser($permission, $role)
    {
        $users = User::leftJoin('model_has_roles', 'model_has_roles.model_id', 'users.id')
            ->select('users.*')
            ->where('model_has_roles.role_id', '=', $role->id)
            ->get();

        if ($users) {
            foreach ($users as $user) {
                $user->givePermissionTo($permission->name);
                broadcast(new UserMenuUpdated('', $user->id));
            }
        }
    }

    /**
     * @param $role
     * @param $detail
     * @param $key
     */
    protected function actionStoreRolePermission($role, $detail, $key)
    {
        $permission = Permission::where('name', $detail['permission'] . '-' . $key)->first();

        if ($permission) {
            $users = User::leftJoin('model_has_roles', 'model_has_roles.model_id', 'users.id')
                ->select('users.*')
                ->where('model_has_roles.role_id', '=', $role->id)
                ->get();

            if ($detail[$key] == 'Y') {
                $role->givePermissionTo($detail['permission'] . '-' . $key);
                if ($users) {
                    foreach ($users as $user) {
                        $user->givePermissionTo($detail['permission'] . '-' . $key);
                        broadcast(new UserMenuUpdated('', $user->id));
                    }
                }
            } else {
                $role->revokePermissionTo($detail['permission'] . '-' . $key);
                if ($users) {
                    foreach ($users as $user) {
                        $user->revokePermissionTo($detail['permission'] . '-' . $key);
                        broadcast(new UserMenuUpdated('', $user->id));
                    }
                }
            }
        }
    }

    /**
     * @param $role
     * @param $detail
     * @param $key
     */
    protected function actionRemovePermission($role, $detail, $key)
    {
        $permission = Permission::where('name', $detail['permission'] . '-' . $key)->first();
        if ($permission) {
            $role->revokePermissionTo($detail['permission'] . '-' . $key);
        }
    }

    /**
     * @param $request
     * @param $role_name
     *
     * @return bool
     */
    protected function checkRole($request, $role_name)
    {
        $roles = $request->user()->roles;

        foreach ($roles as $role) {
            if ($role->name == $role_name) {
                return true;
            }
        }
        return false;
    }
}
