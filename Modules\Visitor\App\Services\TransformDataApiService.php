<?php

namespace Modules\Visitor\App\Services;

use App\Models\Applications;
use App\Models\Settings\AccessLevel;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;

class TransformDataApiService
{
    /**
     * @param string $service
     * @param array $form
     * @param string $accessType
     * @return array
     */
    public function transform(string $service, array $form, $application, $accessType): array
    {
        if ($service == 'Huawei') {
            return $this->transformHuawei($form, $application);
        } elseif ($service == 'HuaweiDelete') {
            return $this->transformHuaweiDelete($form, $application, $accessType);
        } elseif ($service == 'Zkbio') {
            return $this->transformZkbio($form, $application, $accessType);
        } elseif ($service == 'ZkbioDelete') {
            return $this->transformZkbioDelete($form, $application, $accessType);
        } else {
            return [];
        }
    }

    public function getAccessLevel($organizationName, $departmentName, $accessType, $identifyNumber)
    {
        $accessLevels = AccessLevel::whereHas('organizationAccessLevel.organization', function (Builder $query) use ($organizationName) {
            $query->where('name', 'LIKE', '%' . $organizationName . '%');
        })
            ->whereHas('accessType', function (Builder $query) use ($accessType) {
                $query->where('name', 'LIKE', '%' . $accessType . '%');
            })
            ->with(['accessType'])
            ->get();

        foreach ($accessLevels as $key => $accessLevel) {
            if ($accessLevel->department_name == 'ALL') {
                if ((int) $identifyNumber % 2 == 0 && Str::contains($accessLevel->access_level_name, 'GENAP')) {
                    return [
                        "access_level_code" => $accessLevel->access_level_code,
                        "access_level_name" => $accessLevel->access_level_name,
                        "access_type" => $accessLevel->accessType->name,
                    ];
                } else {
                    return [
                        "access_level_code" => $accessLevel->access_level_code,
                        "access_level_name" => $accessLevel->access_level_name,
                        "access_type" => $accessLevel->accessType->name,
                    ];
                }
            } else {
                $dept = explode(',', $accessLevel->department_name);
                if (Str::contains($departmentName, $dept) && (int) $identifyNumber % 2 == 0 && Str::contains($accessLevel->access_level_name, 'GENAP')) {
                    return [
                        "access_level_code" => $accessLevel->access_level_code,
                        "access_level_name" => $accessLevel->access_level_name,
                        "access_type" => $accessLevel->accessType->name,
                    ];
                } else {
                    return [
                        "access_level_code" => $accessLevel->access_level_code,
                        "access_level_name" => $accessLevel->access_level_name,
                        "access_type" => $accessLevel->accessType->name,
                    ];
                }
            }

        }
    }

    /* ------- Huawei ------- */
    protected function transformHuawei(array $form, Applications $application): array
    {
        $data = [];
        foreach ($form as $item) {
            $data[] = [
                'photoName' => $item['photoName'],
                'name' => preg_replace('/[^A-Za-z0-9 !@#$%^&*().]/u', '', strip_tags($item['visEmpName'])),
                'credentialNumber' => $item['identifyNumber'],
                'strId' => $item['identifyNumber'],
                'credentialType' => $item['certType'],
                'gender' => (Str::contains($item['gender'], ['MALE', 'Male'])) ? 0 : 1,
                'bornTime' => NULL,
                'country' => $item['nationality'],
                'occupation' => NULL,
                'description' => $item['description'],
                'company' => $item['company'],
                'department' => $item['departmentName'],
                'application_id' => $application->id,
                'statusEmployee' => 3,

            ];
        }

        return $data;
    }

    /* ------- Huawei Delete------- */
    protected function transformHuaweiDelete(array $form, Applications $application): array
    {
        $data = [];
        foreach ($form as $item) {
            $data[] = [
                'photoName' => $item['photoName'],
                'name' => preg_replace('/[^A-Za-z0-9 !@#$%^&*().]/u', '', strip_tags($item['visEmpName'])),
                'credentialNumber' => $item['identifyNumber'],
                'strId' => $item['identifyNumber'],
                'credentialType' => $item['certType'],
                'gender' => (Str::contains($item['gender'], ['MALE', 'Male'])) ? 0 : 1,
                'bornTime' => NULL,
                'country' => $item['nationality'],
                'occupation' => NULL,
                'description' => $item['description'],
                'company' => $item['company'],
                'department' => $item['departmentName'],
                'application_id' => $application->id,
                'statusEmployee' => 3,

            ];
        }

        return $data;
    }

    /* ------- Zkbio ------- */
    protected function transformZkbio(array $form, Applications $application, $accessType): array
    {
        $data = [];
        foreach ($form as $item) {

            $accessLevel = $this->getAccessLevel($item['company'], $item['departmentName'], $accessType, $item['persPersonPin']);

            if (isset($accessLevel)) {
                $access_type = $accessLevel['access_type'];
            } else {
                $access_type = '';
            }

            $data[] = [
                'persPersonPin' => $item['persPersonPin'],
                'certNum' => $item['certNum'],
                'certType' => $item['certType'],
                'company' => $item['company'],
                'visEmpName' => preg_replace('/[^A-Za-z0-9 !@#$%^&*().]/u', '', strip_tags($item['visEmpName'])),
                'visitEmpPhone' => $item['visitEmpPhone'],
                'visitReason' => $item['visitReason'],
                'visitorCount' => $item['visitorCount'],
                'cardNo' => $item['cardNo'],
                'startTime' => $item['startTime'],
                'endTime' => $item['endTime'],
                'accessMapping' => $item['accessMapping'],
                'application_id' => $application->id,
                'access_type' => $access_type,
                'departmentName' => $item['departmentName'],
                'photoName' => $item['photoName'],
            ];
        }

        return $data;
    }

    protected function transformZkbioDelete(array $form, Applications $application, $accessType): array
    {
        $data = [];
        foreach ($form as $item) {
            $data[] = [
                'certType' => $item['certType'],
                'visEmpName' => preg_replace('/[^A-Za-z0-9 !@#$%^&*().]/u', '', strip_tags($item['visEmpName'])),
                'certNum' => $item['certNum'],
                'company' => $item['company'],
                'persPersonPin' => $item['persPersonPin'],
                'access_level_name' => '',
                'access_level_code' => '',
                'application_id' => $application->id
            ];
        }

        return $data;
    }


}
