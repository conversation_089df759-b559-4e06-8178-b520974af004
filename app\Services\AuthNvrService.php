<?php

namespace App\Services;

use App\Models\Settings\Nvr;
use App\Traits\AppConfig;
use Illuminate\Support\Facades\Http;

class AuthNvrService
{
    use AppConfig;

    public function useAuth($nvrId)
    {
        $getNvr = Nvr::select('ip', 'username', 'password', 'name')
            ->where('id', $nvrId)
            ->first();

        $nvrName = $getNvr->name;

        $responseUser = Http::withoutVerifying()
            ->withOptions(['verify' => false])
            ->withHeaders([
                'Content-Type' => 'application/json',
                'Cookie' => 'JSESSIONID=' . $this->getConfig('Cookie-' . $nvrName, 'NVR'),
            ])
            ->get($getNvr->ip . '/users/userid');
        $setResponseUserCode = $responseUser->json();

        // $this->storeConfig('cookie test-' . $nvrName, 'NVR', 'test');
        // throw new \Exception(json_encode($setResponseUserCode));

        // set login
        if ($setResponseUserCode['resultCode'] != 0) {
            $response = Http::withoutVerifying()
                ->withOptions(['verify' => false])
                ->post($getNvr->ip . '/loginInfo/login/v1.0', [
                    'userName' => $getNvr->username,
                    'password' => $getNvr->password,
                ]);
            $setResponseCode = $response->json();

            if ($setResponseCode['resultCode'] == 0) {
                $this->storeConfig('Cookie-' . $nvrName, 'NVR', $response->cookies()->getCookieByName('JSESSIONID')->getValue());
                // throw new \Exception(json_encode($setResponseCode));
                // $setCookiesNvr  = $response->cookies()->getCookieByName('JSESSIONID')->getValue();
                // cookie($nvrName, $setCookiesNvr);
            }
        }
    }
}
