<?php

namespace App\Http\Controllers\Upload;

use Throwable;
use Illuminate\Bus\Batch;
use App\Models\Settings\Nvr;
use Illuminate\Http\Request;
use App\Imports\FaceRecordImport;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Maatwebsite\Excel\Facades\Excel;
use App\Models\View\ViewAccessControl;
use Illuminate\Database\Eloquent\Collection;
use App\Services\Upload\UploadFaceRecordService;
use App\Jobs\Upload\ProcessUploadFaceRecordUpdate;

class UploadFaceRecordController extends Controller
{
    public function index(Request $request)
    {
        $service = new UploadFaceRecordService();
        try {
            return $this->success('', $service->index($request));
        } catch (\Exception $exception) {
            throw new \Exception($exception->getMessage(), 1);
        }
    }

    public function storeBackup(Request $request)
    {
        $files = $request->file("files");
        $batchQueue = [];
        $nvr_id = $request->nvr_id;
        $user_id = auth()->user()->id;

        $employee = ViewAccessControl::orderBy('company')->chunk(200, function (Collection $employees) use ($nvr_id, $user_id) {
            $batchQueue[] = new ProcessUploadFaceRecordUpdate($employees, $nvr_id, $user_id);

            // $batch = Bus::batch($batchQueue)
            //     ->catch(function (Batch $batch, Throwable $e) {
            //         Log::info('error upload batch ' . now(), [
            //             'error' => $e->getTraceAsString(),
            //         ]);
            //     })
            //     ->name('Upload Face Record ' . date('Y-m-d H:i:s'))
            //     ->onQueue('Medium')
            //     ->allowFailures()
            //     ->dispatch();
        });

        return $this->success('Upload success', [
            'batchQueue' => $batchQueue,
        ]);
    }

    public function store(Request $request)
    {
        $files = $request->file("files");

        $nvr_id = $request->nvr_id;
        $user_id = auth()->user()->id;

        $collection = Excel::toArray(new FaceRecordImport, $files);
        $nvrLists = explode(',', $nvr_id);
        foreach ($nvrLists as $nvrList) {
            $nvr = Nvr::find($nvrList);
            $nvrName = str_replace(' ', '', $nvr->name);
            $data = [];
            $no_exist = [];
            foreach ($collection[0] as $index => $row) {
                if ($index != 0) {
                    $data[] = [
                        'photoName' => $row[0],
                        'name' => $row[1],
                        'credentialNumber' => $row[2],
                        'credentialType' => ($row[3] == 'ID Card') ? 0 : 1,
                        'gender' => ($row[4] == 'Male') ? 0 : 1,
                        'bornTime' => date("Y-m-d", strtotime($row[5])),
                        'country' => $row[6],
                        'occupation' => $row[7],
                        'description' => $row[8],
                        'strId' => $row[2],
                        'company' => $row[9],
                        'department' => $row[8],
                        'org_alias' => $row[11],
                        'log_type' => $row[12],
                    ];
                }
            }

            if (count($data) <= 0) {
                return $this->error('data must greater than 0');
            }

            $collection = collect($data);
            $chunks = $collection->chunk(100);
            $batchQueue = [];
            $batch = Bus::batch([])
                ->catch(function (Batch $batch, Throwable $e) {
                    Log::info('error upload batch ' . now(), [
                        'error' => $e->getTraceAsString(),
                    ]);
                })
                ->name('Upload Face Record '.$nvrName. date('Y-m-d H:i:s'))
                ->onQueue('NVR1DEV')
                ->allowFailures()
                ->dispatch();

            foreach ($chunks as $key => $chunk) {
                // $batchQueue[] = new ProcessUploadFaceRecordUpdate($chunk, $nvrList);
                $batch->add(new ProcessUploadFaceRecordUpdate($chunk, $nvrList, $user_id));
            }
        }
        return $this->success('Upload success', [
            'batch' => $batch,
            'data' => $data,
            'no_exist' => $no_exist,
        ]);
    }
}
