<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Services\Dashboard\SummaryVisitorService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class ReportVisitorController extends Controller
{
    protected $summaryVisitorService;

    public function __construct(SummaryVisitorService $summaryVisitorService)
    {
        $this->summaryVisitorService = $summaryVisitorService;
    }

    /**
     * Get visitor report by date range
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getReport(Request $request): JsonResponse
    {
        $request->validate([
            'start_date' => 'required|date|date_format:Y-m-d',
            'end_date' => 'required|date|date_format:Y-m-d|after_or_equal:start_date'
        ]);

        $result = $this->summaryVisitorService->getVisitorReportByDateRange(
            $request->start_date,
            $request->end_date
        );

        if ($result['success']) {
            return response()->json([
                'status' => true,
                'message' => 'Visitor report retrieved successfully',
                'data' => $result['data'],
                'meta' => [
                    'total_records' => $result['total_records'],
                    'date_range' => $result['date_range']
                ]
            ], 200);
        }

        return response()->json([
            'status' => false,
            'message' => $result['message'],
            'data' => []
        ], 500);
    }

    /**
     * Get current month visitor report
     *
     * @return JsonResponse
     */
    public function getCurrentMonth(): JsonResponse
    {
        $result = $this->summaryVisitorService->getCurrentMonthReport();

        if ($result['success']) {
            return response()->json([
                'status' => true,
                'message' => 'Current month visitor report retrieved successfully',
                'data' => $result['data'],
                'meta' => [
                    'total_records' => $result['total_records'],
                    'date_range' => $result['date_range']
                ]
            ], 200);
        }

        return response()->json([
            'status' => false,
            'message' => $result['message'],
            'data' => []
        ], 500);
    }

    /**
     * Get last month visitor report
     *
     * @return JsonResponse
     */
    public function getLastMonth(): JsonResponse
    {
        $result = $this->summaryVisitorService->getLastMonthReport();

        if ($result['success']) {
            return response()->json([
                'status' => true,
                'message' => 'Last month visitor report retrieved successfully',
                'data' => $result['data'],
                'meta' => [
                    'total_records' => $result['total_records'],
                    'date_range' => $result['date_range']
                ]
            ], 200);
        }

        return response()->json([
            'status' => false,
            'message' => $result['message'],
            'data' => []
        ], 500);
    }

    /**
     * Export visitor report
     *
     * @param Request $request
     * @return JsonResponse|\Illuminate\Http\Response
     */
    public function export(Request $request)
    {
        $request->validate([
            'start_date' => 'required|date|date_format:Y-m-d',
            'end_date' => 'required|date|date_format:Y-m-d|after_or_equal:start_date',
            'format' => 'sometimes|in:json,csv,array'
        ]);

        $format = $request->get('format', 'json');
        $data = $this->summaryVisitorService->exportVisitorReport(
            $request->start_date,
            $request->end_date,
            $format
        );

        if ($format === 'csv') {
            return response($data, 200)
                ->header('Content-Type', 'text/csv')
                ->header('Content-Disposition', 'attachment; filename="visitor_report_' . $request->start_date . '_to_' . $request->end_date . '.csv"');
        }

        return response()->json([
            'status' => true,
            'message' => 'Visitor report exported successfully',
            'data' => $data,
            'format' => $format
        ], 200);
    }

    /**
     * Get visitor report summary/statistics
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getSummary(Request $request): JsonResponse
    {
        $request->validate([
            'start_date' => 'required|date|date_format:Y-m-d',
            'end_date' => 'required|date|date_format:Y-m-d|after_or_equal:start_date'
        ]);

        $result = $this->summaryVisitorService->getVisitorReportByDateRange(
            $request->start_date,
            $request->end_date
        );

        if ($result['success']) {
            $data = $result['data'];
            
           
            $summary = [
                'total_visitors' => $data->count(),
                'date_range' => $result['date_range'],
                'generated_at' => now()->toDateTimeString()
            ];

            return response()->json([
                'status' => true,
                'message' => 'Visitor report summary retrieved successfully',
                'summary' => $summary
            ], 200);
        }

        return response()->json([
            'status' => false,
            'message' => $result['message'],
            'summary' => []
        ], 500);
    }
}
