<?php

namespace App\Services\Upload;

use App\Models\JobBatch;

class UploadFaceUploadService
{
    public function index($request)
    {
        $options = json_decode($request->options, true);
        $pages = isset($options['page']) ? (int)($options['page'] + 1) : 1;
        $row_data = isset($options['rows']) ? (int)$options['rows'] : 10;
        $sorts = isset($options['sortField']) ? (string)$options['sortField'] : "created_at";
        $order = (!empty($options['sortOrder'])) ? 'desc' : 'desc';
        $search = isset($request->search) ? (string)$request->search : "";
        $filters = $options['filters'] ?? null;

        $offset = ($pages - 1) * $row_data;

        $result = array();
        $query = JobBatch::where('name', 'LIKE', '%upload face record%');

        $result["total"] = $query->count();

        $result['data'] = $query
            ->orderBy($sorts, $order)
            ->offset($offset)
            ->limit($row_data)
            ->get();

        $result['header'] = $this->frontEndHeader();

        $collect = collect($result);

        return $collect->all();
    }

    public function frontEndHeader()
    {
        return [
            [
                "accessorKey" => 'name',
                "header" => 'Process Name',
            ],
            [
                "accessorKey" => 'percentage',
                "header" => 'Percentage',
            ],
            [
                "accessorKey" => 'total_jobs',
                "header" => 'Total Jobs',
            ],
            [
                "accessorKey" => 'pending_jobs',
                "header" => 'Pending Jobs',
            ],
            [
                "accessorKey" => 'failed_jobs',
                "header" => 'Failed Jobs',
            ],
            [
                "accessorKey" => 'created_at',
                "header" => 'Created At',
            ],
            [
                "accessorKey" => 'finished_at',
                "header" => 'Finish At',
            ],
        ];
    }
}
