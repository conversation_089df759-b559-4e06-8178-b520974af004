<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('log_transaction', function (Blueprint $table) {
            $table->text('access_level_name')->nullable();
            $table->string('log_type', 60)->nullable();
            $table->string('methode', 60)->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('log_transaction', function (Blueprint $table) {
            //
        });
    }
};
