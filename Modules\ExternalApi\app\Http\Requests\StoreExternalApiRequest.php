<?php

namespace Modules\ExternalApi\app\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreExternalApiRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'form.*.photo' => 'required|string',
            'form.*.name' => 'required|string',
            'form.*.identifyNumber' => 'required|string',
            'form.*.type' => 'required|string',
            'form.*.gender' => 'required|in:MALE,FEMALE',
            'form.*.birthDate' => 'required|date_format:Y-m-d',
            'form.*.nationality' => 'required|string',
            'form.*.employeeType' => 'required|string',
            'form.*.description' => 'nullable|string',
            'form.*.company' => 'required|string',
            'form.*.workLocation' => 'required|string',
            'form.*.departmentName' => 'required|string',
            'form.*.statusEmployee' => 'required|string',
            'accessType' => 'required|string|in:GENERAL,KI,CONTRACTOR',
            'service' => 'required|string|in:ALL,Huawei,Zkbio',
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function wantsJson()
    {
        return true;
    }

    protected function prepareForValidation()
    {
        // Validate that the request content type is application/json
        $this->validateContentType();
    }

    protected function validateContentType()
    {
        $contentType = $this->header('Content-Type');

        if ($contentType !== 'application/json') {
            $this->validator->errors()->add('content_type', 'The content type must be application/json.');
        }
    }
}
