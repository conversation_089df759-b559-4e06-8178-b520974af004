<?php

namespace App\Http\Controllers\Api\Config;

use App\Http\Controllers\Controller;
use App\Models\Settings\Nvr;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class NvrController extends Controller
{
    public function index(Request $request)
    {
        $tags = [];
        $filter = json_decode($request->get('filters'));
        $dataNvr = Nvr::where(function ($q) use ($filter) {
            if ($filter != '') {
                foreach ($filter as $tags) {
                    if ($tags != null) {
                        if ($tags->id == 'status') {
                            $val = ($tags->value == 'Active') ? 1 : 0;
                        } else {
                            $val =  $tags->value;
                        }
                        $q->where($tags->id, 'LIKE', '%' . $val . '%');
                    }
                }
            }
        })
            ->skip($request->get('start'))
            ->take($request->get('size'))
            ->orderBy('id', 'asc')
            ->get();

        $dataAll = Nvr::orderBy('id', 'desc')->get();
        $select = [];

        foreach ($dataAll as $role) {
            $select[] = [
                'value' => $role->id,
                'label' => $role->name,
            ];
        }

        try {
            return response()->json([
                'status'   => true,
                'message'  => 'Success',
                'data'     => $dataNvr,
                'select'   => $select,
                'total'    => $dataAll->count(),
                'response' => $request->all(),
            ], 200);
        } catch (\Throwable $th) {
            return response()->json([
                'status'  => false,
                'message' => $th->getMessage(),
                'data'    => []
            ], 500);
        }
    }

    public function store(Request $request)
    {
        try {
            $validateUser = Validator::make(
                $request->all(),
                [
                    'name'     => 'required|unique:m_nvr,name',
                    'ip'       => 'required',
                    'username' => 'required',
                    'password' => 'required|min:8',
                ]
            );

            if ($validateUser->fails()) {
                return response()->json([
                    'status'  => false,
                    'message' => 'validation error',
                    'errors'  => $validateUser->errors()
                ], 422);
            }

            $user = Nvr::create([
                'name'     => $request->name,
                'ip'       => $request->ip,
                'username' => $request->username,
                'password' => $request->password,
                'status'   => 1,
            ]);

            return response()->json([
                'status'  => true,
                'message' => 'Nvr Created Success'
            ], 200);
        } catch (\Throwable $th) {
            return response()->json([
                'status'  => false,
                'message' => $th->getMessage()
            ], 401);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Nvr  $nvr
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $rules = [
            'name'     => 'required',
            'ip'       => 'required',
            'username' => 'required',
            'password' => 'required|min:8',
        ];

        if ($request->name != $request->name_old) {
            $rules['name'] = 'required|unique:m_nvr,name';
        }

        $validatedData = $request->validate($rules);
        DB::beginTransaction();

        //update data nvr
        $validatedData['status'] = $request->status;
        $data = Nvr::where('id', $id)
            ->update($validatedData);

        if ($data) {
            DB::commit();
            return response()->json([
                'success' => true,
                'message' => $request->input('name') . ' update successfully !',
            ], 200);
        } else {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => "Failed to update !"
            ], 401);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($nvr, Request $request)
    {
        $data = nvr::find($nvr);
        $data->delete();
        return response()->json([
            'success' => true,
            'message' => $request->input('name') . ' successfully deleted !',
        ], 200);
    }
}
