<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;

/**
 * App\Models\Appauthentication
 *
 * @property int $id
 * @property string $name
 * @property string $token
 * @property string $link_url
 * @property int $status
 * @property string|null $created_at
 * @property string|null $updated_at
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection<int, \Illuminate\Notifications\DatabaseNotification> $notifications
 * @property-read int|null $notifications_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Laravel\Sanctum\PersonalAccessToken> $tokens
 * @property-read int|null $tokens_count
 * @method static \Illuminate\Database\Eloquent\Builder|Appauthentication newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Appauthentication newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Appauthentication query()
 * @method static \Illuminate\Database\Eloquent\Builder|Appauthentication whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Appauthentication whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Appauthentication whereLinkUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Appauthentication whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Appauthentication whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Appauthentication whereToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Appauthentication whereUpdatedAt($value)
 * @property string|null $index
 * @property string|null $post
 * @property string|null $delete
 * @property string|null $put
 * @method static \Illuminate\Database\Eloquent\Builder|Appauthentication whereDelete($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Appauthentication whereIndex($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Appauthentication wherePost($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Appauthentication wherePut($value)
 * @mixin \Eloquent
 */
class Appauthentication extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;
    // use HasFactory, Notifiable;

    public $timestamps                          = false;
    protected $table                            = 'applications';
    protected $primaryKey                       = 'id';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'token',
        'link_url',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
    ];
    protected $guarded = [];
}
