<?php

namespace Modules\ExternalApi\app\Services;

use App\Models\Settings\AccessLevel;
use App\Models\Settings\Organization;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Str;

class AccessLevelService
{
    /**
     * Retrieves access level information based on organization, department, and user details.
     *
     * @param string $organizationName Name of the organization
     * @param string $departmentName Department name, defaults to 'ALL'
     * @param string $accessType Type of access being requested
     * @param string $identifyNumber Identification number used for even/odd access rules
     * @param bool $isSimper Flag for SIMPER access type, defaults to false
     * @return array|null Returns access level details array or null if no access found
     */
    public function getAccessLevel($organizationName, $departmentName = 'ALL', $accessType, $identifyNumber, $isSimper = false): array|null
    {
        $accessLevels = $this->queryAccessLevel($organizationName, $departmentName, $accessType, $isSimper);

        if ($accessLevels->isEmpty()) {
            $accessLevels = $this->queryAccessLevel($organizationName, "ALL", $accessType, $isSimper);
        }

        foreach ($accessLevels as $accessLevel) {
            $result = $this->processAccessLevel(
                $accessLevel,
                $departmentName,
                $identifyNumber,
                $isSimper,
                $organizationName,
                $accessType,
                $accessLevels
            );

            if ($result) {
                $group = $this->getOrganizationGroup($organizationName);
                if ($group->name === 'TSINGSHAN') {
                    $resultTsinshan = $this->getTsingshanAccessLevel($accessLevels, $identifyNumber);

                    if ($resultTsinshan) {
                        return [
                            "access_level_code" => $result['access_level_code'] . ',' . $resultTsinshan['access_level_code'],
                            "access_level_name" => $result['access_level_name'] . ',' . $resultTsinshan['access_level_name'],
                            "access_type" => $accessType,
                        ];
                    } else {
                        return $result;
                    }
                } else {
                    return $result;
                }
            }
        }

        return $this->createNullAccessLevel($accessLevels->first()->accessType->name ?? null);
    }

    /**
     * Processes an access level based on department, SIMPER status, and other criteria.
     *
     * @param AccessLevel $accessLevel The access level to process
     * @param string $departmentName Name of the department
     * @param string $identifyNumber User identification number
     * @param bool $isSimper Whether this is a SIMPER access type
     * @param string $organizationName Name of the organization
     * @param string $accessType Type of access being requested
     * @param Collection $accessLevels Collection of all access levels
     * @return array|null Access level details array or null if no access granted
     */
    protected function processAccessLevel(AccessLevel $accessLevel, $departmentName, $identifyNumber, $isSimper, $organizationName, $accessType, $accessLevels)
    {
        $dept = explode(',', strtoupper($accessLevel->department_name));

        // Handle SIMPER case
        if ($isSimper && Str::contains($accessLevel->access_level_name, 'SIMPER')) {
            return $this->createAccessLevelResponse($accessLevel);
        }

        // Handle department specific case
        if (isset($accessLevel->department_name) && Str::contains(strtoupper($departmentName), $dept)) {
            return $this->mergeAccessLevel($identifyNumber, $accessLevel, $departmentName, $dept, $organizationName, $accessType, $accessLevels);
        }

        // Handle ALL department case
        if (strtoupper($accessLevel->department_name) === 'ALL') {
            return $this->processAllDepartmentCase($accessLevel, $identifyNumber, $organizationName);
        }

        return null;
    }

    /**
     * Retrieves the organization group for a given organization name.
     *
     * @param string $organizationName Name of the organization to search for
     * @return \App\Models\Settings\Organization Organization group associated with the organization
     */
    private function getOrganizationGroup($organizationName)
    {
        return Organization::where('name', 'LIKE', '%' . $organizationName . '%')
            ->first()
            ->organizationGroup;
    }

    /**
     * Processes access level rules for 'ALL' department cases based on even/odd identification numbers.
     *
     * @param AccessLevel $accessLevel The access level to process
     * @param string $identifyNumber User identification number to check even/odd status
     * @param string $organizationName Name of the organization
     * @return array|null Access level details array if conditions are met, null otherwise
     */
    protected function processAllDepartmentCase(AccessLevel $accessLevel, $identifyNumber, $organizationName)
    {
        $isEven = (int) $identifyNumber % 2 === 0;
        $group = $this->getOrganizationGroup($organizationName);

        if ($isEven && Str::contains($accessLevel->access_level_name, ['GENAP'])) {
            if ($group->name === 'TSINGSHAN' && !Str::contains($accessLevel->access_level_name, 'TSINGSHAN')) {
                return $this->createAccessLevelResponse($accessLevel);
            } else {
                return $this->createAccessLevelResponse($accessLevel);
            }
        }

        if (!$isEven && Str::contains($accessLevel->access_level_name, ['GANJIL'])) {
            if ($group->name === 'TSINGSHAN' && !Str::contains($accessLevel->access_level_name, 'TSINGSHAN')) {
                return $this->createAccessLevelResponse($accessLevel);
            } else {
                return $this->createAccessLevelResponse($accessLevel);
            }
        }

        return null;
    }

    /**
     * Determines and returns Tsingshan-specific access level based on identification number.
     *
     * @param Collection $accessLevels Collection of access levels to search through
     * @param string $identifyNumber User identification number to check even/odd status
     * @return array|null Access level details array for Tsingshan employees or null if no match found
     */
    private function getTsingshanAccessLevel(Collection $accessLevels, $identifyNumber)
    {
        $isEven = (int) $identifyNumber % 2 === 0;

        $tsingshanEven = $accessLevels->first(function ($level) {
            return Str::contains($level->access_level_name, ['TSINGSHAN EMP GENAP']);
        });

        $tsingshanOdd = $accessLevels->first(function ($level) {
            return Str::contains($level->access_level_name, ['TSINGSHAN EMP GANJIL']);
        });

        if ($isEven && $tsingshanEven) {
            return $this->createAccessLevelResponse($tsingshanEven);
        }

        if (!$isEven && $tsingshanOdd) {
            return $this->createAccessLevelResponse($tsingshanOdd);
        }

        return null;
    }

    /**
     * Creates a standardized array response from an AccessLevel object.
     *
     * @param AccessLevel $accessLevel The access level object to format
     * @return array Array containing access level code, name and type
     */
    protected function createAccessLevelResponse(AccessLevel $accessLevel): array
    {
        return [
            "access_level_code" => $accessLevel->access_level_code,
            "access_level_name" => $accessLevel->access_level_name,
            "access_type" => $accessLevel->accessType->name,
        ];
    }

    /**
     * Creates a default access level array with null values.
     *
     * @param string|null $accessType The access type to include in the response
     * @return array Array containing null access level code and name with provided access type
     */
    protected function createNullAccessLevel($accessType): array
    {
        return [
            "access_level_code" => null,
            "access_level_name" => null,
            "access_type" => $accessType,
        ];
    }

    /**
     * Merges department-specific and global access levels based on identification number.
     *
     * @param string $identifyNumber User identification number for even/odd access rules
     * @param AccessLevel $accessLevel Department-specific access level object
     * @param string $departmentName Name of the department
     * @param array $dept Array of department names to check against
     * @param string $organizationName Name of the organization
     * @param string $accessType Type of access being requested
     * @param Collection $allAccessLevel Collection of all access levels
     * @return array Array containing merged access level details
     */
    protected function mergeAccessLevel($identifyNumber, AccessLevel $accessLevel, $departmentName, $dept, $organizationName, $accessType, $allAccessLevel): array
    {
        // Get ALL department access levels and filter for GANJIL/GENAP based on identify number
        $accessLevels = $this->queryAccessLevel($organizationName, 'ALL', $accessType, false);
        $globalAccessLevel = $accessLevels
            ->first(function ($item) use ($identifyNumber) {
                $isEven = (int) $identifyNumber % 2 === 0;
                $isEvenRule = Str::contains(strtoupper($item->access_level_name), 'GENAP');
                $isOddRule = Str::contains(strtoupper($item->access_level_name), 'GANJIL');

                return $item->department_name === 'ALL' &&
                    (($isEven && $isEvenRule) || (!$isEven && $isOddRule));
            });

        // Format global access level if found
        $globalAccess = $globalAccessLevel ? $this->createAccessLevelResponse($globalAccessLevel) : [];

        // Check if department-specific access should be included
        $hasDepartmentAccess = Str::contains(strtoupper($departmentName), $dept) &&
            !Str::contains(strtoupper($accessLevel->access_level_name), ['GANJIL', 'GENAP']);

        if ($hasDepartmentAccess) {
            return [
                'access_level_code' => $globalAccess ?
                    $accessLevel->access_level_code . ',' . $globalAccess['access_level_code'] :
                    $accessLevel->access_level_code,
                'access_level_name' => $globalAccess ?
                    $accessLevel->access_level_name . ',' . $globalAccess['access_level_name'] :
                    $accessLevel->access_level_name,
                'access_type' => $accessLevel->accessType->name,
            ];
        }

        return [
            'access_level_code' => $globalAccess['access_level_code'] ?? null,
            'access_level_name' => $globalAccess['access_level_name'] ?? null,
            'access_type' => $accessLevel->accessType->name,
        ];
    }

    /**
     * Queries access levels based on organization, department, and access type criteria.
     *
     * @param string $organizationName Name of the organization to filter by
     * @param string $departmentName Name of the department to filter by
     * @param string $accessType Type of access to filter by
     * @param bool $isSimper Whether to apply SIMPER-specific filtering, defaults to false
     * @return Collection<int, AccessLevel>
     */
    protected function queryAccessLevel($organizationName, $departmentName, $accessType, $isSimper = false): Collection
    {
        return AccessLevel::whereHas('organizationAccessLevel.organization', function (Builder $query) use ($organizationName, $isSimper) {
            if (!$isSimper) {
                $query->where('name', 'LIKE', '%' . $organizationName . '%');
            }
        })
            ->whereHas('accessType', function (Builder $query) use ($accessType) {
                $query->where('name', 'LIKE', '%' . $accessType . '%');
            })
            ->where('department_name', 'LIKE', '%' . $departmentName . '%')
            ->with(['accessType'])
            ->get();
    }
}
