<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('face_lists', function (Blueprint $table) {
            // $table->dropColumn('organization_id');
            $table->unsignedInteger('face_list_id');
        });

        Schema::table('access_levels', function (Blueprint $table) {
            // $table->dropColumn('organization_id');
            $table->string('access_level_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('face_lists', function (Blueprint $table) {
            //
        });
    }
};
