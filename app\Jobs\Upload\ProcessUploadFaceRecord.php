<?php

namespace App\Jobs\Upload;

use App\Models\Settings\FaceList;
use App\Models\Settings\Nvr;
use App\Services\Upload\BatchUploadService;
use App\Traits\NvrCookieHelper;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ProcessUploadFaceRecord implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use Batchable;
    use NvrCookieHelper;

    public $row;
    public $faceImage;

    /**
     * Create a new job instance.
     */
    public function __construct($row)
    {
        $this->row = $row;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $rows = $this->row;
        $service = new BatchUploadService();

        Log::info('start upload face record');
        foreach ($rows as $row) {
            $company = $row['company'];
            Log::info('Upload images to nvr ' . $row['nvr_id']);

            $faceList = FaceList::where('nvr_id', $row['nvr_id'])
                ->whereHas('organizationFaceList', function ($query) use ($company) {
                    $query->whereHas('organization', function ($qr) use ($company) {
                        return $qr->where('name', 'LIKE', '%' . $company . '%');
                    });
                })
                ->get();
            foreach ($faceList as $index => $item) {
                $nvr = Nvr::find($item->nvr_id);
                $service->validateNvrCookie($nvr->id);
                $nvrName = str_replace(' ', '_', $nvr->name);
                $cookieNvr = $this->getConfigNvr($nvrName, 'cookie ' . $nvrName);
                $upload = Http::withoutVerifying()
                    ->withOptions(['verify' => false])
                    ->withHeaders([
                        'Accept' => 'application/json',
                        'Cookie' => 'JSESSIONID=' . $cookieNvr,
                    ])
                    ->post($nvr->ip . '/sdk_service/rest/facerepositories/' . $item->face_list_id . '/peoples', [
                        'peopleList' => [
                            'index' => $index,
                            'name' => $row['name'],
                            'credentialNumber' => $row['credentialNumber'],
                            'credentialType' => $row['credentialType'],
                            'gender' => $row['gender'],
                            'bornTime' => $row['bornTime'],
                            'country' => $row['country'],
                            'occupation' => $row['occupation'],
                            'description' => $row['description'],
                            'strId' => $row['strId'],
                            'fileIdList' => $row['file_id'],
                        ]
                    ]);

                Log::info('result from upload face record', [
                    'body' => $upload->body(),
                    'json' => $upload->json(),
                ]);
            }
        }
    }
}
