<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class SummaryEmployeeController extends Controller
{
    public function index(Request $request)
    {
        $service = new \App\Services\Dashboard\SummaryEmployeeService();
        return response()->json([
            "data" => [
                "employee" => collect($service->getSummaryHris())->merge($service->getSummaryCherry())->all(),
                "zkbio" => $service->getSummaryZkbio(),
            ],
        ]);
    }

    public function getTotal(Request $request)
    {
        $service = new \App\Services\Dashboard\SummaryEmployeeService();
        $cherry = $service->getTotalCherry();
        $hris = $service->getTotalHris();
        $total = number_format($cherry + $hris);
        return response()->json([
            "data" => [
                [
                    "name" => "HRIS + Cherry",
                    "total" => number_format($hris) . " + " . number_format($cherry) . " = " . $total,
                ],
                [
                    "name" => "ZKBIO",
                    "total" => number_format($service->getTotalZkbio()),
                ],
            ]
        ]);
    }

    public function show(Request $request, $id)
    {
        $service = new \App\Services\Dashboard\SummaryEmployeeService();
        $isInternalEmployee = !str($id)->contains(["PT BDM", "PT BDT", "PT IMIP"]);

        return response()->json([
            "data" => [
                "employee" => $isInternalEmployee
                    ? $service->getDetailHris($id)
                    : $service->getDetailCherry($id),
                "zkbio" => $isInternalEmployee
                    ? $service->getDetailZkbioInternal($id)
                    : $service->getDetailZkbioImipGroup($id),
            ]
        ]);
    }
}
