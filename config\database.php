<?php

use Illuminate\Support\Str;

return [

    /*
    |--------------------------------------------------------------------------
    | Default Database Connection Name
    |--------------------------------------------------------------------------
    |
    | Here you may specify which of the database connections below you wish
    | to use as your default connection for all database work. Of course
    | you may use many connections at once using the Database library.
    |
    */

    'default' => env('DB_CONNECTION', 'mysql'),

    /*
    |--------------------------------------------------------------------------
    | Database Connections
    |--------------------------------------------------------------------------
    |
    | Here are each of the database connections setup for your application.
    | Of course, examples of configuring each database platform that is
    | supported by Laravel is shown below to make development simple.
    |
    |
    | All database work in Laravel is done through the PHP PDO facilities
    | so make sure you have the driver for your particular database of
    | choice installed on your machine before you begin development.
    |
    */

    'connections' => [

        'sqlite' => [
            'driver' => 'sqlite',
            'url' => env('DATABASE_URL'),
            'database' => env('DB_DATABASE', database_path('database.sqlite')),
            'prefix' => '',
            'foreign_key_constraints' => env('DB_FOREIGN_KEYS', true),
        ],

        'mysqlhris' => [
            'driver' => env('DB_CONNECTION_HRIS', 'mysql'),
            'url' => env('DATABASE_URL'),
            'host' => env('DB_HOST_HRIS', '127.0.0.1'),
            'port' => env('DB_PORT_HRIS', '3306'),
            'database' => env('DB_DATABASE_HRIS', 'forge'),
            'username' => env('DB_USERNAME_HRIS', 'forge'),
            'password' => env('DB_PASSWORD_HRIS', ''),
            'unix_socket' => env('DB_SOCKET', ''),
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'prefix_indexes' => true,
            'strict' => true,
            'engine' => null,
            'options' => extension_loaded('pdo_mysql') ? array_filter([
                PDO::MYSQL_ATTR_SSL_CA => env('MYSQL_ATTR_SSL_CA'),
                PDO::ATTR_EMULATE_PREPARES => true
            ]) : [],
        ],

        'pgsql' => [
            'driver' => 'pgsql',
            'url' => env('DATABASE_URL'),
            'host' => env('DB_HOST', '127.0.0.1'),
            'port' => env('DB_PORT', '5432'),
            'database' => env('DB_DATABASE', 'forge'),
            'username' => env('DB_USERNAME', 'forge'),
            'password' => env('DB_PASSWORD', ''),
            'charset' => 'utf8',
            'prefix' => '',
            'prefix_indexes' => true,
            'search_path' => 'public',
            'sslmode' => 'prefer',
        ],

        'sqlsrv' => [
            'driver'         => 'sqlsrv',
            'url'            => env('DATABASE_URL'),
            'host'           => env('DB_HOST', 'localhost'),
            'port'           => env('DB_PORT', '1433'),
            'database'       => env('DB_DATABASE', 'forge'),
            'username'       => env('DB_USERNAME', 'forge'),
            'password'       => env('DB_PASSWORD', ''),
            'charset'        => 'utf8',
            'prefix'         => '',
            'prefix_indexes' => true,
            'pooling'        => false,
            'options'        => [
                'CharacterSet' => 'UTF-8',
            ],
        ],

        // 'sqlsrvcontractor' => [
        //     'driver'         => 'sqlsrv',
        //     'url'            => env('DATABASE_URL'),
        //     'host'           => env('DB_HOST3', 'localhost'),
        //     'port'           => env('DB_PORT3', '1433'),
        //     'database'       => env('DB_DATABASE3', 'forge'),
        //     'username'       => env('DB_USERNAME3', 'forge'),
        //     'password'       => env('DB_PASSWORD3', ''),
        //     'charset'        => 'utf8',
        //     'prefix'         => '',
        //     'prefix_indexes' => true,
        // ],

        'sqlsrvcontractor' => [
            'driver'         => 'sqlsrv',
            'url'            => env('DATABASE_URL'),
            'host'           => env('DB_HOST_ECON', 'localhost'),
            'port'           => env('DB_PORT_ECON', '1433'),
            'database'       => env('DB_DATABASE_ECON', 'forge'),
            'username'       => env('DB_USERNAME_ECON', 'forge'),
            'password'       => env('DB_PASSWORD_ECON', ''),
            'charset'        => 'utf8',
            'prefix'         => '',
            'prefix_indexes' => true,
        ],

        'sqlsrvsimper' => [
            'driver'         => 'sqlsrv',
            'url'            => env('DATABASE_URL'),
            'host'           => env('DB_HOST_SIMPER', 'localhost'),
            'port'           => env('DB_PORT_SIMPER', '1433'),
            'database'       => env('DB_DATABASE_SIMPER', 'forge'),
            'username'       => env('DB_USERNAME_SIMPER', 'forge'),
            'password'       => env('DB_PASSWORD_SIMPER', ''),
            'charset'        => 'utf8',
            'prefix'         => '',
            'prefix_indexes' => true,
        ],

        'sqlsrvcherry' => [
            'driver'         => 'sqlsrv',
            'url'            => env('DATABASE_URL'),
            'host'           => env('DB_HOST_CHERRY', 'localhost'),
            'port'           => env('DB_PORT_CHERRY', '1433'),
            'database'       => env('DB_DATABASE_CHERRY', 'forge'),
            'username'       => env('DB_USERNAME_CHERRY', 'forge'),
            'password'       => env('DB_PASSWORD_CHERRY', ''),
            'charset'        => 'utf8',
            'prefix'         => '',
            'prefix_indexes' => true,
        ],

        'sqlsrvzkbio' => [
            'driver' => 'sqlsrv',
            'url' => env('DATABASE_URL'),
            'host' => env('DB_HOST_ZKBIO', 'localhost'),
            'port' => env('DB_PORT_ZKBIO', '1433'),
            'database' => env('DB_DATABASE_ZKBIO', 'forge'),
            'username' => env('DB_USERNAME_ZKBIO', 'forge'),
            'password' => env('DB_PASSWORD_ZKBIO', ''),
            'charset' => 'utf8',
            'prefix' => '',
            'prefix_indexes' => true,
        ],

        // 'sqlsrvhris' => [
        //     'driver' => env('DB_CONNECTION_HRIS', 'mysql'),
        //     'url' => env('DATABASE_URL'),
        //     'host' => env('DB_HOST_HRIS', '127.0.0.1'),
        //     'port' => env('DB_PORT_HRIS', '3306'),
        //     'database' => env('DB_DATABASE_HRIS', 'forge'),
        //     'username' => env('DB_USERNAME_HRIS', 'forge'),
        //     'password' => env('DB_PASSWORD_HRIS', ''),
        //     'unix_socket' => env('DB_SOCKET', ''),
        //     'charset' => 'utf8mb4',
        //     'collation' => 'utf8mb4_unicode_ci',
        //     'prefix' => '',
        //     'prefix_indexes' => true,
        //     'strict' => true,
        //     'engine' => null,
        //     'options' => extension_loaded('pdo_mysql') ? array_filter([
        //         PDO::MYSQL_ATTR_SSL_CA => env('MYSQL_ATTR_SSL_CA'),
        //         PDO::ATTR_EMULATE_PREPARES => true
        //     ]) : [],
        // ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Migration Repository Table
    |--------------------------------------------------------------------------
    |
    | This table keeps track of all the migrations that have already run for
    | your application. Using this information, we can determine which of
    | the migrations on disk haven't actually been run in the database.
    |
    */

    'migrations' => 'migrations',

    /*
    |--------------------------------------------------------------------------
    | Redis Databases
    |--------------------------------------------------------------------------
    |
    | Redis is an open source, fast, and advanced key-value store that also
    | provides a richer body of commands than a typical key-value system
    | such as APC or Memcached. Laravel makes it easy to dig right in.
    |
    */

    'redis' => [

        'client' => env('REDIS_CLIENT', 'phpredis'),

        'options' => [
            'cluster' => env('REDIS_CLUSTER', 'redis'),
            'prefix' => env('REDIS_PREFIX', Str::slug(env('APP_NAME', 'laravel'), '_') . '_database_'),
        ],

        'default' => [
            'url' => env('REDIS_URL'),
            'host' => env('REDIS_HOST', '127.0.0.1'),
            'username' => env('REDIS_USERNAME'),
            'password' => env('REDIS_PASSWORD'),
            'port' => env('REDIS_PORT', '6379'),
            'database' => env('REDIS_DB', '0'),
        ],

        'cache' => [
            'url' => env('REDIS_URL'),
            'host' => env('REDIS_HOST', '127.0.0.1'),
            'username' => env('REDIS_USERNAME'),
            'password' => env('REDIS_PASSWORD'),
            'port' => env('REDIS_PORT', '6379'),
            'database' => env('REDIS_CACHE_DB', '1'),
        ],

    ],

];
