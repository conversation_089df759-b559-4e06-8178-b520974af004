<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Appauthentication;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class AppauthenticationController extends Controller
{ 

	public function verify(Request $request){

        return response()->json([
            'status'   => true,
            'message'  => 'Success',
        ], 200);
    }
}