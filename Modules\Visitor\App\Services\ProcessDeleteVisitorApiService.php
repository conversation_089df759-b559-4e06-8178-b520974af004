<?php

namespace Modules\Visitor\App\Services;

use Throwable;
use Illuminate\Bus\Batch;
use App\Models\Applications;
use App\Models\Settings\Nvr;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use App\Models\Settings\Log_transaction;
use Illuminate\Database\Eloquent\Collection;
use Modules\Visitor\App\Jobs\ProcessDeleteVisitorZkbioModule;
use Modules\Visitor\App\Jobs\ProcessDeleteVisitorHuaweiModule;

class ProcessDeleteVisitorApiService
{
      /**
     * @param $data
     * @param Applications $application
     * @return array
     * @throws Throwable
     */
    public function processQueueZkbio($data, Applications $application): array
    {

        $collection = collect($data);
        $chunks = $collection->chunk(20);
        $batchQueue = [];
        $batch = Bus::batch([])
            ->catch(function (Batch $batch, Throwable $e) use ($application) {
                Log::info('error upload batch ' . now(), [
                    'error' => $e->getTraceAsString(),
                ]);
                $logTransaction = Log_transaction::where('batch_id', $batch->id)
                    ->select(
                        "credential_number",
                        "name",
                        "company",
                        "nvr_id",
                        "face_list_id",
                        "service_code",
                        "service_message",
                        "batch_id"
                    )
                    ->where("service_code", "<>", "0")
                    ->where('application_id', $application->id)
                    ->where('type', "ZKBIO")
                    ->get();

                $params = [
                    'message' => $e->getMessage(),
                    'batch_id' => $batch->id,
                    'status' => 'error',
                    'name' => $batch->name,
                    'data' => $logTransaction,
                    // "application" => $application->id,
                ];

                Http::post($application->link_url, $params);

                activity()
                    ->performedOn($application)
                    ->withProperties($params)
                    ->log('Error process face record to application ' . $application->name);
            })
            ->then(function (Batch $batch) use ($application) {
                Log::info('Batch ID ZKBIO ' . $batch->id);
                $logTransaction = Log_transaction::where('batch_id', $batch->id)
                    ->select(
                        "credential_number",
                        "name",
                        "company",
                        "nvr_id",
                        "face_list_id",
                        "service_code",
                        "service_message",
                        "batch_id"
                    )
                    ->where('application_id', $application->id)
                    ->where('type', "ZKBIO")
                    ->get();
                $params = [
                    'message' => 'success',
                    'batch_id' => $batch->id,
                    'status' => 'completed',
                    'name' => $batch->name,
                    "data" => $logTransaction,
                ];
                Http::post($application->link_url, $params);

                activity()
                    ->performedOn($application)
                    ->withProperties($params)
                    ->log('Success process face record to application ' . $application->name);
            })
            ->name('Upload Zkbio Visitor' . date('Y-m-d H:i:s'))
            ->onQueue('ZkbioVisitorApiDelete')
            ->allowFailures()
            ->dispatch();

        foreach ($chunks as $key => $chunk) {
            $batch->add(new ProcessDeleteVisitorZkbioModule(row: $chunk, batchId: $batch->id, application: $application));
        }

        return [
            [
                'label' => 'ZKBIO',
                'batch_id' => $batch->id
            ]
        ];
    }

    public function processQueueHuawei($data, Collection $nvr, Applications $application): array
    {
        $batchId = [];
        foreach ($nvr as $item) {
            $nvrName = str_replace(' ', '', $item->name);
            $collection = collect($data);
            $chunks = $collection->chunk(100);
            $batchQueue = [];
            $batch = Bus::batch([])
                ->catch(function (Batch $batch, Throwable $e) use ($application, $nvrName) {
                    Log::info('Error delete from huawei ' . $nvrName . ' time: ' . now(), [
                        'error' => $e->getTraceAsString(),
                    ]);
                    $logTransaction = Log_transaction::where('batch_id', $batch->id)
                        ->select(
                            "credential_number",
                            "name",
                            "company",
                            "access_level_code",
                            "access_level_name",
                            "service_code",
                            "service_message",
                            "batch_id"
                        )
                        ->where("service_code", "<>", "0")
                        ->where('application_id', $application->id)
                        ->where('type', "HUAWEI")
                        ->get();

                    $params = [
                        'message' => $e->getMessage(),
                        'batch_id' => $batch->id,
                        'status' => 'error',
                        'name' => $batch->name,
                        'data' => $logTransaction,
                    ];
                    Http::post($application->link_url, $params);

                    activity()
                        ->performedOn($application)
                        ->withProperties($params)
                        ->log('Error process visitor from application ' . $application->name);
                })
                ->then(function (Batch $batch) use ($application) {
                    Log::info('Batch ID Huawei ' . $batch->id);
                    $logTransaction = Log_transaction::where('batch_id', $batch->id)
                        ->select(
                            "credential_number",
                            "name",
                            "company",
                            "access_level_code",
                            "access_level_name",
                            "service_code",
                            "service_message",
                            "batch_id"
                        )
                        ->where('application_id', $application->id)
                        ->where('type', "HUAWEI")
                        ->get();

                    $params = [
                        'message' => 'success',
                        'batch_id' => $batch->id,
                        'status' => 'completed',
                        'name' => $batch->name,
                        "data" => $logTransaction,
                    ];
                    Http::post($application->link_url, $params);

                    activity()
                        ->performedOn($application)
                        ->withProperties($params)
                        ->log('Success delete visitor from application ' . $application->name);
                })
                ->name('Delete Visitor Face Record ' . $nvrName . date('Y-m-d H:i:s'))
                ->onQueue(queue: $nvrName.'API-VISITOR')
                ->allowFailures()
                ->dispatch();

            foreach ($chunks as $key => $chunk) {
                $batch->add(new ProcessDeleteVisitorHuaweiModule($chunk, $item->id, $batch->id,application: $application));
            }

            $batchId[] = [
                'label' => $nvrName,
                'batch_id' => $batch->id
            ];
        }
        return $batchId;
    }
}