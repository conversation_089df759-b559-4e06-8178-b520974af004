<?php

namespace Modules\ExternalApi\app\Jobs;

use App\Models\Applications;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Bus\Batchable;
use Illuminate\Support\Collection;
use Illuminate\Queue\InteractsWithQueue;
use App\Services\ProcessDataPersonService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class ProcessPhotoZkbioModule implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use Batchable;

    public Collection $row;
    public string $accLevelIds;
    public $batchId;
    public Applications $application;

    /**
     * Create a new job instance.
     */
    public function __construct($row, $batchId, $application)
    {
        $this->row = $row;
        $this->batchId = $batchId;
        $this->application = $application;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $rows = $this->row;

        $service = new ProcessDataPersonService();

        $service->processChangePhotoZkbio($rows, batch_id: $this->batchId, applications: $this->application);
        
    }
}
