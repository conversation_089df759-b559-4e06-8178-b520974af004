<?php

namespace Modules\Device\App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class OpenDeviceRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'doorName' => 'required',
            'interval' => 'required|integer|max:255',
        ];
    }

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }
}
