<?php

namespace App\Services;

use Carbon\Carbon;
use App\Models\Applications;
use App\Models\Settings\Nvr;
use App\Helpers\DepartmentCode;
use App\Traits\NvrCookieHelper;
use App\Models\Settings\Setting;
use App\Models\Settings\FaceList;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use App\Models\Settings\Organization;
use Intervention\Image\Facades\Image;
use Illuminate\Support\Facades\Storage;
use App\Models\Settings\Log_transaction;
use App\Models\Settings\Persons;
use App\Services\Upload\BatchUploadService;
use App\Services\Upload\BatchUploadZkbioService;
use Illuminate\Support\Facades\Artisan;

class ProcessDataPersonService
{
    use NvrCookieHelper;

    /**
     * @param int $nvr_id
     * @param string $company
     * @return mixed
     */
    public function getFaceList(int $nvr_id, string $company, int $status = 3): mixed
    {
        return FaceList::where('nvr_id', $nvr_id)
            ->whereHas('organizationFaceList', function ($query) use ($company) {
                $query->whereHas('organization', function ($qr) use ($company) {
                    return $qr->where('name', 'LIKE', '%' . $company . '%');
                });
            })
            ->where('face_list_type', $status)
            ->get();
    }

    /**
     * @param $rows
     * @param int $nvr_id
     * @param $batch_id
     * @return void
     */
    public function uploadFaceRecord($rows, int $nvr_id, $batch_id): void
    {
        $service = new BatchUploadService();

        $start = Carbon::now()->format('Y-m-d H:i:s');

        Log::info('start upload face record to NVR ' . $nvr_id . ' ! Time :' . $start, [
            'count rows' => count($rows)
        ]);

        foreach ($rows as $employee) {
            $ftp = Storage::disk('ftp')->get('photo/' . $employee['photoName']);

            $company = $employee['company'];
            $statusEmployee = $employee['statusEmployee'];

            // don't add face record when status employee is 0
            if ($statusEmployee != '0') {
                $fullName = preg_replace('/[^A-Za-z0-9\-\s]/', '', $employee['name']);

                if (isset($ftp) or isset($employee['base64'])) {

                    if (isset($employee['base64'])) {
                        $base64 = preg_replace('#^data:image/\w+;base64,#i', '', $employee['base64']);
                    } else {
                        $ftpImage = Image::make($ftp)->resize(null, 500, function ($constraint) {
                            $constraint->aspectRatio();
                        });

                        $base64 = preg_replace('#^data:image/\w+;base64,#i', '', $ftpImage->encode('data-url'));
                    }

                    $photoName = $employee['photoName'];
                    $faceList = $this->getFaceList($nvr_id, $company, $statusEmployee);

                    if ($faceList) {
                        foreach ($faceList as $index => $item) {
                            $index = 1;
                            $nvr = Nvr::find($item->nvr_id);

                            $service->validateNvrCookie($nvr->id);
                            $nvrName = str_replace(' ', '_', $nvr->name);
                            $cookieNvr = $this->getConfigNvr($nvrName, 'cookie ' . $nvrName);

                            Log::info('Cookie ' . $nvrName . ' = ' . $cookieNvr);

                            $this->processUploadFaceRecord($index, $fullName, $employee, $base64, $cookieNvr, $nvr, $item, $batch_id, $photoName, 'POST');
                        }
                    } else {
                        $this->createLogFaceRecord(employee: $employee, item: [], upload: [], params: [], batch_id: $batch_id, submitted: false, message: 'FaceList Not Found!', code: 'FL404', method: 'POST');

                    }
                } else {
                    $this->createLogFaceRecord(employee: $employee, item: [], upload: [], params: [], batch_id: $batch_id, submitted: false, message: 'Photo not found!', code: 'PH404', method: 'POST');
                    Log::info('Photo not found!');
                }
            }
        }

        Log::info('Upload Done ! Time : ' . Carbon::now()->diffForHumans($start), [
            'batch id' => $batch_id
        ]);
    }

    /**
     * @param $rows
     * @param int $nvr_id
     * @param $batch_id
     * @return void
     */
    public function editFaceRecord($rows, int $nvr_id, $batch_id): void
    {
        $service = new BatchUploadService();

        $start = Carbon::now()->format('Y-m-d H:i:s');

        Log::info('start upload face record to NVR ' . $nvr_id . ' ! Time :' . $start, [
            'count rows' => count($rows)
        ]);

        foreach ($rows as $employee) {
            $ftp = Storage::disk('ftp')->get('photo/' . $employee['photoName']);

            $company = $employee['company'];
            $statusEmployee = $employee['statusEmployee'];
            $fullName = preg_replace('/[^A-Za-z0-9\-\s]/', '', $employee['name']);

            Log::info('Upload employee ' , [
                'employee' => $employee
            ]);

            // $credentialNumber = (string) $employee['credentialNumber'];  // Cast to string
            $logLast = Log_transaction::select('company')
                ->where('credential_number', strval($employee['credentialNumber']))
                ->orderByDesc('id')
                ->first();

            if ($logLast->company != $company) {
                if (isset($ftp) or isset($employee['base64'])) {

                    if (isset($employee['base64'])) {
                        $base64 = preg_replace('#^data:image/\w+;base64,#i', '', $employee['base64']);
                    } else {
                        $ftpImage = Image::make($ftp)->resize(null, 500, function ($constraint) {
                            $constraint->aspectRatio();
                        });

                        $base64 = preg_replace('#^data:image/\w+;base64,#i', '', $ftpImage->encode('data-url'));
                    }

                    $photoName = $employee['photoName'];
                    $faceListOld = $this->getFaceList($nvr_id, $logLast->company, $statusEmployee);
                    $faceList = $this->getFaceList($nvr_id, $company, $statusEmployee);

                    if ($faceListOld) {
                        foreach ($faceListOld as $indexOld => $itemOld) {
                            $nvr = Nvr::find($itemOld->nvr_id);
                            $service->validateNvrCookie($nvr->id);
                            $nvrName = str_replace(' ', '_', $nvr->name);
                            $cookieNvr = $this->getConfigNvr($nvrName, 'cookie ' . $nvrName);
                            // upload face record
                            $this->deleteFaceRecord($indexOld, $fullName, $employee, $base64, $cookieNvr, $nvr, $itemOld, $batch_id);
                        }
                    }

                    if ($faceList) {
                        foreach ($faceList as $index => $item) {
                            $nvr = Nvr::find($item->nvr_id);
                            $service->validateNvrCookie($nvr->id);
                            $nvrName = str_replace(' ', '_', $nvr->name);
                            $cookieNvr = $this->getConfigNvr($nvrName, 'cookie ' . $nvrName);

                            Log::info('Cookie ' . $nvrName . ' = ' . $cookieNvr);
                            // upload face record
                            $this->processUploadFaceRecord($index, $fullName, $employee, $base64, $cookieNvr, $nvr, $item, $batch_id, $photoName, 'PUT');
                        }
                    } else {
                        $this->createLogFaceRecord(employee: $employee, item: [], upload: [], params: [], batch_id: $batch_id, submitted: false, message: 'FaceList Not Found!', code: 'FL404', method: 'PUT');
                        Log::info('FaceList Not Found!');
                    }
                } else {
                    $this->createLogFaceRecord(employee: $employee, item: [], upload: [], params: [], batch_id: $batch_id, submitted: false, message: 'Photo not found!', code: 'PH404', method: 'PUT');
                    Log::info('Photo not found!');
                }

            } else {
                if (isset($ftp) or isset($employee['base64'])) {

                    if (isset($employee['base64'])) {
                        $base64 = preg_replace('#^data:image/\w+;base64,#i', '', $employee['base64']);
                    } else {
                        $ftpImage = Image::make($ftp)->resize(null, 500, function ($constraint) {
                            $constraint->aspectRatio();
                        });

                        $base64 = preg_replace('#^data:image/\w+;base64,#i', '', $ftpImage->encode('data-url'));
                    }

                    $faceList = $this->getFaceList($nvr_id, $company, $statusEmployee);

                    if ($faceList) {
                        foreach ($faceList as $index => $item) {
                            $nvr = Nvr::find($item->nvr_id);
                            $service->validateNvrCookie($nvr->id);
                            $nvrName = str_replace(' ', '_', $nvr->name);
                            $cookieNvr = $this->getConfigNvr($nvrName, 'cookie ' . $nvrName);

                            Log::info('Cookie ' . $nvrName . ' = ' . $cookieNvr);
                            // upload face record
                            $this->processEditFaceRecord($index, $fullName, $employee, $base64, $cookieNvr, $nvr, $item, $batch_id);
                        }
                    } else {
                        $this->createLogFaceRecord(employee: $employee, item: [], upload: [], params: [], batch_id: $batch_id, submitted: false, message: 'FaceList Not Found!', code: 'FL404', method: 'PUT');
                        Log::info('FaceList Not Found!');
                    }
                } else {
                    $this->createLogFaceRecord(employee: $employee, item: [], upload: [], params: [], batch_id: $batch_id, submitted: false, message: 'Photo not found!', code: 'PH404', method: 'PUT');
                    Log::info('Photo not found!');
                }
            }


        }

        Log::info('Upload Done ! Time : ' . Carbon::now()->diffForHumans($start), [
            'batch id' => $batch_id
        ]);
    }

    /**
     * @param $rows
     * @param int $nvr_id
     * @param $batch_id
     * @return void
     */
    public function changePhotoFaceRecord($rows, int $nvr_id, $batch_id): void
    {
        $service = new BatchUploadService();

        $start = Carbon::now()->format('Y-m-d H:i:s');

        Log::info('start upload face record to NVR ' . $nvr_id . ' ! Time :' . $start, [
            'count rows' => count($rows)
        ]);

        foreach ($rows as $employee) {
            $ftp = Storage::disk('ftp')->get('photo/' . $employee['photoName']);
            $company = $employee['company'];
            $statusEmployee = $employee['statusEmployee'];
            $fullName = preg_replace('/[^A-Za-z0-9\-\s]/', '', $employee['name']);

            if (isset($ftp) or isset($employee['base64'])) {
                if (isset($employee['base64'])) {
                    $base64 = preg_replace('#^data:image/\w+;base64,#i', '', $employee['base64']);
                } else {
                    $ftpImage = Image::make($ftp)->resize(null, 500, function ($constraint) {
                        $constraint->aspectRatio();
                    });
                    $base64 = preg_replace('#^data:image/\w+;base64,#i', '', $ftpImage->encode('data-url'));
                }
                $faceList = $this->getFaceList($nvr_id, $company, $statusEmployee);
                if ($faceList) {
                    foreach ($faceList as $index => $item) {
                        $nvr = Nvr::find($item->nvr_id);
                        $service->validateNvrCookie($nvr->id);
                        $nvrName = str_replace(' ', '_', $nvr->name);
                        $cookieNvr = $this->getConfigNvr($nvrName, 'cookie ' . $nvrName);

                        Log::info('Cookie ' . $nvrName . ' = ' . $cookieNvr);
                        $this->processChangePhotoFaceRecord($index, $fullName, $employee, $base64, $cookieNvr, $nvr, $item, $batch_id);
                    }
                } else {
                    $this->createLogFaceRecord(employee: $employee, item: [], upload: [], params: [], batch_id: $batch_id, submitted: false, message: 'FaceList Not Found!', code: 'FL404', method: 'PUT');
                    Log::info('FaceList Not Found!');
                }
            } else {
                $this->createLogFaceRecord(employee: $employee, item: [], upload: [], params: [], batch_id: $batch_id, submitted: false, message: 'Photo not found!', code: 'PH404', method: 'PUT');
                Log::info('Photo not found!');
            }
        }

        Log::info('Upload Done ! Time : ' . Carbon::now()->diffForHumans($start), [
            'batch id' => $batch_id
        ]);
    }

    /**
     * @param Collection $rows
     * @param int $nvr_id
     * @param $batch_id
     * @return void
     */
    public function uploadFaceRecordBlockList(Collection $rows, int $nvr_id, $batch_id): void
    {
        $service = new BatchUploadService();

        $start = Carbon::now()->format('Y-m-d H:i:s');

        Log::info('start upload face record ! Time :' . $start);

        foreach ($rows as $employee) {
            $ftp = Storage::disk('ftp')->get('photo/' . $employee['photoName']);

            $company = $employee['company'];
            $statusEmployee = $employee['statusEmployee'];
            $fullName = preg_replace('/[^A-Za-z0-9\-\s]/', '', $employee['name']);

            if ($ftp) {
                $ftpImage = Image::make($ftp)->resize(null, 500, function ($constraint) {
                    $constraint->aspectRatio();
                });

                $base64 = preg_replace('#^data:image/\w+;base64,#i', '', $ftpImage->encode('data-url'));

                $photoName = $employee['photoName'];

                $faceList = $this->getFaceList($nvr_id, $company, $statusEmployee);

                if ($faceList) {
                    foreach ($faceList as $index => $item) {
                        $nvr = Nvr::find($item->nvr_id);
                        $service->validateNvrCookie($nvr->id);
                        $nvrName = str_replace(' ', '_', $nvr->name);
                        $cookieNvr = $this->getConfigNvr($nvrName, 'cookie ' . $nvrName);

                        Log::info('Cookie ' . $nvrName . ' = ' . $cookieNvr);
                        // upload face record
                        $this->processUploadFaceRecord($index, $fullName, $employee, $base64, $cookieNvr, $nvr, $item, $batch_id, $photoName, 'DELETE');
                    }
                } else {
                    $this->createLogFaceRecord(employee: $employee, item: [], upload: [], params: [], batch_id: $batch_id, submitted: false, message: 'FaceList Not Found!', code: 'FL404', method: 'DELETE');
                }
            } else {
                Log::info('Photo not found!');
                $this->createLogFaceRecord(employee: $employee, item: [], upload: [], params: [], batch_id: $batch_id, submitted: false, message: 'Photo not found!', code: 'PH404', method: 'DELETE');
            }

        }

        Log::info('Upload Done ! Time : ' . Carbon::now()->diffForHumans($start), [
            'batch id' => $batch_id
        ]);
    }

    /**
     * @param int $index
     * @param string $fullName
     * @param array $employee
     * @param string $base64
     * @param string $cookieNvr
     * @param Nvr $nvr
     * @param $item
     * @param $batch_id
     * @return void
     */
    protected function processUploadFaceRecord(int $index, string $fullName, array $employee, string $base64, string $cookieNvr, Nvr $nvr, $item, $batch_id, $photoName, $method): void
    {
        $params = [
            'peopleList' => [
                'index' => $index,
                'name' => preg_replace('/-+/', '', $fullName),
                'credentialNumber' => $employee['credentialNumber'],
                'credentialType' => $employee['credentialType'],
                'gender' => $employee['gender'],
                'bornTime' => $employee['bornTime'],
                'country' => $employee['country'],
                'occupation' => $employee['occupation'],
                'description' => $employee['description'],
                'strId' => $employee['strId'],
                'pictures' => [$base64]
            ]
        ];

        $paramSearch = [
            'credential_number' => strval($employee['credentialNumber'])
        ];

        $paramPerson = [
            'credential_type' => strval($employee['credentialType']),
            'name' => preg_replace('/-+/', '', $fullName),
            'company' => $employee['company'],
            'department' => $employee['description'],
            'photo_name' => $photoName,
            'status' => $employee['statusEmployee'],
        ];

        $upload = Http::withoutVerifying()
            ->withOptions(['verify' => false])
            ->withHeaders([
                'Accept' => 'application/json',
                'Cookie' => 'JSESSIONID=' . $cookieNvr,
            ])
            ->post($nvr->ip . '/sdk_service/rest/facerepositories/' . $item->face_list_id . '/peoples', $params);

        $code = $upload->collect()['resultCode'];
        $message = $upload->collect()['resultMsg'];
        
        Persons::updateOrCreate($paramSearch, $paramPerson);

        Log_transaction::create([
            'credential_number' => $employee['credentialNumber'],
            'credential_type' => $employee['credentialType'],
            'name' => $employee['name'],
            'company' => $employee['company'],
            'type' => 'HUAWEI',                        // HUAWEI & ZKBIO
            'nvr_id' => $nvr->id,
            'face_list_id' => $item->face_list_id,
            'access_level_code' => null,
            'status' => $employee['statusEmployee'],
            'application_id' => $employee['application_id'],
            'service_code' => $code,
            'service_message' => $message,
            'service_payload' => '',
            'batch_id' => $batch_id,
            'access_level_name' => null,
            'log_type' => 'PERSON',
            'method' => $method,
        ]);

        Log::info('Result from upload face record to NVR: ' . $nvr->name, [
            'json' => $upload->json(),
        ]);
    }

    /**
     * @param $rows
     * @param int $nvr_id
     * @param $batch_id
     * @return void
     */
    public function deleteFaceRecord(int $index, string $fullName, array $employee, string $base64, string $cookieNvr, Nvr $nvr, $item, $batch_id): void
    {

        $batchUploadService = new BatchUploadService();
        $paramsPeople = [
            'groupids' => $item->face_list_id,
            'credentialNumber' => $employee['credentialNumber'],
            'page' =>
                [
                    "no" => "1",
                    "size" => "1",
                    "sort" => "asc",
                    "orderName" => "name"
                ]
        ];

        $batchUploadService->validateNvrCookie($nvr->id);
        $nvrName = str_replace(' ', '_', $nvr->name);
        $cookieNvr = $batchUploadService->getConfigNvr($nvrName, 'cookie ' . $nvrName);

        $detailPeople = Http::withoutVerifying()
            ->withOptions(['verify' => false])
            ->withHeaders([
                'Accept' => 'application/json',
                'Cookie' => 'JSESSIONID=' . $cookieNvr,
            ])
            ->post($nvr->ip . '/sdk_service/rest/facerepositories/peoples', $paramsPeople);

        $data = json_decode($detailPeople, true);
        Log::info('Delete Data Ready ' . $nvr->name);


        if(isset($data['peopleList'])){
            $people = $data['peopleList'];
            $collection = collect($people);
            $paramsId = json_encode($collection->pluck('peopleId'));
            $peopleIds = json_decode($paramsId, true)[0];
            $url = $nvr->ip . '/sdk_service/rest/facerepositories/' . $item->face_list_id . '/peoples?ids=' . $peopleIds . '&credentialnumbers=' . $employee['credentialNumber'];

            Http::withoutVerifying()
                ->withOptions(['verify' => false])
                ->withHeaders([
                    'Accept' => 'application/json',
                    'Cookie' => 'JSESSIONID=' . $cookieNvr,
                ])->delete($url);
        }
    }

    /**
     * @param int $index
     * @param string $fullName
     * @param array $employee
     * @param string $base64
     * @param string $cookieNvr
     * @param Nvr $nvr
     * @param $item
     * @param $batch_id
     * @return void
     */
    protected function processEditFaceRecord(int $index, string $fullName, array $employee, string $base64, string $cookieNvr, Nvr $nvr, $item, $batch_id): void
    {
        $params = [
            'people' => [
                'index' => $index,
                'name' => preg_replace('/-+/', '', $fullName),
                'credentialNumber' => $employee['credentialNumber'],
                'credentialType' => $employee['credentialType'],
                'gender' => $employee['gender'],
                'bornTime' => $employee['bornTime'],
                'country' => $employee['country'],
                'occupation' => $employee['occupation'],
                'description' => $employee['description'],
                'strId' => $employee['strId'],
                'pictures' => [$base64]
            ]
        ];

        $paramsPeople = [
            'groupids' => $item->face_list_id,
            'credentialNumber' => $employee['credentialNumber'],
            'page' => [
                "no" => "1",
                "size" => "1",
                "sort" => "asc",
                "orderName" => "name"
            ]
        ];

        $detailPeople = Http::withoutVerifying()
            ->withOptions(['verify' => false])
            ->withHeaders([
                'Accept' => 'application/json',
                'Cookie' => 'JSESSIONID=' . $cookieNvr,
            ])
            ->post($nvr->ip . '/sdk_service/rest/facerepositories/peoples', $paramsPeople);

        $data = json_decode($detailPeople, true);

        if(isset($data['peopleList'])){
            $people = $data['peopleList'];
            $collection = collect($people);
            $paramsId = json_encode($collection->pluck('peopleId'));
            $peopleIds = json_decode($paramsId, true)[0];

            Log::info('Result People NVR: ' . $nvr->name, [
                'param' => $peopleIds,
                'json' => $detailPeople->json(),
            ]);

            $upload = Http::withoutVerifying()
                ->withOptions(['verify' => false])
                ->withHeaders([
                    'Accept' => 'application/json',
                    'Cookie' => 'JSESSIONID=' . $cookieNvr,
                ])
                ->patch($nvr->ip . '/sdk_service/rest/facerepositories/' . $item->face_list_id . '/peoples' . '/' . $peopleIds, $params);

            $this->createLogEditFaceRecord(employee: $employee, item: $item, upload: $upload, params: $params, batch_id: $batch_id, submitted: true, message: '', nvr: $nvr, method: 'PUT');

            Log::info('Result from upload face record to NVR: ' . $nvr->name, [
                'json' => $upload->json(),
            ]);
        }
    }

    /**
     * @param int $index
     * @param string $fullName
     * @param array $employee
     * @param string $base64
     * @param string $cookieNvr
     * @param Nvr $nvr
     * @param $item
     * @param $batch_id
     * @return void
     */
    protected function processChangePhotoFaceRecord(int $index, string $fullName, array $employee, string $base64, string $cookieNvr, Nvr $nvr, $item, $batch_id): void
    {
        $params = [
            'people' => [
                'index' => $index,
                'name' => preg_replace('/-+/', '', $fullName),
                'credentialNumber' => $employee['credentialNumber'],
                'credentialType' => $employee['credentialType'],
                'pictures' => [$base64]
            ]
        ];

        $paramsPeople = [
            'groupids' => $item->face_list_id,
            'credentialNumber' => $employee['credentialNumber'],
            'page' => [
                "no" => "1",
                "size" => "1",
                "sort" => "asc",
                "orderName" => "name"
            ]
        ];

        $detailPeople = Http::withoutVerifying()
            ->withOptions(['verify' => false])
            ->withHeaders([
                'Accept' => 'application/json',
                'Cookie' => 'JSESSIONID=' . $cookieNvr,
            ])
            ->post($nvr->ip . '/sdk_service/rest/facerepositories/peoples', $paramsPeople);

        $data = json_decode($detailPeople, true);

        $people = $data['peopleList'];
        $collection = collect($people);
        $paramsId = json_encode($collection->pluck('peopleId'));
        $peopleIds = json_decode($paramsId, true)[0];

        Log::info('Result People NVR: ' . $nvr->name, [
            'param' => $peopleIds,
            'json' => $detailPeople->json(),
        ]);

        $upload = Http::withoutVerifying()
            ->withOptions(['verify' => false])
            ->withHeaders([
                'Accept' => 'application/json',
                'Cookie' => 'JSESSIONID=' . $cookieNvr,
            ])
            ->patch($nvr->ip . '/sdk_service/rest/facerepositories/' . $item->face_list_id . '/peoples' . '/' . $peopleIds, $params);

        $this->createLogEditFaceRecord(employee: $employee, item: $item, upload: $upload, params: $params, batch_id: $batch_id, submitted: true, message: '', nvr: $nvr, method: 'PUT');

        Log::info('Result from upload face record to NVR: ' . $nvr->name, [
            'json' => $upload->json(),
        ]);
    }

    /**
     * @param array $employee
     * @param $item
     * @param $upload
     * @param $params
     * @param bool $submitted
     * @param string $message
     * @param $batch_id
     * @return void
     */
    protected function createLogFaceRecord(array $employee, $item, $upload, $params, bool $submitted = true, string $message = '', $code, $batch_id, $method): void
    {
        if (!$submitted) {
            $code = $code;
            $params = '';
        } else {
            $code = $upload->collect()['resultCode'];
            $message = $upload->collect()['resultMsg'];
            $params = json_encode(collect($params['peopleList'])->except(['pictures']));
        }

        $fullName = preg_replace('/[^A-Za-z0-9\-\s]/', '', $employee['name']);
        $photoName = $employee['photoName'];
        $paramSearch = [
            'credential_number' => strval($employee['credentialNumber'])
        ];

        $paramPerson = [
            'credential_type' =>strval($employee['credentialType']),
            'name' => preg_replace('/-+/', '', $fullName),
            'company' => $employee['company'],
            'department' => $employee['description'],
            'photo_name' => $photoName,
            'status' => $employee['statusEmployee'],
        ];

        Persons::updateOrCreate($paramSearch, $paramPerson);

        $log = Log_transaction::create([
            'credential_number' => strval($employee['credentialNumber']),
            'credential_type' => $employee['credentialType'],
            'name' => $employee['name'],
            'company' => $employee['company'],
            'type' => 'HUAWEI',
            'nvr_id' => '',
            'face_list_id' => '',
            'access_level_code' => null,
            'status' => $employee['statusEmployee'],
            'application_id' => $employee['application_id'],
            'service_code' => $code,
            'service_message' => $message,
            'service_payload' => '',
            'batch_id' => $batch_id,
            'access_level_name' => null,
            'log_type' => 'PERSON',
            'method' => $method,
        ]);

        Log::info('Create Log HUAWEI for user ' . $employee['credentialNumber'] . ' with ID ' . $log->id);
    }

    /**
     * @param array $employee
     * @param $item
     * @param $upload
     * @param $params
     * @param bool $submitted
     * @param string $message
     * @param $batch_id
     * @return void
     */
    protected function createLogEditFaceRecord(array $employee, $item, $upload, $params, $batch_id, bool $submitted = true, string $message = '', $nvr, $method): void
    {
        if (!$submitted) {
            $code = '';
            $message = '';
        } else {
            $code = $upload->collect()['resultCode'];
            $message = $upload->collect()['resultMsg'];
        }

        $paramSearch = [
            'credential_number' => strval($employee['credentialNumber'])
        ];

        $fullName = preg_replace('/[^A-Za-z0-9\-\s]/', '', $employee['name']);
        $photoName = $employee['photoName'];

        $paramPerson = [
            'credential_type' => strval($employee['credentialType']),
            'name' => preg_replace('/-+/', '', $fullName),
            'company' => $employee['company'],
            'department' => $employee['description'],
            'photo_name' => $photoName,
            'status' => 1,
        ];

        Persons::updateOrCreate($paramSearch, $paramPerson);

        $log = Log_transaction::create([
            'credential_number' => strval($employee['credentialNumber']),
            'credential_type' => $employee['credentialType'],
            'name' => $employee['name'],
            'company' => $employee['company'],
            'type' => 'HUAWEI',
            'nvr_id' => $nvr->id,
            'face_list_id' => $item->face_list_id,
            'access_level_code' => null,
            'status' => $employee['statusEmployee'],
            'application_id' => $employee['application_id'],
            'service_code' => $code,
            'service_message' => $message,
            'service_payload' => '',
            'batch_id' => $batch_id,
            'access_level_name' => null,
            'log_type' => 'PERSON',
            'method' => $method,
        ]);

        Log::info('Create Log HUAWEI for user ' . $employee['credentialNumber'] . ' with ID ' . $log->id);

    }

    /**
     * @param $pid
     * @param $accessToken
     * @return \GuzzleHttp\Promise\PromiseInterface|\Illuminate\Http\Client\Response
     */
    public function getPerson($pid, $accessToken): \GuzzleHttp\Promise\PromiseInterface|\Illuminate\Http\Client\Response
    {
        $urlGetPerson = Setting::getSetting('UrlGetPersonZkbio');
        return Http::withoutVerifying()
            ->withOptions(['verify' => false])
            ->withHeaders([
                'Content-Type' => 'application/json',
            ])
            ->get($urlGetPerson . $pid . '?access_token=' . $accessToken);
    }

    /**
     * @param array $row
     * @return array|string|null
     */
    public function getPhoto(array $row): array|string|null
    {
        $base64 = '';
        $ftp = Storage::disk('ftp')->get('photo/' . $row['photoName']);
        if ($ftp) {
            $ftpImage = Image::make($ftp)->resize(null, 500, function ($constraint) {
                $constraint->aspectRatio();
            });
            $base64 = preg_replace('#^data:image/\w+;base64,#i', '', $ftpImage->encode('data-url'));
        }

        return $base64;

    }

    /**
     * @param array $row
     * @param string $accessType
     * @return array
     */
    public function getDepartmentCode(array $row, string $accessType = 'GENERAL'): array
    {
        $org = Organization::where('name', $row['company'])->first();

        if(isset($org)){
            $org_group = $org->organization_group;
            $org_type = $org->organization_type;
    
            Log::info('Check Group ' . $org_group . ' - ' . $org_type);
    
            Log::info('comp = ' . $row['company'] . 'org = ' . $org . '');
    
            $data_emp = array(
                'workLocation' => $row['work_location'],
                'company' => $org->alias,
                'dept' => $row['dept_name'],
                'organization_group' => $org_group,
                'compType' => $org_type,
                'accessType' => $accessType,
            );
    
            $departmentCode = new DepartmentCode();
            $deptCode = $departmentCode->deptCode($data_emp, 3);
    
            Log::info('Dept Code :' . $deptCode['deptCode'] . ' - ' . $deptCode['deptName']);
    
            return [
                'deptCode' => $deptCode['deptCode'],
                'deptName' => $deptCode['deptName'],
            ];
        }else{
           return [NULL];
        }
    }

    /**
     * @param string $accessToken
     * @param string $pid
     * @param string $code_depart
     * @param string $name_depart
     * @param string $data_name
     * @param $data_last_name
     * @param array $row
     * @param string $new_data_level
     * @param string $batch_id
     * @return void
     */
    public function processUploadAccessLevel(
        string $accessToken,
        string $pid,
        string $code_depart,
        string $name_depart,
        string $data_name,
        $data_last_name,
        array $row,
        string $new_data_level,
        string $batch_id,
        $personPhoto,
        $photoName,
        string $status = "store",
        array $extraParams = []
    ): void {
        $params = [
            'pin' => $pid,
            'deptCode' => $code_depart,
            'deptName' => $name_depart,
            'name' => $data_name,
            'lastName' => $data_last_name,
            'birthday' => $row['bornTime'],
            'gender' => $row['gender'],
            'cardNo' => NULL,
            'accLevelIds' => $new_data_level,
            'personPhoto' => preg_replace('#^data:image/\w+;base64,#i', '', $personPhoto),
        ];

        if ($status == "simper") {
            $params = array_merge($params, [
                'cardNo' => $extraParams["cardNumber"],
                'accStartTime' => $extraParams["accStartTime"],
                'accEndTime' => $extraParams["accEndTime"],
            ]);
            // info("params submit zkbio", $params);
        }

        $urlAddPerson = Setting::getSetting('UrlAddPersonZkbio');
        $upload = Http::withoutVerifying()
            ->withOptions(['verify' => false])
            ->withHeaders([
                'Content-Type' => 'application/json',
            ])
            ->post($urlAddPerson . '?access_token=' . $accessToken, $params);


        if (isset($personPhoto) or $personPhoto != '') {
            $urlAddPerson = Setting::getSetting('UrlAddPersonZkbio');
            $upload = Http::withoutVerifying()
                ->withOptions(['verify' => false])
                ->withHeaders([
                    'Content-Type' => 'application/json',
                ])
                ->post($urlAddPerson . '?access_token=' . $accessToken, $params);

            $code_hasil = ($upload->collect()->has('code'))?$upload->collect()['code']:(($upload->collect()->has('ret'))?$upload->collect()['ret']:-1);
            $message = ($upload->collect()->has('message'))?$upload->collect()['message']:(($upload->collect()->has('msg'))?$upload->collect()['msg']:-1);
            $submitted = true;
            if($code_hasil != 0){
                $submitted = false;
            }

            Log::info('Result from upload access level zkbio on processUploadAccessLevel ', [
                'params' => $params,
                'json' => $upload->json(),
                'url' => $urlAddPerson
            ]);
            

            $this->createLogAccessLevel(employee: $row, new_data_level: $new_data_level, upload: $upload, params: $params, batch_id: $batch_id, submitted: $submitted, message: '', code: '', photoName: $photoName, method: 'POST');
        } else {
            $this->createLogAccessLevel(employee: $row, new_data_level: "", upload: '', params: $params, batch_id: $batch_id, submitted: true, message: 'Photo not found!', code: 'PH404', photoName: $photoName, method: 'POST');
            Log::info('Photo not found!');
        }
    }

    /**
     * @param array $rows
     * @param string $batch_id
     * @return void
     */
    public function processUpdateAccessLevel(array $rows, $batch_id)
    {

        $access_token = Setting::getSetting('AccessTokenZkbio');
        $setResponseUserCode = $this->getPerson($rows['credentialNumber'], $access_token)->json();

        if ($rows['is_leave'] == true) {
            $params = [
                'pin' => $rows['credentialNumber'],
                'leaveDate' => $rows['leave_date'],
                'leaveType' => '2',
            ];

            $urlLeavePerson = Setting::getSetting('UrlLeavePersonZkbio');
            $upload = Http::withoutVerifying()
                ->withOptions(['verify' => false])
                ->withHeaders([
                    'Content-Type' => 'application/json',
                ])
                ->post($urlLeavePerson . '?access_token=' . $access_token, $params);

            Log::info('Result from update access level zkbio', [
                'json' => $upload->json(),
                'url' => $urlLeavePerson
            ]);
        } else {
            $new_data_level = $rows['access_level_code'];
            if ($rows['is_disabled'] == true) {
                $params = [
                    'pin' => $rows['credentialNumber'],
                    'accLevelIds' => $new_data_level,
                    'accStartTime' => $rows['start_time'],
                    'accEndTime' => $rows['end_time'],
                    'isDisabled' => $rows['is_disabled'],
                    'birthday' => $rows['birthday']
                ];
            } else {
                $params = [
                    'pin' => $rows['credentialNumber'],
                    'accLevelIds' => $new_data_level,
                    'accStartTime' => '',
                    'accEndTime' => '',
                    'isDisabled' => false,
                    'birthday' => $rows['birthday']
                ];
            }

            $urlAddPerson = Setting::getSetting('UrlAddPersonZkbio');
            $upload = Http::withoutVerifying()
                ->withOptions(['verify' => false])
                ->withHeaders([
                    'Content-Type' => 'application/json',
                ])
                ->post($urlAddPerson . '?access_token=' . $access_token, $params);

            Log::info('Result from update access level zkbio', [
                'json' => $upload->json(),
                'url' => $urlAddPerson
            ]);
        }
        $this->createLogAccessLevel(employee: $rows, new_data_level: $rows['access_level_code'], upload: $upload, params: $params, batch_id: $batch_id, submitted: true, message: '', method: 'PUT');
    }


    /**
     * @param array $rows
     * @param string $batch_id
     * @return void
     */
    public function processDeleteAccessLevel(array $rows, $batch_id)
    {
        Log::info($rows);
        $params = null;
        $access_token = Setting::getSetting('AccessTokenZkbio');
        $urlDeletePerson = Setting::getSetting('UrlDeletePersonZkbio');

        $upload = Http::withoutVerifying()
            ->withOptions(['verify' => false])
            ->withHeaders([
                'Content-Type' => 'application/json',
            ])
            ->post($urlDeletePerson . '/' . $rows['oldCredentialNumber'] . '?access_token=' . $access_token);

        Log::info('Result from update access level zkbio', [
            'json' => $upload->json(),
            'url' => $urlDeletePerson . '/' . $rows['oldCredentialNumber'] . '?access_token=' . $access_token
        ]);

        $this->createLogAccessLevel(employee: $rows, new_data_level: $rows['access_level_code'], upload: $upload, params: $params, batch_id: $batch_id, submitted: true, message: '', method: 'DELETE');
    }

    /**
     * @param array $rows
     * @param string $batch_id
     * @return void
     */
    public function processDeletePersonZkbio(array $rows, $batch_id)
    {
        $params = null;
        $access_token = Setting::getSetting('AccessTokenZkbio');
        $urlDeletePerson = Setting::getSetting('UrlDeletePersonZkbio');

        $upload = Http::withoutVerifying()
            ->withOptions(['verify' => false])
            ->withHeaders([
                'Content-Type' => 'application/json',
            ])
            ->post($urlDeletePerson . '/' . $rows['credentialNumber'] . '?access_token=' . $access_token);

        Log::info('Result from update delete person zkbio', [
            'json' => $upload->json(),
            'url' => $urlDeletePerson . '/' . $rows['credentialNumber'] . '?access_token=' . $access_token
        ]);

        $this->createLogAccessLevel(employee: $rows, new_data_level: $rows['access_level_code'], upload: $upload, params: $params, batch_id: $batch_id, submitted: true, message: '', method: 'DELETE');
    }

    /**
     * @param string $accessToken
     * @param string $pid
     * @param string $personPhoto
     * @param string $data_name
     * @return void
     */
    protected function updatePersonnel(string $accessToken, string $pid, string $personPhoto, string $data_name): void
    {
        $urlUpdatePersonnel = Setting::getSetting('UrlUpdatePersonnelZkbio');
        $uploadPhoto = Http::withoutVerifying()
            ->withOptions(['verify' => false])
            ->withHeaders([
                'Content-Type' => 'application/json',
            ])
            ->post($urlUpdatePersonnel . '?access_token=' . $accessToken, [
                'pin' => $pid,
                'personPhoto' => $personPhoto,
            ]);
        Log::info('result from upload photo personnel ' . $data_name . ' (' . $pid . ')', [
            'body' => $uploadPhoto->body(),
            'json' => $uploadPhoto->json(),
        ]);
    }

    /**
     * @param Collection $rows
     * @param string $batch_id
     * @param Applications $applications
     * @return void
     */
    public function uploadAccessLevel(Collection $rows, string $batch_id, Applications $applications): void
    {
        $start = Carbon::now()->format('Y-m-d H:i:s');

        Log::info('start upload person zkbio, time: ' . $start);
        $data_level = [];
        foreach ($rows as $row) {
            Log::info('Upload person ' . $row['strId'] . ' dengan jenis kelamin ' . $row['gender'] . ' to zkbio ');

            $pid = $row['strId'];
            $accessToken = Setting::getSetting('AccessTokenZkbio');

            /* Cek if Exist */
            $setResponseUserCode = $this->getPerson($pid, $accessToken)->json();

            if (isset($setResponseUserCode['data']['accLevelIds'])) {
                $data_level = $setResponseUserCode['data']['accLevelIds'];
                $new_data_level = $data_level . ',' . $row['access_level_code'];
            } else {
                $new_data_level = $row['access_level_code'];
            }

            /* Data Name */
            if (isset($setResponseUserCode['data']['name'])) {
                $data_name = $setResponseUserCode['data']['name'];
                $data_last_name = $setResponseUserCode['data']['lastName'];
            } else {
                $data_name = $row['name'];
                $data_last_name = '';
            }

            /* Get Photo */
            if (isset($row['base64'])) {
                $base64 = $row['base64'];
            } else {
                $photo = $this->getPhoto($row);
                $base64 = $photo;
            }

            $photoName = $row['photoName'];
            // Log::info('Upload person ' . $row['strId'] . ' dengan photoname ' . $photoName . ' to zkbio ');

            /* Emp Data */
            $deptCode = $this->getDepartmentCode((array) $row, $row['access_type']);
            if (is_array($deptCode) && array_values($deptCode) !== [null]) {
                $code_depart = $deptCode['deptCode'];
                $name_depart = $deptCode['deptName'];
    
                $personPhoto = $base64;
    
                Log::info('Department Name ' . $name_depart . ' (' . $code_depart . ') to zkbio ');
                $this->processUploadAccessLevel($accessToken, $pid, $code_depart, $name_depart, $data_name, $data_last_name, $row, $new_data_level, $batch_id, $personPhoto, $photoName);
                $this->updatePersonnel($accessToken, $pid, $personPhoto, $data_name);

            }else{
                $this->createLogAccessLevel(employee: $row, new_data_level: "", upload: '', params: NULL, batch_id: $batch_id, submitted: false, message: 'Company not found!', code: 'COMP404', photoName: $photoName, method: 'POST');
            }
           
        }

        Log::info('Upload person zkbio Done ! Time : ' . Carbon::now()->diffForHumans($start));
    }

    /**
     * @param Collection $rows
     * @param string $batch_id
     * @param Applications $applications
     * @return void
     */
    public function processLeaveZkbio(Collection $rows, string $batch_id, Applications $applications): void
    {
        $start = Carbon::now()->format('Y-m-d H:i:s');
        $date = Carbon::now()->format('Y-m-d');

        Log::info('start upload person zkbio, time: ' . $start);
        $data_level = [];
        foreach ($rows as $row) {

            $accessToken = Setting::getSetting('AccessTokenZkbio');

            $params = [
                'pin' => $row['credentialNumber'],
                'leaveDate' => $date,
                'leaveType' => 2,
            ];

            $urlLeavePerson = Setting::getSetting('UrlLeavePersonZkbio');
            $upload = Http::withoutVerifying()
                ->withOptions(['verify' => false])
                ->withHeaders([
                    'Content-Type' => 'application/json',
                ])
                ->post($urlLeavePerson . '?access_token=' . $accessToken, $params);

            Log::info('Result from update access level zkbio', [
                'json' => $upload->json(),
                'url' => $urlLeavePerson
            ]);

            $this->createLogAccessLevel(employee: $row, new_data_level: "", upload: $upload, params: $params, batch_id: $batch_id, submitted: true, message: '', method: 'DELETE');

        }

        Log::info('Upload person zkbio Done ! Time : ' . Carbon::now()->diffForHumans($start));
    }

    /**
     * @param Collection $rows
     * @param string $batch_id
     * @param Applications $applications
     * @return void
     */
    public function processUpdateZkbio(Collection $rows, string $batch_id, Applications $applications): void
    {
        $start = Carbon::now()->format('Y-m-d H:i:s');
        $accessToken = Setting::getSetting('AccessTokenZkbio');
        foreach ($rows as $row) {
            $pid = $row['strId'];
            $Services = new BatchUploadZkbioService();
            $responseUser = $Services->getPerson($pid, $accessToken);
            $biometric = '';
            $setResponseUserCode = $responseUser->json();
            /* Delete Person */
            if (isset($setResponseUserCode['data']['accLevelIds'])) {
                $biometric = $setResponseUserCode['data']['personPhoto'];
                $urlDeletePerson = Setting::getSetting('UrlDeletePersonZkbio');
                Log::info('Data delete ' . $row['strId'], [
                    'data' => $row,
                    'url' => $urlDeletePerson
                ]);
                $upload = Http::withoutVerifying()
                    ->withOptions(['verify' => false])
                    ->withHeaders([
                        'Content-Type' => 'application/json',
                    ])
                    ->post($urlDeletePerson . '/' . $row['strId'] . '?access_token=' . $accessToken);
    
                Log::info('Result from delete person '.$row['strId'], [
                    'json' => $upload->json(),
                    'url' => $urlDeletePerson . '/' .$row['strId'] . '?access_token=' . $accessToken,
                ]);
            }


            /* Upload Person */
            $data_name = $row['name'];
            $data_last_name = '';
            $deptCode = $this->getDepartmentCode((array) $row, $row['access_type']);
            if (is_array($deptCode) && array_values($deptCode) !== [null]) {
                $code_depart = $deptCode['deptCode'];
                $name_depart = $deptCode['deptName'];
    
                if (isset($row['base64'])) {
                    $base64 = $row['base64'];
                } else {
                    $photo = $this->getPhoto($row);
                    $base64 = $photo;
                }

                if($biometric == ''){
                    $biometric = $base64;
                }

                $new_data_level = $row['access_level_code'];
                $params = [
                    'pin' => $pid,
                    'deptCode' => $code_depart,
                    'deptName' => $name_depart,
                    'name' => $data_name,
                    'lastName' => $data_last_name,
                    'birthday' => $row['bornTime'],
                    'gender' => $row['gender'],
                    'cardNo' => NULL,
                ];

                $urlAddPerson = Setting::getSetting('UrlAddPersonZkbio');
                $upload = Http::withoutVerifying()
                    ->withOptions(['verify' => false])
                    ->withHeaders([
                        'Content-Type' => 'application/json',
                    ])
                    ->post($urlAddPerson . '?access_token=' . $accessToken, $params);
    
                Log::info('Result from upload person zkbio ' . $row['strId'], [
                    'json' => $upload->json(),
                    'url' => $urlAddPerson
                ]);

                $this->createLogAccessLevel(employee: $row, new_data_level: '', upload: $upload, params: $params, batch_id: $batch_id, submitted: true, message: '', method: 'PUT');
            }else{
                $this->createLogAccessLevel(employee: $row, new_data_level: '', upload: '', params: NULL, batch_id: $batch_id, submitted: true, message: 'Company not found!', code: 'COMP404', method: 'PUT');
            }

        }
    }

    public function processMutasiZkbio(Collection $rows, string $batch_id, Applications $applications): void
    {
        Artisan::call('cache:clear');
        Artisan::call('config:cache');
        $start = Carbon::now()->format('Y-m-d H:i:s');
        $accessToken = Setting::getSetting('AccessTokenZkbio');
        foreach ($rows as $row) {
            $pid = $row['strId'];
            $Services = new BatchUploadZkbioService();
            $responseUser = $Services->getPerson($pid, $accessToken);
            $biometric = '';
            $setResponseUserCode = $responseUser->json();
            $new_ki_lvl = "";
            /* Delete Person */
            if (isset($setResponseUserCode['data']['pin'])) {
                /* Cek level KI */
                // $array_ki = array('40286a929448da770194d9c7a8ae15ed','40286a929448da770194d9cc84d915ef','40286a929448da770194d9cca24415f1');
                $array_ki = array('40286aee890f6c9301894ccb42320dd4','40286aee890f6c9301894ccb9e1e0ddb','40286aee9283a4440192eab975b5067a');
                $existing_acclvl = $setResponseUserCode['data']['accLevelIds'];
                if($existing_acclvl != NULL){
                    Log::info('Ada existing_acclvl ' . $row['strId']);
                    $array_acclvl = explode(",", $existing_acclvl);
                
                    $index = 0;
                    foreach($array_ki as $value){
                        foreach($array_acclvl as $value_det){
                            if($value == $value_det){
                                if($index == 0){
                                $new_ki_lvl = $value;
                                }else{
                                $new_ki_lvl = $value_det.",".$value;
                                }
                                $index++;
                            }
                        }
                    }
                }

                $biometric = $setResponseUserCode['data']['personPhoto'];
                $urlDeletePerson = Setting::getSetting('UrlDeletePersonZkbio');
                Log::info('Data delete ' . $row['strId'], [
                    'data' => $row,
                    'url' => $urlDeletePerson
                ]);
                $upload = Http::withoutVerifying()
                    ->withOptions(['verify' => false])
                    ->withHeaders([
                        'Content-Type' => 'application/json',
                    ])
                    ->post($urlDeletePerson . '/' . $row['strId'] . '?access_token=' . $accessToken);
    
                Log::info('Result from delete person '.$row['strId'], [
                    'json' => $upload->json(),
                    'url' => $urlDeletePerson . '/' .$row['strId'] . '?access_token=' . $accessToken,
                ]);
            }


            /* Upload Person */
            $data_name = $row['name'];
            $data_last_name = '';
            $deptCode = $this->getDepartmentCode((array) $row, $row['access_type']);
            if (is_array($deptCode) && array_values($deptCode) !== [null]) {
                $code_depart = $deptCode['deptCode'];
                $name_depart = $deptCode['deptName'];
    
                if (isset($row['base64'])) {
                    $base64 = $row['base64'];
                } else {
                    $photo = $this->getPhoto($row);
                    $base64 = $photo;
                }

                if($biometric == ''){
                    $biometric = $base64;
                }

                $new_data_level = $row['access_level_code'];
                Log::info('Data access_level hasil mapping ' . $row['strId'], [
                    'mapping_data_level' => $new_data_level,
                ]);
                if($new_ki_lvl != ""){
                    $new_data_level = $new_data_level.",".$new_ki_lvl;
                }
                Log::info('Data access_level terbaru ' . $row['strId'], [
                    'new_data_level' => $new_data_level,
                ]);
                $params = [
                    'pin' => $pid,
                    'deptCode' => $code_depart,
                    'deptName' => $name_depart,
                    'name' => $data_name,
                    'lastName' => $data_last_name,
                    'birthday' => $row['bornTime'],
                    'gender' => $row['gender'],
                    'cardNo' => NULL,
                    'accLevelIds' => $new_data_level,
                    'personPhoto' => $biometric,
                ];

                $urlAddPerson = Setting::getSetting('UrlAddPersonZkbio');
                $upload = Http::withoutVerifying()
                    ->withOptions(['verify' => false])
                    ->withHeaders([
                        'Content-Type' => 'application/json',
                    ])
                    ->post($urlAddPerson . '?access_token=' . $accessToken, $params);
    
                Log::info('Result from upload person zkbio ' . $row['strId'], [
                    'json' => $upload->json(),
                    'url' => $urlAddPerson
                ]);

                $this->createLogAccessLevel(employee: $row, new_data_level: $new_data_level, upload: $upload, params: $params, batch_id: $batch_id, submitted: true, message: '', method: 'PUT');
            }else{
                $this->createLogAccessLevel(employee: $row, new_data_level: '', upload: '', params: NULL, batch_id: $batch_id, submitted: true, message: 'Company not found!', code: 'COMP404', method: 'PUT');
            }

        }
    }

    /**
     * @param Collection $rows
     * @param string $batch_id
     * @param Applications $applications
     * @return void
     */
    public function processChangePhotoZkbio(Collection $rows, string $batch_id, Applications $applications): void
    {
        $start = Carbon::now()->format('Y-m-d H:i:s');
        $accessToken = Setting::getSetting('AccessTokenZkbio');
        foreach ($rows as $row) {
            $identifyNumber = $row['credentialNumber'];
            $data_name = $row['name'];
            $data_last_name = '';
            $deptCode = $this->getDepartmentCode((array) $row, $row['access_type']);
            if (is_array($deptCode) && array_values($deptCode) !== [null]) {
                $code_depart = $deptCode['deptCode'];
                $name_depart = $deptCode['deptName'];
    
                if (isset($row['base64'])) {
                    $base64 = $row['base64'];
                } else {
                    $photo = $this->getPhoto($row);
                    $base64 = $photo;
                }
    
                $params = [
                    'pin'         => $identifyNumber,
                    'personPhoto' => $base64
                ];
                $urlAddPerson = Setting::getSetting('UrlAddPersonZkbio');
                $upload = Http::withoutVerifying()
                    ->withOptions(['verify' => false])
                    ->withHeaders([
                        'Content-Type' => 'application/json',
                    ])
                    ->post($urlAddPerson . '?access_token=' . $accessToken, $params);
    
                Log::info('Result from upload access level zkbio', [
                    'json' => $upload->json(),
                    'url' => $urlAddPerson
                ]);
    
    
                $this->createLogChangePhoto(employee: $row, new_data_level: '', upload: $upload, params: $params, batch_id: $batch_id, submitted: true, message: '', method: 'PUT');
            }else{
                $this->createLogAccessLevel(employee: $row, new_data_level: '', upload: '', params: NULL, batch_id: $batch_id, submitted: true, message: 'Company not found!', code: 'COMP404', method: 'PUT');
            
            }
        }
    }

    /**
     * @param array $employee
     * @param $new_data_level
     * @param $upload
     * @param $params
     * @param bool $submitted
     * @param string $message
     * @param $batch_id
     * @return void
     */
    protected function createLogAccessLevel(array $employee, $new_data_level, $upload, $params = null, bool $submitted = true, string $message = '', $batch_id, $code = '', $photoName = '', $method = ''): void
    {
        Log::info('Result from upload access level zkbio', [
            'data json upload' => $upload,
        ]);

        Log::info('Data Employee', [
            'employee' => $employee,
            'submitted' => $submitted,
        ]);
        if (!$submitted) {
            $code = $code;
            $message = $upload->collect()['message'];
        } else {
            $code = ($upload->collect()->has('code')) ? $upload->collect()['code'] : (
                ($upload->collect()->has('ret')) ? $upload->collect()['ret'] : -1
            );
            $message = $upload->collect()['message'];
        }

        $paramSearch = [
            'credential_number' => strval($employee['credentialNumber'])
        ];

        $paramPerson = [
            'name' => preg_replace('/-+/', '', $employee['name']),
            'company' => $employee['company'],
            'department' => (isset($employee['department'])) ? $employee['department'] : '',
            'credential_type' => $employee['credentialType'],
            'photo' => $photoName,
            'status' => (isset($employee['statusEmployee'])) ? $employee['statusEmployee'] : '',
        ];

        Persons::updateOrCreate($paramSearch, $paramPerson);

        if($new_data_level == ''){
            $new_data_level = $employee['access_level_code'];
        }

        $log_type = 'PERSON';
        if(isset($employee['log_type'])){
            $log_type = $employee['log_type'];
        }

        $log = Log_transaction::create([
            'credential_number' => $employee['credentialNumber'],
            'credential_type' => $employee['credentialType'],
            'name' => $employee['name'],
            'company' => $employee['company'],
            'type' => 'ZKBIO', // HUAWEI & ZKBIO
            'nvr_id' => null,
            'face_list_id' => null,
            'access_level_code' => $new_data_level,
            'status' => (isset($employee['statusEmployee'])) ? $employee['statusEmployee'] : '',
            'application_id' => (isset($employee['application_id'])) ? $employee['application_id'] : '',
            'service_code' => $code,
            'service_message' => $message,
            'service_payload' => '',
            'batch_id' => $batch_id,
            'access_level_name' => $employee['access_level_name'],
            'log_type' => $log_type,
            'method' => $method,
        ]);

        Log::info('Result log_type from upload access level zkbio', [
            'log_type' => $log_type,
            'log' => $log,
        ]);

        Log::info('Create Log Zkbio for user ' . $employee['credentialNumber'] . ' with id ' . $log->id);
    }

    /**
     * @param array $employee
     * @param $new_data_level
     * @param $upload
     * @param $params
     * @param bool $submitted
     * @param string $message
     * @param $batch_id
     * @return void
     */
    protected function createLogChangePhoto(array $employee, $new_data_level, $upload, $params = null, bool $submitted = true, string $message = '', $batch_id, $code = '', $photoName = '', $method = ''): void
    {
        if (!$submitted) {
            $code = $code;
        } else {
            $code = ($upload->collect()->has('code')) ? $upload->collect()['code'] : (
                ($upload->collect()->has('ret')) ? $upload->collect()['ret'] : -1
            );
            $message = $upload->collect()['message'];
        }

        $paramSearch = [
            'credential_number' => strval($employee['credentialNumber'])
        ];

        $paramPerson = [
            'name' => preg_replace('/-+/', '', $employee['name']),
            'company' => $employee['company'],
            'department' => $employee['department'],
            'photo' => $photoName,
            'status' => $employee['statusEmployee'],
        ];

        Persons::updateOrCreate($paramSearch, $paramPerson);

        $log = Log_transaction::create([
            'credential_number' => $employee['credentialNumber'],
            'credential_type' => $employee['credentialType'],
            'name' => $employee['name'],
            'company' => $employee['company'],
            'type' => 'ZKBIO',                         // HUAWEI & ZKBIO
            'nvr_id' => null,
            'face_list_id' => null,
            'access_level_code' => $new_data_level,
            'status' => $employee['statusEmployee'],
            'application_id' => $employee['application_id'],
            'service_code' => $code,
            'service_message' => $message,
            'service_payload' => '',
            'batch_id' => $batch_id,
            'log_type' => 'PERSON',
            'method' => $method
        ]);

        Log::info('Create Log Zkbio for user ' . $employee['credentialNumber'] . ' with id ' . $log->id);
    }
}
