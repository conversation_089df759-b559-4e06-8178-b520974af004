<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('m_mapping_dept', function (Blueprint $table) {
            $table->id();
            $table->string('department',150);
            $table->string('department_code',150);
            $table->string('nvr_id',20);
            $table->string('face_list_name');
            $table->string('face_list_id');
            $table->string('acc_level');
            $table->text('description');
            $table->smallInteger('status');
            $table->integer('created_by')->nullable();
            $table->integer('updated_by')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('mapping_dept');
    }
};
