<?php

namespace Modules\Vehicle\app\Http\Controllers;

use Carbon\Carbon;
use GuzzleHttp\Client;

use App\Models\Applications;
use Illuminate\Http\Request;
use GuzzleHttp\Promise\Utils;
use GuzzleHttp\Promise;
use App\Models\Settings\Setting;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Http;
use Modules\Vehicle\App\Models\Vehicle;
use App\Models\Settings\Log_transaction;
use Modules\Vehicle\app\Models\VehicleDetail;
use Modules\Vehicle\app\Services\TransformDataApiService;
use Modules\Vehicle\app\Http\Requests\StoreVehicleRequest;
use Modules\Vehicle\app\Http\Requests\DeleteVehicleRequest;
use Modules\Vehicle\app\Http\Requests\UpdateVehicleRequest;
use Modules\Vehicle\app\Services\ProcessDataVehicleApiService;
use Modules\Vehicle\app\Services\ProcessDeleteVehicleApiService;
use Modules\Vehicle\app\Services\ProcessUpdateVehicleApiService;

class VehicleController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('vehicle::create');
    }

     /**
     * @param StoreVehicleRequest $request
     * @return \Illuminate\Http\JsonResponse
     * @throws Throwable
     */
    public function store(StoreVehicleRequest $request): \Illuminate\Http\JsonResponse
    {
        // return response()->json($request->all());
        $application = Applications::where('token', $request->bearerToken())
            ->where('status', '1')
            ->first();

        $transform = new TransformDataApiService();

        $service = new ProcessDataVehicleApiService();
        if ($request->service == 'Huawei') {
           
        } elseif ($request->service == 'Zkbio') {
            $data = $transform->transform('Zkbio', $request->form, $application, $request->accessType);
            $batch = $service->processQueueZkbio(data: $data, application: $application);
            return response()->json([
                'message' => 'Data Face Records is currently being processed in the queue',
                'status' => 'processing',
                'worker' => [
                    [
                        'service' => 'zkbio',
                        'device' => $batch
                    ]
                ]
            ]);
        } elseif ($request->service == 'ALL') {
            $dataZkt = $transform->transform('Zkbio', $request->form, $application, $request->accessType);
            $batch1 = $service->processQueueZkbio(data: $dataZkt, application: $application);
            return response()->json([
                'message' => 'Data Persons is currently being processed in the queue',
                'status' => 'processing',
                'worker' => [
                    [
                        'service' => 'zkbio',
                        'device' => $batch1
                    ]
                ]
            ]);
        } else {
            return $this->error('Service Not Found!');
        }
    }

    /**
     * Show the specified resource.
     */
    public function show(Request $request)
    {
        $batchId = $request->batchId;
        Log::info('request', [
            'request all' => $request->all(),
            'batchId' => $request->batchId
        ]);
        $params = [];
        $application = Applications::where('token', $request->bearerToken())
            ->where('status', '1')
            ->first();


        foreach ($batchId as $id) {
            $batch = Bus::findBatch($id);

            if ($batch->finished()) {
                $logTransaction = Log_transaction::where('batch_id', $batch->id)
                    ->select(
                        "credential_number",
                        "name",
                        "company",
                        "access_level_code",
                        "access_level_name",
                        "service_code",
                        "service_message",
                        "batch_id"
                    )
                    // ->where("service_code", "=", "0")
                    ->whereApplicationId($application->id)
                    ->whereIn("type", ["HUAWEI", "ZKBIO"])
                    ->get();

                $params[] = [
                    'message' => 'success',
                    'batch_id' => $batch->id,
                    'status' => 'completed',
                    'name' => $batch->name,
                    "data" => $logTransaction
                ];
            } else {
                $logTransaction = Log_transaction::where('batch_id', $batch->id)
                    ->select(
                        "credential_number",
                        "name",
                        "company",
                        "access_level_code",
                        "access_level_name",
                        "service_code",
                        "service_message",
                        "batch_id"
                    )
                    ->where("service_code", "<>", "0")
                    ->whereApplicationId($application->id)
                    ->whereIn("type", ["HUAWEI", "ZKBIO"])
                    ->get();
                $params[] = [
                    'message' => 'success',
                    'batch_id' => $batch->id,
                    'name' => $batch->name,
                    'status' => 'error',
                    "data" => $logTransaction
                ];
            }
        }

        return response()->json($params);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($id)
    {
    }

     /**
     * @param UpdateVehicleRequest $request
     * @return \Illuminate\Http\JsonResponse
     * @throws Throwable
     */
    public function update(UpdateVehicleRequest $request): \Illuminate\Http\JsonResponse
    {
        $application = Applications::where('token', $request->bearerToken())
        ->where('status', '1')
        ->first();

        $transform = new TransformDataApiService();
        $accLevelIds = $application->access_level_code;

        $service = new ProcessUpdateVehicleApiService();
        if ($request->service == 'Huawei') {
        
        } elseif ($request->service == 'Zkbio') {

            $data = $transform->transform('Zkbio', $request->form, $application, $request->accessType);
            $batch = $service->processQueueZkbio(data: $data, application: $application);
            return response()->json([
                'message' => 'Data Face Records is currently being processed in the queue',
                'status' => 'processing',
                'worker' => [
                    [
                        'service' => 'zkbio',
                        'device' => $batch
                    ]
                ]
            ]);
        } elseif ($request->service == 'ALL') {
            $dataZkt = $transform->transform('Zkbio', $request->form, $application, $request->accessType);
            $batch1 = $service->processQueueZkbio(data: $dataZkt, application: $application);
            return response()->json([
                'message' => 'Data Persons is currently being processed in the queue',
                'status' => 'processing',
                'worker' => [
                    [
                        'service' => 'zkbio',
                        'device' => $batch1
                    ]
                ]
            ]);
        } else {
            return $this->error('Service Not Found!');
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(DeleteVehicleRequest $request): \Illuminate\Http\JsonResponse
    {
        $application = Applications::where('token', $request->bearerToken())
        ->where('status', '1')
        ->first();

        $transform = new TransformDataApiService();
        $accLevelIds = $application->access_level_code;

        $service = new ProcessDeleteVehicleApiService();
        if ($request->service == 'Huawei') {
        
        } elseif ($request->service == 'Zkbio') {

            $data = $transform->transform('ZkbioDelete', $request->form, $application, $request->accessType);
            $batch = $service->processQueueZkbio(data: $data, application: $application);
            return response()->json([
                'message' => 'Data Face Records is currently being processed in the queue',
                'status' => 'processing',
                'worker' => [
                    [
                        'service' => 'zkbio',
                        'device' => $batch
                    ]
                ]
            ]);
        } elseif ($request->service == 'ALL') {
            $dataZkt = $transform->transform('ZkbioDelete', $request->form, $application, $request->accessType);
            $batch1 = $service->processQueueZkbio(data: $dataZkt, application: $application);
            return response()->json([
                'message' => 'Data Persons is currently being processed in the queue',
                'status' => 'processing',
                'worker' => [
                    [
                        'service' => 'zkbio',
                        'device' => $batch1
                    ]
                ]
            ]);
        } else {
            return $this->error('Service Not Found!');
        }
    }


    public function getLogVehicleAll(Request $request)
    {
        $accessToken = Setting::getSetting('AccessTokenZkbio');
        $urlGetTransaction = Setting::getSetting('UrlGetTransactionZkbio');
        $client = new Client();
        
        $serialNumbers = array_map('trim', explode(',', $request->device));
        $noPage = $request->pageSize;
        $noSize = $request->noSize;

        $promises = [];
        
        foreach ($serialNumbers as $serialNumber) {
            $promises[$serialNumber] = $client->getAsync("{$urlGetTransaction}/{$serialNumber}", [
                'query' => [
                    'pageNo' => $noPage,
                    'pageSize' => $noSize,
                    'access_token' => $accessToken,
                ],
                'headers' => [
                    'Content-Type' => 'application/json',
                ],
            ]);
        }
        
        $responses = Utils::all($promises)->wait();
        $combinedData = [];

        foreach ($responses as $serialNumber => $response) {
            $data = json_decode($response->getBody()->getContents(), true);
            if (isset($data['data']['data'])) {
                $combinedData = array_merge($combinedData, $data['data']['data']);
            }
        }

        if (empty($combinedData)) {
            return response()->json([
                'message' => 'No data found',
                'service' => 'ZKBIO',
                'data'    => [],
                'total'   => 0
            ]);
        }

        $log = [];

        foreach ($combinedData as $item) {
            if ($item['pin'] == null) {
                continue;
            }

            $vehicle = VehicleDetail::where('vhc_cardnumber', $item['pin'])->first();

            if (isset($vehicle)) {
                $log[] = [
                    'pin'       => $item['pin'],
                    'name'      => $item['name'],
                    'devName'   => $item['devName'],
                    'eventTime' => $item['eventTime'],
                    'eventName' => $item['eventName'],
                    'data'  =>  [
                        'color'     => $vehicle->vhc_warna,
                        'noPolice'  => $vehicle->vhc_no_polisi,
                        'company'   => $vehicle->perusahaan->nama_perusahaan ?? $vehicle->vhc_perusahaan,
                        'noLambung' => $vehicle->vhc_no_lambung,
                        'merk'      => $vehicle->merk->merk_name,
                        'itemType'  => $vehicle->vhc_jenis_barang,
                        'area'      => $vehicle->vhc_area,
                        'expired'   => $this->getExpired($vehicle->vhc_terbit, $vehicle->vhc_berakhir)
                    ]
                ];
            } else {
                $log[] = [
                    'pin'       => $item['pin'],
                    'name'      => $item['name'],
                    'devName'   => $item['devName'],
                    'eventTime' => $item['eventTime'],
                    'eventName' => $item['eventName'],
                    'data'  =>  null
                ];
            }
        }

        return response()->json([
            'message' => 'Success',
            'service' => 'ZKBIO',
            'data'    => $log,
            'total'   => count($log)
        ]);
    }


    public function getLogVehicle(Request $request){

        $client = new Client();
        
        $accessToken       = Setting::getSetting('AccessTokenZkbio');
        $urlGetTransaction = Setting::getSetting('UrlGetTransactionZkbio');

        $serialNumber = $request->device;
        $noPage       = $request->pageSize;
        $noSize       = $request->noSize;

        $get = Http::withoutVerifying()
            ->withOptions(['verify' => false])
            ->withHeaders([
                'Content-Type' => 'application/json',
            ])
            ->get($urlGetTransaction . '/'.$serialNumber.'?pageNo='.$noPage.'&pageSize='.$noSize.'&access_token=' . $accessToken);

        $data = $get->json();
        $log  = array();

        foreach ($data['data']['data'] as $item) {

            if($item['pin'] == null){
                continue;
            }
           
           $vehicle = VehicleDetail::where('vhc_cardnumber',  $item['pin'])->first();

           if(isset($vehicle)){
            $log[] = array(
                    'pin'       => $item['pin'],
                    'name'      => $item['name'],
                    'devName'   => $item['devName'],
                    'eventTime' => $item['eventTime'],
                    'eventName' => $item['eventName'],
                    'data'  =>  [
                        'color'     => $vehicle->vhc_warna,
                        'noPolice'  => $vehicle->vhc_no_polisi,
                        'company'   => $vehicle->perusahaan->nama_perusahaan ??  $vehicle->vhc_perusahaan,
                        'noLambung' => $vehicle->vhc_no_lambung,
                        'merk'      => $vehicle->merk->merk_name,
                        'itemType'  => $vehicle->vhc_jenis_barang,
                        'area'      => $vehicle->vhc_area,
                        'expired'   => $this->getExpired($vehicle->vhc_terbit, $vehicle->vhc_berakhir)
                    ]
               );
           }else{
                $log[] = array(
                    'pin'       => $item['pin'],
                    'name'      => $item['name'],
                    'devName'   => $item['devName'],
                    'eventTime' => $item['eventTime'],
                    'eventName' => $item['eventName'],
                    'data'  =>  null
                );
           }
          
        }
        
        return response()->json([
            'message' => 'Success',
            'service' => 'ZKBIO',
            'data'    => $log,
            'total'   => $data['data']['total']
        ]);
    }

    public function getTotalVehicle(Request $request){

        $data = VehicleDetail::selectRaw("
            FORMAT(vhc_insert_date, 'MMMM yyyy') as month_year, 
            COUNT(id_vehicle) as total, 
            MONTH(vhc_insert_date) as month_num
        ")
        ->whereYear('vhc_insert_date', date('Y'))
        ->groupByRaw("FORMAT(vhc_insert_date, 'MMMM yyyy'), MONTH(vhc_insert_date)")
        ->orderBy('month_num') 
        ->get();
        
        $runningTotal = 0;
        foreach ($data as $item) {
            $runningTotal += $item->total;
            $item->running_total = $runningTotal;
        }

        return response()->json([
            'message' => 'Success',
            'data'    => $data
        ]);
    }

    private function getExpired($terbit = null, $berakhir = null){

        $vhc_terbit = $terbit;
        $vhc_berakhir = Carbon::parse($berakhir); 
        
        $today = Carbon::today();
        $oneMonthBefore = $today->copy()->addMonth();
        $oneWeekBefore = $today->copy()->addWeek();
        
        if ($vhc_berakhir->equalTo($oneMonthBefore)) {
            return "Akan expired dalam 1 bulan.";
        } elseif ($vhc_berakhir->equalTo($oneWeekBefore)) {
            return "Akan expired dalam 1 minggu.";
        } elseif ($vhc_berakhir->equalTo($today)) {
            return "Hari ini adalah tanggal expired.";
        } elseif ($vhc_berakhir->lessThan($today)) { 
            return "EXPIRED " . $vhc_berakhir->toDateString();
        } else {
            return $vhc_berakhir->toDateString(); 
        }
        
    }
}
