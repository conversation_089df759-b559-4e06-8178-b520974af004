<?php

namespace App\Jobs\Upload;

use App\Services\Settings\Process;
use App\Services\Upload\BatchUploadZkbioService;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Intervention\Image\Facades\Image;
use Illuminate\Queue\SerializesModels;
use App\Models\Settings\Setting;
use Illuminate\Support\Facades\Storage;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Str;
use App\Models\User;
use App\Models\Settings\Organization;
use App\Models\Settings\Nvr;
use Illuminate\Support\Facades\DB;
use Modules\ExternalApi\app\Services\ProcessUpdateExternalApiService;
use Modules\ExternalApi\app\Services\ProcessMutationPersonService;
use Modules\ExternalApi\app\Jobs\ProcessUploadPersonModule;
use Modules\ExternalApi\app\Jobs\ProcessDeletePersonZkbioModule;
use App\Jobs\Service\TransformDataMutasiService;
use Modules\ExternalApi\app\Services\AccessLevelService;
use Illuminate\Database\Eloquent\Collection;


class ProcessMutasiPerson implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use Batchable;

    // public $row;
    public $dataMutasi;
    public $application;
    /**
     * Create a new job instance.
     */
    public function __construct($dataMutasi, $application)
    {
        
        $this->dataMutasi = $dataMutasi;
        $this->application = $application;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $nvr = Nvr::where("status", "=", 1)->get();
        $dataMutasi = $this->dataMutasi;
        $application = $this->application;
        $accessType = 'General';
        /* Loop the data */
        /* Run the Api External Services */
        if(count($dataMutasi) >= 1){
            foreach($dataMutasi as $item){
                $transform = new TransformDataMutasiService();
                $dataMutasiZkbio = $transform->transform('Zkbio', $item, $application, $accessType);
                Log::info('Data Upload Mutasi ' . now(), [
                    'transform' => $transform,
                ]);
                $dataMutasiHuawei = $transform->transform('Huawei', $item, $application, $accessType);
                
                $service = new ProcessUpdateExternalApiService();
                $batch1 = $service->processQueueZkbio($dataMutasiZkbio, $application);
                $service2 = new ProcessMutationPersonService();
                $batch2 = $service2->processQueueHuawei($dataMutasiHuawei, $application);
            }
        }
    }
}
