<?php

namespace App\Http\Controllers\Settings;

use App\Http\Controllers\Controller;
use App\Models\Settings\Permission;
use App\Models\View\ListPermission;
use App\Services\PermissionService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class PermissionController extends Controller
{
    public $service;

    /**
     * MasterPermissionController constructor.
     */
    public function __construct()
    {
        $this->service = new PermissionService();
        // $this->middleware(['direct_permission:Permissions-index'])->only(['index', 'show']);
        // $this->middleware(['direct_permission:Permissions-store'])->only('store');
        // $this->middleware(['direct_permission:Permissions-edits'])->only('update');
        // $this->middleware(['direct_permission:Permissions-erase'])->only('destroy');
    }
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $rows = (isset($request->page)) ? $request->page : 20;
        $sorts = (isset($request->sortField)) ? $request->sortField : "order_line";
        $order = (isset($request->sortOrder)) ? 'desc' : 'asc';
        $search = isset($request->filters) ? (string)$request->filters : "";
        // $filters = (isset($options['filters'])) ? $options['filters']['global']['value'] : '';


        $all_data = ListPermission::orderBy($sorts, $order)
            ->get();
        // ->paginate($rows);

        $all_data = [
            'data' => $all_data,
            'total' => count($all_data)
        ];

        $parents = Permission::where('has_child', 'Y')
            ->select('id as value', 'menu_name as label')
            ->get();
        $dataParent = [];
        foreach ($parents as $key => $item) {
            $dataParent[] = [
                'value' => (int)$item->value,
                'label' => $item->label,
            ];
        }

        $collect = collect($all_data);

        $result = [
            'parent' => $dataParent,
            'header' => $this->service->frontEndHeader()
        ];
        return $this->success('Success', $collect->merge($result)->all());
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        if ($this->validation($request)) {
            return $this->error($this->validation($request));
        }

        $form = $request->all();
        DB::beginTransaction();
        try {
            $this->service->processPermission($form);

            DB::commit();

            return $this->success('Data inserted!', [
                'errors' => false,
            ]);
        } catch (\Exception $exception) {
            DB::rollBack();

            return $this->error($exception->getMessage(), [
                "Trace" => $exception->getTrace()
            ]);
        }
    }

    /**
     * @param $request
     *
     * @return false|string
     */
    protected function validation($request): bool|string
    {
        $messages = [
            'menu_name' => 'Menu Name is required!',
            'order_line' => 'Order line field is required!',
            'role' => 'Role field is required!',
        ];

        $validator = Validator::make($request->all(), [
            'menu_name' => 'required',
            'order_line' => 'required',
            'role' => 'required|array',
        ], $messages);

        $string_data = "";
        if ($validator->fails()) {
            foreach (collect($validator->messages()) as $error) {
                foreach ($error as $items) {
                    $string_data .= $items . " \n  ";
                }
            }
            return $string_data;
        } else {
            return false;
        }
    }

    /**
     * Display the specified resource.
     *
     * @param Request $request
     * @param $id
     *
     * @return JsonResponse
     */
    public function show(Request $request,  $id): JsonResponse
    {
        $menu_name = $request->menu_name;
        $data = DB::select("EXEC sp_single_permission '$id' ");

        if ($data) {
            $arr_permission_role = [];
            $arr_role_name = [];
            $rolesPermission = Permission::leftJoin('role_has_permissions', 'role_has_permissions.permission_id', 'permissions.id')
                ->leftJoin('roles', 'roles.id', 'role_has_permissions.role_id')
                ->where('permissions.menu_name', $data[0]->menu_name)
                ->distinct()
                ->select('role_has_permissions.role_id AS role', 'roles.name AS role_name')
                ->get();
            foreach ($rolesPermission as $roles) {
                $arr_permission_role[] = (int)$roles->role;
                $arr_role_name[] = $roles->role_name;
            }

            $data[0]->role_name = implode(', ', $arr_role_name);
            $data[0]->role = $arr_permission_role;
            return $this->success('Success', [
                'data' => $data[0]
            ]);
        }
        return $this->success('No data found!', [
            'data' => []
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     *
     * @return JsonResponse
     */
    public function update(Request $request, $id): JsonResponse
    {
        if ($this->validation($request)) {
            return $this->error($this->validation($request), []);
        }

        $form = $request->all();
        DB::beginTransaction();
        try {
            $this->service->processPermission($form);

            DB::commit();

            return $this->success('Data updated!', []);
        } catch (\Exception $exception) {
            DB::rollBack();

            return $this->error($exception->getMessage(), [
                "Trace" => $exception->getTrace()
            ]);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param Request $request
     * @param int $id
     *
     * @return JsonResponse
     * @throws \Exception
     */
    public function destroy(Request $request, $id): JsonResponse
    {
        DB::beginTransaction();
        try {
            $collect = collect($request->selected);
            $selected = $collect->pluck('menu_name');


            $prefix = ['index', 'store', 'erase', 'delete'];

            foreach ($selected->all() as $value) {
                foreach ($prefix as $item) {
                    $permission = Permission::where('name', $value . '-' . $item)->first();
                    if ($permission) {
                        $this->service->removeRolePermission($permission->id);
                        $this->service->removeUserPermission($permission->id);
                    }
                }
            }

            $details = Permission::whereIn('menu_name', $selected->all())->delete();

            DB::commit();

            return $this->success('Row deleted!', [
                'errors' => false,
            ]);
        } catch (\Exception $exception) {
            DB::rollBack();
            throw new \Exception($exception->getMessage(), 1);
        }
    }
}
