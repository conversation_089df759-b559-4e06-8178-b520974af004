// Types for Visitor Report API responses

export interface VisitorReportData {
    [key: string]: any; // Dynamic columns from pivot table stored procedure
}

export interface VisitorReportMeta {
    total_records: number;
    date_range: {
        start_date: string;
        end_date: string;
    };
}

export interface VisitorReportResponse {
    status: boolean;
    message: string;
    data: VisitorReportData[];
    meta: VisitorReportMeta;
}

export interface VisitorReportSummaryResponse {
    status: boolean;
    message: string;
    summary: {
        total_visitors: number;
        date_range: {
            start_date: string;
            end_date: string;
        };
        generated_at: string;
    };
}

export interface VisitorReportExportResponse {
    status: boolean;
    message: string;
    data: VisitorReportData[] | string; // Array for JSON, string for CSV
    format: 'json' | 'csv' | 'array';
}

// Request types
export interface VisitorReportRequest {
    start_date: string; // Format: Y-m-d
    end_date: string;   // Format: Y-m-d
}

export interface VisitorReportExportRequest extends VisitorReportRequest {
    format?: 'json' | 'csv' | 'array';
}

// Frontend component types
export interface DateRange {
    startDate: Date | null;
    endDate: Date | null;
}

export interface TableColumn {
    key: string;
    label: string;
    sortable?: boolean;
    width?: number;
}

// Error types
export interface VisitorReportError {
    message: string;
    code?: string;
    details?: any;
}
