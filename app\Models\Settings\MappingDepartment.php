<?php

namespace App\Models\Settings;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Settings\MappingDepartment
 *
 * @property int $id
 * @property string|null $department
 * @property string $department_code
 * @property string|null $face_list_name
 * @property string $face_list_id
 * @property string $acc_level
 * @property string $description
 * @property int $status
 * @property int|null $created_by
 * @property int|null $updated_by
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int|null $nvr_id
 * @property string|null $name
 * @property string|null $flag
 * @property string|null $acc_level_code
 * @method static \Illuminate\Database\Eloquent\Builder|MappingDepartment newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MappingDepartment newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MappingDepartment query()
 * @method static \Illuminate\Database\Eloquent\Builder|MappingDepartment whereAccLevel($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MappingDepartment whereAccLevelCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MappingDepartment whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MappingDepartment whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MappingDepartment whereDepartment($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MappingDepartment whereDepartmentCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MappingDepartment whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MappingDepartment whereFaceListId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MappingDepartment whereFaceListName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MappingDepartment whereFlag($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MappingDepartment whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MappingDepartment whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MappingDepartment whereNvrId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MappingDepartment whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MappingDepartment whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MappingDepartment whereUpdatedBy($value)
 * @mixin \Eloquent
 */
class MappingDepartment extends Model
{
    use HasFactory;
    protected $guarded = [];
    protected $table   = 'm_mapping_dept';
}
