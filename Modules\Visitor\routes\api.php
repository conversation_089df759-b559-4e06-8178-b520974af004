<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Modules\Visitor\App\Http\Controllers\VisitorController;


/*
    |--------------------------------------------------------------------------
    | API Routes
    |--------------------------------------------------------------------------
    |
    | Here is where you can register API routes for your application. These
    | routes are loaded by the RouteServiceProvider within a group which
    | is assigned the "api" middleware group. Enjoy building your API!
    |
*/

Route::group(['middleware' => ['external.app'], 'prefix' => 'v1'], function () {
    Route::get('visitor', [VisitorController::class, 'show']);
    Route::post('visitor', [VisitorController::class, 'store']);
    Route::put('visitor', [VisitorController::class, 'update']);
    Route::delete('visitor', [VisitorController::class, 'destroy']);
});
